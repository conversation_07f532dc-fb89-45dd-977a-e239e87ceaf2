{"main": {"id": "4df88f963a5f4d23", "type": "split", "children": [{"id": "58d312cd50085a13", "type": "tabs", "children": [{"id": "73c93796c298e7f4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "catalogue-aggregator-service/docs/cms-data-sourcing-analysis.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "cms-data-sourcing-analysis"}}]}], "direction": "vertical"}, "left": {"id": "d2bc374e99869a37", "type": "split", "children": [{"id": "2711f89d601337c5", "type": "tabs", "children": [{"id": "a95de2d9476f6832", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "1391edb8a9a759b2", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "3117cfbd8a883d5a", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 504.5}, "right": {"id": "2d3434b4fcfddbf4", "type": "split", "children": [{"id": "642ee4fe37f321f8", "type": "tabs", "children": [{"id": "95ea1f367f636896", "type": "leaf", "state": {"type": "backlink", "state": {"file": "catalogue-aggregator-service/docs/cms-data-sourcing-analysis.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for cms-data-sourcing-analysis"}}, {"id": "d5d52f5f789a29db", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "catalogue-aggregator-service/docs/cms-data-sourcing-analysis.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from cms-data-sourcing-analysis"}}, {"id": "c1ca928d086b6247", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "e9a6adb3a1c16be6", "type": "leaf", "state": {"type": "outline", "state": {"file": "catalogue-aggregator-service/docs/cms-data-sourcing-analysis.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of cms-data-sourcing-analysis"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "73c93796c298e7f4", "lastOpenFiles": ["cms-types-workflow-improvements.md", "TYPESENSE_ARCHITECTURE.md", "thirdPartyEvent-revised-implementation-plan.md", "thirdPartyEvent-PR-descriptions.md", "thirdPartyEvent-exact-changes.md", "catalogue-sites/node_modules/handlebars/release-notes.md", "catalogue-aggregator-service/docs/lifecycle-implementation/02-design/us-tours-umbraco-implementation-plan.md", "Untitled.md", "catalogue-aggregator-service/docs/lifecycle-implementation/02-design/lifecycle-presentation-for-dummies.md", "catalogue-aggregator-service/docs/lifecycle-implementation/02-design/FINAL-lifecycle-implementation-summary.md", "catalogue-aggregator-service/docs/lifecycle-implementation/03-tickets/TICKET-lifecycle-refactor.md", "catalogue-aggregator-service/docs/lifecycle-implementation/05-reference/architecture-diagrams.md", "catalogue-aggregator-service/docs/lifecycle-implementation/JIRA-TICKETS-UPDATED.md", "catalogue-aggregator-service/docs/lifecycle-implementation/05-reference/quick-reference.md", "catalogue-aggregator-service/docs/lifecycle-implementation/JIRA-TICKETS-SPLIT-REFACTOR.md", "catalogue-aggregator-service/docs/lifecycle-implementation/JIRA-TICKETS-REFACTOR-FIRST.md", "catalogue-aggregator-service/docs/lifecycle-implementation/IMPLEMENTATION-CHECKLIST.md", "catalogue-aggregator-service/docs/diagrams.md", "catalogue-aggregator-service/docs/playwright-tests.md", "catalogue-aggregator-service/docs/lifecycle-implementation/README.md", "cas-status-scheduling-analysis.md"]}