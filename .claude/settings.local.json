{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(npx prettier:*)", "Bash(pnpm run format:check:*)", "Bash(npm run lint)", "Bash(node:*)", "Bash(AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test aws --endpoint-url=http://localhost:4566 --region eu-west-2 logs describe-log-streams --log-group-name \"/aws/lambda/cms-production--development\" --order-by LastEventTime --descending --max-items 1 --query \"logStreams[0].logStreamName\" --output text)", "Read(//Users/<USER>/**)", "Bash(npm run local:deploy:*)", "Bash(DOCKER_CONFIG=/dev/null pnpm jest test/integration/tour-processing.integration.test.ts --config jest.integration.config.js)", "Bash(AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test aws --endpoint-url=http://localhost:4566 --region eu-west-2 logs filter-log-events --log-group-name \"/aws/lambda/cms-tour--development\" --filter-pattern \"mapUmbracoBodyOld\" --start-time 1756743000000 --query \"events[0].message\" --output text)", "Bash(AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test aws --endpoint-url=http://localhost:4566 --region eu-west-2 logs filter-log-events --log-group-name \"/aws/lambda/cms-tour--development\" --start-time 1725292000000)", "Bash(pnpm test:*)", "Bash(npm test:*)", "Bash(npm run lint:*)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(git log:*)", "Bash(git ls-tree:*)", "<PERSON><PERSON>(podman ps:*)", "Bash(npm run test:integration:*)", "Bash(npm run typecheck:*)", "Bash(git add:*)", "Bash(npx lint-staged:*)", "Bash(npm run test:*)", "Bash(npx semantic-release:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "Bash(SLACK_ALERTS_WEBHOOK=https://hooks.slack.com/test SLACK_INFO_WEBHOOK=https://hooks.slack.com/test SLACK_PM_FIX_WEBHOOK=https://hooks.slack.com/test npm test)", "Bash(SLACK_ALERTS_WEBHOOK=https://hooks.slack.com/test SLACK_INFO_WEBHOOK=https://hooks.slack.com/test SLACK_PM_FIX_WEBHOOK=https://hooks.slack.com/test npm test src/handlers/umbraco/backfill/publish-content.test.ts src/handlers/routes/list-productions/index.test.ts)"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/Documents/cas-interactive-docs"]}, "outputStyle": "Explanatory"}