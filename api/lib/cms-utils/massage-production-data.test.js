const t = require('tap')

const { massageProductionData } = require('./')

t.test(
  "cms show data massager - don't break on missing actors data",
  async ({ strictSame }) => {
    const cmsResponse = {
      casting: [
        { actorSchedule: 'lorem ispum', characterName: 'dolor sit amet' },
      ],
      title: { seoDescription: 'SEO description for meta tag' },
      venue: {
        venue: { addressCoordinates: { lat: 0, lon: 0 } },
      },
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    const expectedCredits = [
      {
        avatarUrl: '',
        actorName: '',
        actorSchedule: 'lorem ispum',
        characterName: 'dolor sit amet',
      },
    ]

    strictSame(
      massaged.credits,
      expectedCredits,
      'Should have empty avatarUrl and actorName'
    )
    strictSame(
      massaged.seoDescription,
      'SEO description for meta tag',
      'Should copy seoDescription field'
    )
  }
)

t.test(
  'cms show data massager - remove upsell if skipUpsell is true',
  async ({ strictSame }) => {
    const cmsResponse = {
      casting: [
        { actorSchedule: 'lorem ispum', characterName: 'dolor sit amet' },
      ],
      title: { seoDescription: 'SEO description for meta tag' },
      venue: {
        venue: { addressCoordinates: { lat: 0, lon: 0 } },
      },
      upsells: [
        {
          type: 'standard',
          price: 5,
          title: 'Glass of Prosecco',
          info: 'Get your evening off to the perfect start with a delicious glass of fruity & fragrant Vitelli Prosecco',
          moreInfo:
            '<p>You’ll receive a voucher with your ticket which you can redeem at any bar.</p>',
          imageUrl: 'https://example.com/example.jpg',
          unitType: 'per glass',
        },
      ],
      skipUpsell: true,
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    strictSame(massaged.upsells, {}, 'upsells for production should be empty')
  }
)
