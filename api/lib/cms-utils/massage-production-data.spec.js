const DateFormatter = require('../dateFormatter')

const { massageProductionData } = require('./')

const dateFormatter = new DateFormatter()

describe('cms show data massager', () => {
  const production = {
    casting: [],
    nightsNotToMiss: [],
    title: {
      seoDescription: '',
      headerAssets: [],
      mobileAssets: [],
      bodyAssets: [],
      topLevelTitle: {
        quotes: [],
        reviews: [],
        genreList: [],
        ageGuidance: '',
        trailerUrl: '',
        statsAndFacts: [],
      },
      imageOverrideVertical: '',
      imageOverrideHorizontal: '',
    },
    venue: {
      venue: { addressCoordinates: {} },
      avVenueId: '',
      screenIDs: '',
    },
    browserTitle: '',
    priceMinimum: 0,
    priceTransactionFee: 0,
    salePeriod: '',
    showTimes: [],
    doorsTimes: '',
    accessDateAudioDesc: '',
    accessDateCaptioned: '',
    accessDateRelaxed: '',
    accessDateSigned: '',
    accessibilityDates: [],
    tourUrl: '',
    soldOut: '',
    externalBuyTicketsLink: '',
    performanceMode: '',
    upsells: [],
    manualPromoCodes: false,
  }

  it("don't break on missing actors data", async () => {
    const cmsResponse = {
      ...production,
      casting: [
        { actorSchedule: 'lorem ispum', characterName: 'dolor sit amet' },
      ],
      title: { seoDescription: 'SEO description for meta tag' },
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    const expectedCredits = [
      {
        avatarUrl: '',
        actorName: '',
        actorSchedule: 'lorem ispum',
        characterName: 'dolor sit amet',
      },
    ]

    expect(massaged.credits).toStrictEqual(expectedCredits)

    expect(massaged.seoDescription).toBe('SEO description for meta tag')
  })

  it('remove upsell if skipUpsell is true', async () => {
    const cmsResponse = {
      ...production,
      upsells: [
        {
          type: 'standard',
          price: 5,
          title: 'Glass of Prosecco',
          info: 'Get your evening off to the perfect start with a delicious glass of fruity & fragrant Vitelli Prosecco',
          moreInfo:
            '<p>You’ll receive a voucher with your ticket which you can redeem at any bar.</p>',
          imageUrl: 'https://example.com/example.jpg',
          unitType: 'per glass',
        },
      ],
      skipUpsell: true,
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    expect(massaged.upsells).toStrictEqual({})
  })

  it('should return correct title', async () => {
    const title = {
      id: '75sGD7TDnIISUD2NmieGCo',
      seoDescription: 'SEO description for meta tag',
      topLevelTitle: {
        title: 'Moulin Rouge! The Musical',
        quotes: [
          {
            quoteAuthor: 'The Telegraph / Metro / Broadway World',
            content: 'Spectacular!',
          },
          {
            quoteAuthor: 'Mail on Sunday',
            content:
              'Sheer high-octane energy! If we have an energy crisis, just plug this lot into the national grid!',
          },
        ],
        reviews: [],
        genreList: [],
        ageGuidance: '',
        trailerUrl: '',
        statsAndFacts: [],
        whySeeIt: 'After winning critical acclaim on Broadway ...',
        layoutFallbackHeader: false,
      },
    }

    const titleSlug = 'titleSlug'
    const cmsResponse = { ...production, title }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug,
      venueSlug: 'venueSlug',
    })

    expect(massaged.titleSlug).toBe(titleSlug)
    expect(massaged.titleDetailsId).toBe(title.id)
    expect(massaged.why).toBe(title.whySeeIt)
    expect(massaged.fallbackHeader).toBe(title.layoutFallbackHeader)
    expect(massaged.quotes).toStrictEqual(
      title.topLevelTitle.quotes.map((quote) => ({
        content: quote.content || '',
        by: quote.quoteAuthor || '',
      }))
    )
  })

  it('should return correct venue', () => {
    const venueSlug = 'venueSlug'

    const venueInfo = {
      venueName: 'Piccadilly Theatre',
      address: '16 Denman Street  London  W1D 7DY',
      boxOfficeNotes:
        'Access to three of the four entrance doors to the box office are ramped',
      boxOfficeHours: 'Closed',
      closestTubeStation: 'Piccadilly Circus or Train to Charing Cross Station',
      isThirdPartyVenue: false,
      name: 'Piccadilly Theatre',
      venueRegion: 'London West End',
      stationType: 'tube',
      addressCoordinates: { lat: 55.5, lon: 77.7 },
      accessBookingLine: '',
      accessTypetalkNumber: '',
      accessText: '',
    }

    const venue = {
      avVenueId: 'FCA47275-3028-49A7-B1A0-E5CCF7834332',
      venue: venueInfo,
      screenIDs: [
        '0EAA099B-0DBA-4E8B-BAD3-F37188AFCD37',
        'DCE9C7F0-3CDA-4F9A-82D6-2746DE82D050',
        '4DE72D7C-3E82-48B6-90C0-0DFEE77A6F5C',
      ],
      accessBookingLine: '',
      accessTypetalkNumber: '',
      accessText: '',
      hideMembershipPromotion: true,
    }

    const cmsResponse = { ...production, venue }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug,
    })

    expect(massaged.venueSlug).toBe(venueSlug)
    expect(massaged.venueId).toBe(venue.avVenueId)
    expect(massaged.screenIds).toBe(venue.screenIDs)
    expect(massaged.venue).toStrictEqual({ ...venue })

    expect(massaged.venueInfo).toStrictEqual({
      name: venueInfo.venueName,
      address: venueInfo.address,
      tube: venueInfo.closestTubeStation,
      boxOfficeTimes: venueInfo.boxOfficeHours,
      boxOfficeNotes: venueInfo.boxOfficeNotes,
      closestStation: venueInfo.closestTubeStation,
      stationType: venueInfo.stationType,
      region: venueInfo.venueRegion,
      isThirdPartyVenue: Boolean(production.venue.isThirdPartyVenue),
      hideMembershipPromotion: venue.hideMembershipPromotion,
    })

    expect(massaged.venueDetails).toStrictEqual({
      aboutDetails: {
        gallery: {
          images:
            venue.assets &&
            venue.assets.map((asset) => ({
              big: asset.url_1280x720,
              medium: asset.url_413x232,
              description: asset.description,
              alt: asset.altDescription,
            })),
        },
        amenities: venue.amenities,
        aboutRawHTML: venue.about,
      },
      mapDetails: {
        mapLocation: {
          mapSnapshotUrl: venue.mapSnapshot,
          googleMapsUrl: venue.googleMapsUrl,
        },
        location: {
          lat: venueInfo.addressCoordinates.lat,
          lng: venueInfo.addressCoordinates.lon,
        },
        transportOptions: {
          parking: venue.transportParking,
          train: venue.transportTrain,
          bus: venue.transportBus,
        },
      },
      accessDetails: {
        accessOptions: {
          audio: cmsResponse.accessDateAudioDesc && {
            text: 'Audio Description',
            date: dateFormatter.fullLongHumanDate(
              cmsResponse.accessDateAudioDesc
            ),
          },
          captioned: cmsResponse.accessDateCaptioned && {
            text: 'Captioned',
            date: dateFormatter.fullLongHumanDate(
              cmsResponse.accessDateCaptioned
            ),
          },
          relaxed: cmsResponse.accessDateRelaxed && {
            text: 'Relaxed',
            date: dateFormatter.fullLongHumanDate(
              cmsResponse.accessDateRelaxed
            ),
          },
          signed: cmsResponse.accessDateSigned && {
            text: 'Sign language interpreted',
            date: dateFormatter.fullLongHumanDate(cmsResponse.accessDateSigned),
          },
        },
        accessibilityDates: cmsResponse.accessibilityDates
          .filter(
            (accessibilityDate) =>
              accessibilityDate.accessType &&
              accessibilityDate.name &&
              accessibilityDate.date
          )
          .map(({ accessType, name, date }) => ({
            accessType,
            name,
            date: dateFormatter.fullLongHumanDate(date),
          })),
        accessBookingLine: venue.accessBookingLine,
        accessTypetalkNumber: venue.accessTypetalkNumber,
        accessRawHTML: venue.accessText,
      },
    })
  })

  it('should return correct assets', () => {
    const assets = [
      {
        assetCaption: '',
        assetDescription: 'Moulin Rouge! The Musical Title Shot',
        assetUrl_1200W: 'https://res.cloudinary.com/image/2560x1440.jpg',
        assetType: 'image',
      },
    ]

    const cmsResponse = {
      ...production,
      title: { headerAssets: assets, bodyAssets: assets, mobileAssets: assets },
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    expect(massaged.headerAssets).toStrictEqual(
      assets.map((asset) => ({
        type: asset.assetType,
        src: asset.assetUrl_1200W,
        description: asset.assetDescription,
        caption: asset.assetCaption,
        alt: asset.altDescription,
      }))
    )

    expect(massaged.bodyAssets).toStrictEqual(
      assets.map((asset) => ({
        type: asset.assetType,
        src: asset.assetUrl_1200W,
        description: asset.assetDescription,
        caption: asset.assetCaption,
        alt: asset.altDescription,
      }))
    )

    expect(massaged.mobileHeaderAssets).toStrictEqual(
      assets.map((asset) => ({
        type: asset.assetType,
        src: asset.assetUrl_1200W,
        description: asset.assetDescription,
        caption: asset.assetCaption,
        alt: asset.altDescription,
      }))
    )
  })

  it('should return correct reviews', () => {
    const reviews = [
      {
        content:
          'Sheer high-octane energy! If we have an energy crisis, just plug this lot into the national grid!',
        quoteAuthor: 'Mail on Sunday',
        quoteScore: 3,
      },
      {
        quoteAuthor: 'The Telegraph / Metro / Broadway World',
        content: 'Spectacular!',
      },
    ]

    const cmsResponse = {
      ...production,
      title: { topLevelTitle: { reviews } },
    }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    expect(massaged.reviews).toStrictEqual(
      reviews.map((review) => ({
        content: review.content || '',
        by: review.quoteAuthor || '',
        score: review.quoteScore || 0,
      }))
    )
  })

  it('should return correct nights', () => {
    const nightsNotToMiss = [
      { nightDate: 'date' },
      {
        nightDate: 'date',
        nightTitle: 'title',
        nightDescription: 'description',
      },
    ]

    const cmsResponse = { ...production, nightsNotToMiss }
    const massaged = massageProductionData(cmsResponse, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    expect(massaged.nights).toStrictEqual(
      nightsNotToMiss.map((night, id) => ({
        id,
        date: night.nightDate || '',
        title: night.nightTitle || '',
        description: night.nightDescription || '',
      }))
    )
  })

  it('check if the value of the layout and key is returned correctly', () => {
    const titleSlug = 'titleSlug'
    const venueSlug = 'venueSlug'

    const massaged = massageProductionData(production, {
      titleSlug: 'titleSlug',
      venueSlug: 'venueSlug',
    })

    expect(massaged.layout).toBe('default')
    expect(massaged.key).toStrictEqual([titleSlug, venueSlug].join('-'))
  })
})
