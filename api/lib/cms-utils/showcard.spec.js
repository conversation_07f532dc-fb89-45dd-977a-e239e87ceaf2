const { populateShowCards, populateShowCard } = require('./showcard')

const offsetDate = (days) => new Date(Date.now() + 60000 * 60 * 24 * days)
const cmsPast = offsetDate(-2).toISOString()
const cmsPast2 = offsetDate(-3).toISOString()

describe('populateShowCards', () => {
  it('applies isTour', async () => {
    const showCards = [
      {
        title: {
          title: 'Come from away',
          titleSlug: 'come-from-away',
        },
        link: '',
        production: [0, 1],
        tourName: 'Moop',
        isTour: true,
        headerAsset: {},
        bodyAsset: {},
        externalBuyTicketsLink: '',
      },
    ]
    const venueInfo = {
      title: {
        title: 'Come from away',
        titleSlug: 'come-from-away',
        topLevelTitle: {
          genreList: ['Musical'],
        },
      },
      venue: {
        venueSlug: 'phoenix-theatre',
        venue: { venueName: 'Phoenix' },
      },
    }

    const toursByTitleSlug = {
      'come-from-away': {
        'phoenix-theatre': venueInfo,
        'other-theatre': venueInfo,
      },
    }
    const result = populateShowCards(
      showCards,
      undefined,
      false,
      toursByTitleSlug
    )

    expect(result[0].isTour).toBeTruthy()
  })

  it('handles unknown genre', async () => {
    const showCards = [
      {
        title: {
          title: 'Come from away',
          titleSlug: 'come-from-away',
        },
        link: '',
        production: [0, 1],
        tourName: 'Moop',
        isTour: true,
        headerAsset: {},
        bodyAsset: {},
        externalBuyTicketsLink: '',
      },
    ]
    const venueInfo = {
      title: {
        title: 'Come from away',
        titleSlug: 'come-from-away',
        topLevelTitle: {},
      },
      venue: {
        venueSlug: 'phoenix-theatre',
        venue: { venueName: 'Phoenix' },
      },
      nextPerformanceDate: '2022-09-13T18:30:00.000Z',
    }

    const toursByTitleSlug = {
      'come-from-away': {
        'phoenix-theatre': venueInfo,
        'other-theatre': venueInfo,
      },
    }
    const result = populateShowCards(
      showCards,
      undefined,
      false,
      toursByTitleSlug
    )

    expect(result[0].isTour).toBeTruthy()
  })

  it('dont display embargoed tours', async () => {
    const showCards = [
      {
        name: 'Come From Away',
        titleDetails: {
          id: 'cfa-id',
          title: 'Come From Away',
          titleSlug: 'come-from-away',
        },
      },
      {
        isTour: true,
        tourName: 'The Book of Mormon',
        title: {
          id: 'book-id',
          title: 'The Book of Mormon',
          titleSlug: 'the-book-of-mormon',
        },
        production: [
          {
            id: '6Rzi5I2jlWazE2C8I5DskF',
            productionName: 'The Book Of Mormon at Edinburgh Playhouse',
            title: {
              id: '7B8OOjIyysuEmkMoMoiyAs',
              title: 'The Book Of Mormon',
              titleSlug: 'the-book-of-mormon',
              topLevelTitle: {
                id: '3JsjY1RnMsWCEe0IamgYo4',
                title: 'The Book of Mormon',
              },
            },
            venue: {
              id: '6snaAye9B6AsYYS6yU2MoA',
              venueConfigName: 'Edinburgh Playhouse',
              venueSlug: 'edinburgh-playhouse',
              avVenueId: '275DE3F4-858A-4C11-9106-C92C7F34EEC4',
              venue: {},
            },
          },
          {
            id: '6Rzi5I2jlWazE2C8I5DskF',
            productionName: 'The Book Of Mormon at Edinburgh Playhouse',
            title: {
              id: '7B8OOjIyysuEmkMoMoiyAs',
              title: 'The Book Of Mormon',
              titleSlug: 'the-book-of-mormon',
              topLevelTitle: {
                id: '3JsjY1RnMsWCEe0IamgYo4',
                title: 'The Book of Mormon',
              },
            },
            venue: {
              id: 'other-id',
              venueConfigName: 'Other Edinburgh Playhouse',
              venueSlug: 'other-edinburgh-playhouse',
              avVenueId: '275DE3F4-858A-4C11-9106-C92C7F34EEC4',
              venue: {},
            },
          },
        ],
      },
    ]

    const toursByTitleSlug = {
      'the-book-of-mormon': {
        'edinburgh-playhouse': {
          title: {
            title: 'The Book Of Mormon',
            titleSlug: 'the-book-of-mormon',
            topLevelTitle: {
              id: '3JsjY1RnMsWCEe0IamgYo4',
              title: 'The Book of Mormon',
              genreList: ['Musical'],
            },
          },
          venue: {
            id: '6snaAye9B6AsYYS6yU2MoA',
            venueConfigName: 'Edinburgh Playhouse',
            venueSlug: 'edinburgh-playhouse',
          },
          status: 'embargo',
        },
        'other-edinburgh-playhouse': {
          title: {
            title: 'The Book Of Mormon',
            titleSlug: 'the-book-of-mormon',
            topLevelTitle: {
              id: '3JsjY1RnMsWCEe0IamgYo4',
              title: 'The Book of Mormon',
              genreList: ['Musical'],
            },
          },
          venue: {
            id: 'other-id',
            venueConfigName: 'Other Edinburgh Playhouse',
            venueSlug: 'other-edinburgh-playhouse',
          },
          status: 'embargo',
        },
      },
    }

    const result = populateShowCards(showCards, {}, false, toursByTitleSlug)

    expect(result).toStrictEqual([
      {
        title: 'Come From Away',
        titleSlug: 'come-from-away',
        status: 'closed',
        genreList: [],
        isTour: false,
        venueCount: 0,
        venue: undefined,
        productionsByVenue: [],
        name: 'Come From Away',
        purchaseLink: undefined,
        assetUrl: undefined,
        assetCaption: 'Come From Away',
        altAssetUrl: undefined,
        altAssetCaption: 'Come From Away',
        salePeriod: 'Multiple Performances',
        accessibility: [],
      },
    ])
  })

  it('removes tour card from show cards list if no matching tour productions in toursByTitleSlug data', async () => {
    const showCards = [
      {
        title: {
          title: 'Come from away',
          titleSlug: 'come-from-away',
        },
        link: '',
        production: [0, 1],
        tourName: 'Moop',
        isTour: true,
        headerAsset: {},
        bodyAsset: {},
        externalBuyTicketsLink: '',
      },
    ]
    const toursByTitleSlug = {
      'another-tour-slug': {
        'phoenix-theatre': {},
      },
    }

    const result = populateShowCards(
      showCards,
      undefined,
      false,
      toursByTitleSlug
    )

    expect(result).toStrictEqual([])
  })

  it('populates from production when showcard is empty', async () => {
    const showCard = {}
    const production = {
      title: 'Come From Away',
      titleSlug: 'come-from-away',
      venueSlug: 'phoenix-theatre',
      city: 'London',
      venueInfo: {
        name: 'Phoenix Theatre',
      },
      priceInfo: {
        min: 10.0,
        transactionFee: 3.65,
      },
      status: 'onSale',
      onSaleStartDate: cmsPast,
      preSaleStarts: cmsPast,
      bodyAssets: [
        {
          src: 'https://picsum.photos/200',
          caption: 'Come From Away shot 1',
        },
      ],
      headerAssets: [
        {
          src: 'https://picsum.photos/400',
          caption: 'Come From Away shot 2',
        },
      ],
      genreList: ['musical'],
      overview: 'overview',
      salePeriod: 'from X to Y',
      opening: 'from X to Y',
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
      venueDetails: {
        accessDetails: {
          accessibilityDates: [
            {
              accessType: 'audio',
              name: 'Audio Description',
              date: 'Wednesday 19 Jun 2019 19:30pm',
            },
          ],
        },
      },
      externalBuyTicketsLink: '',
    }
    const isHero = false
    const isTour = false
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result).toStrictEqual({
      title: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'phoenix-theatre',
      location: 'Phoenix Theatre',
      city: 'London',
      priceMinimum: 10.0,
      priceTransactionFee: 3.65,
      status: 'onSale',
      isTour: false,
      onSaleStartDate: cmsPast,
      preSaleDate: cmsPast,
      assetUrl: 'https://picsum.photos/200',
      assetCaption: 'Come From Away shot 1',
      altAssetUrl: 'https://picsum.photos/400',
      altAssetCaption: 'Come From Away shot 2',
      productionsByVenue: [],
      genreList: ['musical'],
      overview: 'overview',
      salePeriod: 'from X to Y',
      link: '/shows/come-from-away/phoenix-theatre/',
      purchaseLink: '/shows/come-from-away/phoenix-theatre/calendar/',
      accessibility: [
        {
          accessType: 'audio',
          name: 'Audio Description',
          date: 'Wednesday 19 Jun 2019 19:30pm',
        },
      ],
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
    })
  })

  it('overrides production data when showCard is populated', async () => {
    const showCard = {
      name: 'Come From Away',
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
      titleDetails: {
        titleSlug: 'come-from-away',
      },
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      onSaleStartDate: cmsPast2,
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
    }
    const production = {
      title: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'phoenix-theatre',
      city: 'London',
      venueInfo: {
        name: 'Phoenix Theatre',
      },
      priceInfo: {
        min: 10.0,
        transactionFee: 3.65,
      },
      status: 'onSale',
      onSaleStartDate: cmsPast,
      preSaleStarts: cmsPast,
      bodyAssets: [
        {
          src: 'https://picsum.photos/200',
          caption: 'Come From Away shot 1',
        },
      ],
      headerAssets: [
        {
          src: 'https://picsum.photos/400',
          caption: 'Come From Away shot 2',
        },
      ],
      genreList: ['musical'],
      overview: 'overview',
      salePeriod: 'from X to Y',
      opening: 'from X to Y',
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
      venueDetails: {
        accessDetails: {
          accessibilityDates: [
            {
              accessType: 'audio',
              name: 'Audio Description',
              date: 'Wednesday 19 Jun 2019 19:30pm',
            },
          ],
        },
      },
      externalBuyTicketsLink: '',
    }
    const isHero = false
    const isTour = false
    const result = populateShowCard({
      showCard,
      production,
      isHero,
      isTour,
    })

    expect(result).toStrictEqual({
      title: 'Come From Away',
      name: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      city: 'London',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      status: 'onSale',
      isTour: false,
      onSaleStartDate: cmsPast2,
      preSaleDate: cmsPast,
      assetUrl: 'https://picsum.photos/201',
      assetCaption: 'Come From Away shot 1b',
      altAssetUrl: 'https://picsum.photos/401',
      altAssetCaption: 'Come From Away shot 2b',
      productionsByVenue: [],
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
      accessibility: [
        {
          accessType: 'audio',
          name: 'Audio Description',
          date: 'Wednesday 19 Jun 2019 19:30pm',
        },
      ],
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
    })
  })

  it('populates showcard with defaults when production is missing', async () => {
    const showCard = {
      name: 'Come From Away',
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
      titleDetails: {
        titleSlug: 'come-from-away',
      },
      productionsByVenue: [],
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      onSaleStartDate: cmsPast2,
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
    }

    const production = {}
    const isHero = false
    const isTour = false
    const result = populateShowCard({
      showCard,
      production,
      isHero,
      isTour,
    })

    expect(result).toStrictEqual({
      title: 'Come From Away',
      name: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      onSaleStartDate: cmsPast2,
      assetUrl: 'https://picsum.photos/201',
      assetCaption: 'Come From Away shot 1b',
      altAssetUrl: 'https://picsum.photos/401',
      altAssetCaption: 'Come From Away shot 2b',
      productionsByVenue: [],
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
      // defaults
      accessibility: [],
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
      status: 'closed',
      isTour: false,
    })
  })

  it('provides safe defaults when production is empty', async () => {
    const showCard = {
      name: 'Come From Away',
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
      titleDetails: {
        titleSlug: 'come-from-away',
      },
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      onSaleStartDate: cmsPast2,
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
    }
    const production = {}
    const isHero = false
    const isTour = false
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result.status).toBe('closed')
    expect(result.isTour).toBeFalsy()
  })

  it('if production is empty defaults to showCard link', async () => {
    const showCard = {
      name: 'Come From Away',
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      onSaleStartDate: cmsPast2,
      link: 'https://google.com',
      accessibility: [],
    }
    const production = {}
    const isHero = false
    const isTour = false
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result.link).toBe('https://google.com')
    expect(result.status).toBe('onSale')
  })

  it('provides safe defaults when both showcard.titleDetails and production are empty', async () => {
    const showCard = {
      name: 'Come From Away',
      venueSlug: 'other-theatre',
      location: 'Other Theatre',
      priceMinimum: 15.0,
      priceTransactionFee: 3.95,
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
      genreList: ['play'],
      overview: 'overview2',
      salePeriod: 'from A to B',
      onSaleStartDate: cmsPast2,
      link: 'https://google.com',
      purchaseLink: 'https://example.com',
      accessibility: [],
    }
    const production = {}
    const isHero = false
    const isTour = false
    const result = populateShowCard({
      showCard,
      production,
      isHero,
      isTour,
    })

    expect(result.status).toBe('onSale')
    expect(result.isTour).toBeFalsy()
  })

  it('uses header asset primarly when is hero (showcard)', async () => {
    const showCard = {
      bodyAsset: {
        assetUrl_1200W: 'https://picsum.photos/201',
        assetCaption: 'Come From Away shot 1b',
      },
      headerAsset: {
        assetUrl_1200W: 'https://picsum.photos/401',
        assetCaption: 'Come From Away shot 2b',
      },
    }
    const production = {}
    const isHero = true
    const isTour = false
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result.assetUrl).toBe('https://picsum.photos/401')
    expect(result.assetCaption).toBe('Come From Away shot 2b')
  })

  it('uses header asset primarly when is hero (production)', async () => {
    const showCard = {}
    const production = {
      bodyAssets: [
        {
          src: 'https://picsum.photos/200',
          caption: 'Come From Away shot 1',
        },
      ],
      headerAssets: [
        {
          src: 'https://picsum.photos/400',
          caption: 'Come From Away shot 2',
        },
      ],
    }
    const isHero = true
    const isTour = false
    const result = populateShowCard({
      showCard,
      production,
      isHero,
      isTour,
    })

    expect(result.assetUrl).toBe('https://picsum.photos/400')
    expect(result.assetCaption).toBe('Come From Away shot 2')
  })

  it('externalBuyTicketsLink exists in production', async () => {
    const showCard = {
      link: 'https://www.atgtickets.com/shows/the-lion-king-london/lyceum-theatre/',
    }
    const production = {
      externalBuyTicketsLink:
        'https://thelyceumtheatre.nliven.co/tickets/series/lionking',
    }
    const isHero = false
    const isTour = false
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result.purchaseLink).toBe(
      'https://thelyceumtheatre.nliven.co/tickets/series/lionking'
    )
  })

  it('drops detail from tours', async () => {
    const showCard = {}
    const production = {
      title: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'phoenix-theatre',
      venueInfo: {
        name: 'Phoenix Theatre',
      },
      priceInfo: {
        min: 10.0,
        transactionFee: 3.65,
      },
      status: 'soldOut',
      onSaleStartDate: cmsPast,
      bodyAssets: [
        {
          src: 'https://picsum.photos/200',
          caption: 'Come From Away shot 1',
        },
      ],
      headerAssets: [
        {
          src: 'https://picsum.photos/400',
          caption: 'Come From Away shot 2',
        },
      ],
      genreList: ['Musical'],
      overview: 'overview',
      salePeriod: 'from X to Y',
      tourUrl: 'https://www.atgtickets.com/shows/come-from-away/',
      opening: 'from X to Y',
      venueDetails: {
        accessDetails: {
          accessibilityDates: [
            {
              accessType: 'audio',
              name: 'Audio Description',
              date: 'Wednesday 19 Jun 2019 19:30pm',
            },
          ],
        },
      },
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
    }
    const isHero = false
    const isTour = true
    const result = populateShowCard({ showCard, production, isHero, isTour })

    expect(result).toStrictEqual({
      title: 'Come From Away',
      titleSlug: 'come-from-away',
      venueCount: 1,
      venueSlug: 'phoenix-theatre',
      status: 'soldOut',
      isTour: true,
      assetUrl: 'https://picsum.photos/200',
      assetCaption: 'Come From Away shot 1',
      altAssetUrl: 'https://picsum.photos/400',
      altAssetCaption: 'Come From Away shot 2',
      productionsByVenue: [],
      genreList: ['Musical'],
      overview: 'overview',
      salePeriod: 'from X to Y',
      link: 'https://www.atgtickets.com/shows/come-from-away/',
      purchaseLink: 'https://www.atgtickets.com/shows/come-from-away/',
      accessibility: [
        {
          accessType: 'audio',
          name: 'Audio Description',
          date: 'Wednesday 19 Jun 2019 19:30pm',
        },
      ],
      venue: {
        id: '3XTEg6ooTeYsg2os6uIUcu',
        venueConfigName: 'Phoenix Theatre',
      },
    })
  })
})
