const _ = require('lodash')

const DateFormatter = require('../../lib/dateFormatter')
const { GENERIC_ASSET_URL } = require('../../lib/utils')
const { genreObjectToList } = require('../../lib/utils')

const dateFormatter = new DateFormatter()
const { dateUtc } = dateFormatter

const byfirstPerformanceDateAsc = (a, b) =>
  dateUtc(a.firstPerformanceDate) - dateUtc(b.firstPerformanceDate)

function transformProduction(production) {
  const transformed = {
    name: production.title,
    title: production.title,
    venueName:
      (production.venueInfo && production.venueInfo.name) ||
      production.venueName,
    id: production.id,
    key: production.key,
    titleSlug: production.titleSlug,
    venueSlug: production.venueSlug,
    firstPerformanceDate: production.firstPerformanceDate,
    tourUrl: production.tourUrl,
    onSaleEndDate: production.onSaleEndDate,
    opening: production.opening,
    nameOfShow: production.venueInfo ? production.venueInfo.name : '',
    location: production.venueInfo ? production.venueInfo.name : '',
    link: production.tourUrl,
  }
  return transformed
}

const transformTourProduction = ({
  title: {
    titleSlug,
    topLevelTitle: { genreList = [] },
  },
  venue: {
    venueSlug,
    venue: { venueName },
  },
  firstPerformanceDate,
  salePeriod,
}) => ({
  venueName,
  titleSlug,
  venueSlug,
  firstPerformanceDate,
  genreList: genreObjectToList(genreList),
  opening: salePeriod,
})

function populateShowCard({
  showCard,
  production = {},
  isHero = false,
  isTour = false,
  venueCount = 1,
  productionsByVenue = [],
}) {
  const titleSlug =
    (showCard.isTour && showCard.title && showCard.title.titleSlug) ||
    (showCard.titleDetails && showCard.titleDetails.titleSlug) ||
    production.titleSlug

  const genreListObject =
    _.get(showCard, 'title.topLevelTitle.genreList') ||
    _.get(showCard, 'titleDetails.topLevelTitle.genreList')

  // Destructuring will also apply ShowCard overrides
  const merged = {
    title: showCard.name || production.title,
    titleSlug,
    status: !showCard.titleDetails ? 'onSale' : 'closed',
    genreList: genreObjectToList(genreListObject),
    isTour,
    venueCount,
    venue: showCard.venue || production.venue,
    productionsByVenue,
    ...extractCardData(production, isTour),
    ...showCard,
  }

  if (showCard.tourName) {
    merged.title = showCard.tourName
    merged.name = showCard.tourName
    merged.status = 'onSale'
    merged.productionsByVenue = productionsByVenue // final object reaching the browser carousel
    merged.purchaseLink = showCard.link
    merged.assetUrl =
      showCard.headerAsset && showCard.headerAsset.assetUrl_1200W
        ? showCard.headerAsset.assetUrl_1200W
        : GENERIC_ASSET_URL
    merged.altAssetUrl =
      showCard.bodyAsset && showCard.bodyAsset.assetUrl_1200W
        ? showCard.bodyAsset.assetUrl_1200W
        : GENERIC_ASSET_URL
    merged.isTour = true
    merged.salePeriod = showCard.salePeriod || 'Multiple Performances'
  } else {
    merged.purchaseLink = merged.purchaseLink || merged.link
    const { bodyAsset, headerAsset } = merged
    const assetsByPlacement = isHero
      ? [headerAsset, bodyAsset]
      : [bodyAsset, headerAsset]
    const [primaryAsset = {}, secondaryAsset = {}] =
      assetsByPlacement.filter((isTruthy) => isTruthy) || {}
    const { title, location } = merged
    const fallbackCaption = location ? `${title} at ${location}` : title
    merged.assetUrl = primaryAsset.assetUrl_1200W || primaryAsset.src
    merged.assetCaption =
      primaryAsset.assetCaption || primaryAsset.caption || fallbackCaption
    merged.altAssetUrl = secondaryAsset.assetUrl_1200W || secondaryAsset.src
    merged.altAssetCaption =
      secondaryAsset.assetCaption || secondaryAsset.caption || fallbackCaption
    merged.salePeriod = merged.salePeriod || 'Multiple Performances'
  }
  if (production.venueDetails) {
    merged.accessibility =
      production.venueDetails.accessDetails.accessibilityDates
  } else {
    merged.accessibility = []
  }

  // Override with externalBuyTicketsLink /////////////////////////////////////
  const showcardProduction = showCard.production
  const externalBuyTicketsLink =
    showcardProduction && showcardProduction.externalBuyTicketsLink
  if (externalBuyTicketsLink) {
    merged.purchaseLink = externalBuyTicketsLink
  }
  /////////////////////////////////////////////////////////////////////////////

  delete merged.bodyAsset
  delete merged.headerAsset
  // delete merged.name // FIXME: ui relies on name property, but it's a duplicate of the title property

  delete merged.titleDetails

  return merged
}

function extractCardData(production, isTour) {
  if (_.isEmpty(production)) {
    return
  }

  const {
    title,
    status,
    city,
    onSaleStartDate,
    headerAssets = [],
    bodyAssets = [],
    genreList,
    overview,
    salePeriod,
    titleSlug,
    venueSlug,
    preSaleStarts,
    externalBuyTicketsLink,
  } = production

  const headerAsset = headerAssets[0]
  const bodyAsset = bodyAssets[0]
  const priceInfo = production.priceInfo || {}
  const venueInfo = production.venueInfo || {}
  const tourUrl = production.tourUrl || null

  const cardData = {
    title,
    status,
    headerAsset,
    bodyAsset,
    genreList: genreObjectToList(genreList),
    overview,
    salePeriod,
    titleSlug,
    venueSlug,
    link: isTour && tourUrl ? tourUrl : `/shows/${titleSlug}/${venueSlug}/`,
    purchaseLink:
      externalBuyTicketsLink !== ''
        ? externalBuyTicketsLink
        : isTour && tourUrl
          ? tourUrl
          : `/shows/${titleSlug}/${venueSlug}/calendar/`,
  }
  if (!isTour) {
    const details = {
      location: venueInfo.name,
      city,
      priceTransactionFee: parseFloat(priceInfo.transactionFee),
      priceMinimum: parseFloat(priceInfo.min),
      onSaleStartDate,
      preSaleDate: preSaleStarts,
    }
    return { ...cardData, ...details }
  }
  return cardData
}

function getProductionsByVenue(showCard = {}, productionsByTitle = {}) {
  let titleSlug
  if (showCard.titleDetails) {
    titleSlug = showCard.titleDetails.titleSlug
  } else {
    const title = showCard.tourName || ''
    titleSlug = title.toString().replace(/ /g, '-').toLowerCase()
  }

  return Object.values(productionsByTitle[titleSlug] || [])
}

function populateShowCards(
  showCards,
  productionsByTitle,
  isHero,
  toursByTitleSlug
) {
  return showCards
    .filter((showCard) => {
      if (showCard.isTour && !toursByTitleSlug[showCard.title.titleSlug]) {
        return false
      }

      if (
        Array.isArray(showCard.production) &&
        showCard.production
          .map(
            (performance) =>
              performance.venue?.venueSlug &&
              toursByTitleSlug[showCard.title.titleSlug][
                performance.venue.venueSlug
              ]
          )
          .every(
            (performance) => performance && performance.status === 'embargo'
          )
      ) {
        return false
      }
      // check to see if externalButTicketsLink exists and do not ignore if it exists
      const showcardProduction = showCard.production
      const externalBuyTicketsLink =
        showcardProduction && showcardProduction.externalBuyTicketsLink
      if (!showCard.titleDetails && !externalBuyTicketsLink) {
        return true
      }

      const production =
        showCard.production ||
        getProductionsByVenue(showCard, productionsByTitle)[0]
      return (
        !production ||
        !(production.status === 'embargo' || production.status === 'closed')
      )
    })
    .map((showCard) => {
      let transformedProduction = undefined
      let productionArray = []

      const isTour = showCard.isTour
      if (isTour) {
        const titleSlug = showCard.title.titleSlug
        productionArray = Object.values(toursByTitleSlug[titleSlug])
          .filter((tourProduction) => tourProduction.nextPerformanceDate)
          .map(transformTourProduction)
        transformedProduction = productionArray[0]
      } else if (showCard.production) {
        const titleSlug = showCard.titleDetails.titleSlug
        const venueSlug = showCard.production.venue.venueSlug
        const venues = productionsByTitle[titleSlug]
        transformedProduction = venues[venueSlug]
        productionArray = Object.values(venues).map(transformProduction)
      } else {
        const titleSlug =
          showCard.titleDetails && showCard.titleDetails.titleSlug
        if (titleSlug) {
          const venues = productionsByTitle[titleSlug]
          if (venues) {
            const venuesList = Object.values(venues)
            transformedProduction = venuesList[0]
            productionArray = venuesList.map(transformProduction)
          }
        }
      }

      // there's a bunch more of field mapping inside this function (in different stages!  extractCardData)
      // it's confusing as hell (⊙_☉)
      return populateShowCard({
        showCard,
        production: transformedProduction,
        isHero,
        isTour,
        venueCount: productionArray.length,
        productionsByVenue: productionArray.sort(byfirstPerformanceDateAsc),
      })
    })
}

module.exports = {
  populateShowCard,
  populateShowCards,
}
