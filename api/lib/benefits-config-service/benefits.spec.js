const sinon = require('sinon')

const benefitsService = require('./benefits')

describe('getBenefitConfigs', () => {
  it('should return correct benefitConfigs and promoCodes', async () => {
    const expectedResolve = {
      data: {
        data: {
          benefitConfig: {
            hiddenPromoCodeIds: '["D4A3E81B-6796-4FC6-944D-4734E670057F"]',
            benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
            benefitAmenities: {
              transactionFeeOverride: 0,
              preSaleEnabled: false,
            },
          },
        },
      },
    }
    const fetchBenefitsStub = sinon.stub().resolves(expectedResolve)

    const benefits = [
      {
        id: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
        valid_until_date: '2024-05-24',
      },
    ]

    const response = await benefitsService.getBenefitConfigs(
      benefits,
      fetchBenefitsStub,
      null
    )

    expect(fetchBenefitsStub.calledOnce).toBeTruthy()
    expect(
      fetchBenefitsStub.calledWithExactly(
        '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15'
      )
    ).toBeTruthy()
    expect(response).toStrictEqual({
      hiddenPromoCodeIds: ['D4A3E81B-6796-4FC6-944D-4734E670057F'],
      benefitConfigs: [
        {
          hiddenPromoCodeIds: ['D4A3E81B-6796-4FC6-944D-4734E670057F'],
          benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
          benefitAmenities: {
            preSaleEnabled: false,
            transactionFeeOverride: 0,
          },
        },
      ],
    })
  })

  it('should return empty benefitConfigs if benefits are not provided', async () => {
    const fetchBenefitsStub = sinon.stub().resolves(undefined)

    const benefits = []

    const response = await benefitsService.getBenefitConfigs(
      benefits,
      fetchBenefitsStub,
      null
    )

    expect(fetchBenefitsStub.notCalled).toBeTruthy()
    expect(response).toStrictEqual({
      hiddenPromoCodeIds: [],
      benefitConfigs: [],
    })
  })

  it('should return empty benefitConfigs if benefitConfig is missing', async () => {
    const expectedResolve = {
      data: {
        data: {},
      },
    }
    const fetchBenefitsStub = sinon.stub().resolves(expectedResolve)

    const benefits = [
      {
        id: 'test-id',
        valid_until_date: '2024-05-24',
      },
    ]

    const response = await benefitsService.getBenefitConfigs(
      benefits,
      fetchBenefitsStub,
      null
    )

    expect(fetchBenefitsStub.calledOnce).toBeTruthy()
    expect(response).toStrictEqual({
      hiddenPromoCodeIds: [],
      benefitConfigs: [],
    })
  })
})
