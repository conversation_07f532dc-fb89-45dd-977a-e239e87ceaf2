const axios = require('axios')
const {
  generateTraceContextHeaders,
} = require('@atg-digital/server-logger-library')

const { getBenefitConfig } = require('./query')

const { BENEFITS_PROMO_CODES_ENDPOINT, BENEFITS_PROMO_CODES_API_KEY } =
  process.env

const fetchBenefitInfo = (benefitId) => {
  const requestData = JSON.stringify({
    query: getBenefitConfig,
    variables: { benefitId },
  })

  return axios.post(BENEFITS_PROMO_CODES_ENDPOINT, requestData, {
    headers: {
      ...generateTraceContextHeaders(),
      'Content-Type': 'application/json',
      'x-api-key': BENEFITS_PROMO_CODES_API_KEY,
    },
  })
}

const getBenefitConfigs = async (benefits, fetchBenefitInfoFunc) => {
  let responseObj = {}

  const responsesArray = await Promise.all(
    benefits.map((benefit) => fetchBenefitInfoFunc(benefit.id))
  )

  responseObj = responsesArray.reduce(
    (acc, response) => {
      if (response.data.data.benefitConfig) {
        acc.hiddenPromoCodeIds.push(
          JSON.parse(response.data.data.benefitConfig.hiddenPromoCodeIds)[0]
        )

        acc.benefitConfigs.push({
          ...response.data.data.benefitConfig,
          hiddenPromoCodeIds: JSON.parse(
            response.data.data.benefitConfig.hiddenPromoCodeIds
          ),
        })
      }

      return acc
    },
    {
      hiddenPromoCodeIds: [],
      benefitConfigs: [],
    }
  )
  return responseObj
}

module.exports = { getBenefitConfigs, fetchBenefitInfo }
