process.env.CUSTOMER_ACCOUNTS_SERVICE_SUBDOMAIN =
  'customer-accounts-service-test'
const mockIsFeatureEnabled = jest.fn()

const axios = require('axios')

const { validateBenefitsByDate, updateBenefits } = require('./index')

jest.mock('../optimizely', () => ({
  isFeatureEnabled: mockIsFeatureEnabled,
}))
jest.mock('@optimizely/optimizely-sdk', () => ({
  createInstance: jest.fn(),
}))

const brand = 'brand.com'

describe('account-service-client', () => {
  const mockUnexpiredBenefits = [
    {
      id: 'A121C3B5-39CD-4979-9F47-1009205E3575',
      validUntilDate: '2025-01-01',
    },
  ]

  const mockExpiredBenefits = [
    {
      id: 'A121C3B5-39CD-4979-9F47-1009205E3575',
      validUntilDate: '2024-01-01',
    },
    {
      id: 'A121C3B5-39CD-4979-9F47-1009205E3575',
      validUntilDate: '2020-05-01',
    },
  ]

  describe('validateBenefitsByDate', () => {
    beforeEach(() => {
      jest.useFakeTimers()
      jest.setSystemTime(Date.parse('2022-03-03T00:00:00Z'))
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('benefits validation with an unexpired date', async () => {
      const result = validateBenefitsByDate(mockUnexpiredBenefits)

      expect(result).toBe(false)
    })

    it('benefits validation with a past date', async () => {
      const result = validateBenefitsByDate(mockExpiredBenefits)

      expect(result).toBe(true)
    })
  })

  describe('updateBenefits', () => {
    beforeEach(() => {
      jest.useFakeTimers()
      jest.setSystemTime(Date.parse('2022-03-03T00:00:00Z'))
      mockIsFeatureEnabled.mockResolvedValue(false)
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    const customerDataResponseHeaderStub = {
      headers: {
        'set-cookie': ['user-data=newUserData;', 'user-sig=newUserSig'],
      },
    }

    it('receives updated user data, user signature and cookie value when benefits have expired', async () => {
      jest
        .spyOn(axios, 'get')
        .mockImplementation()
        .mockResolvedValue(customerDataResponseHeaderStub)

      const result = await updateBenefits(
        mockExpiredBenefits,
        'userData',
        'userSig',
        'boltSession',
        brand
      )

      expect(result).toStrictEqual({
        isCookieUpdated: true,
        userData: 'newUserData',
        userSignature: 'newUserSig',
        updatedCookie: ['user-data=newUserData;', 'user-sig=newUserSig'],
      })
    })

    it('receives passed user data and user signature when benefits have not expired', async () => {
      jest
        .spyOn(axios, 'get')
        .mockImplementation()
        .mockResolvedValue(customerDataResponseHeaderStub)

      const result = await updateBenefits(
        mockUnexpiredBenefits,
        'userData',
        'userSig',
        'boltSession',
        brand
      )

      expect(result).toStrictEqual({
        isCookieUpdated: false,
        userData: 'userData',
        userSignature: 'userSig',
      })
    })
  })
})
