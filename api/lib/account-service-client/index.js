const axios = require('axios')
const setCookie = require('set-cookie-parser')
const {
  serverLogger,
  generateTraceContextHeaders,
} = require('@atg-digital/server-logger-library')

const { getCustomerAccountsServiceEndpoint } = require('../brand-utils')
const DateFormatter = require('../dateFormatter')

const dateFormatter = new DateFormatter()

async function updateBenefits(
  benefits,
  userData,
  userSignature,
  boltSession,
  brand
) {
  const log = serverLogger.getInstance()
  const isUpdateNeeded = validateBenefitsByDate(benefits)
  if (isUpdateNeeded) {
    try {
      const CUSTOMER_ACCOUNTS_SERVICE_URL =
        getCustomerAccountsServiceEndpoint(brand)

      const headers = {
        ...generateTraceContextHeaders(),
        cookie: `user-data=${userData}; user-sig=${userSignature}; bolt-session=${boltSession};`,
      }

      const customerDataResponse = await axios.get(
        `${CUSTOMER_ACCOUNTS_SERVICE_URL}/customer-data`,
        {
          headers,
        }
      )
      if (customerDataResponse?.headers?.['set-cookie']) {
        const cookies = setCookie.parse(
          customerDataResponse.headers['set-cookie'],
          {
            decodeValues: true,
            map: false,
            silent: false,
          }
        )
        const updatedUserData = cookies.find(
          (cookie) => cookie.name === 'user-data'
        )
        const updatedUserSig = cookies.find(
          (cookie) => cookie.name === 'user-sig'
        )

        return {
          userData: updatedUserData?.value,
          userSignature: updatedUserSig?.value,
          isCookieUpdated: true,
          updatedCookie: customerDataResponse.headers['set-cookie'],
        }
      }
    } catch (err) {
      log.error(`Invalid token: ${err}`)
    }
  }

  return { userData, userSignature, isCookieUpdated: false }
}
function validateBenefitsByDate(benefits) {
  if (Array.isArray(benefits)) {
    return benefits.some((benefit) => {
      return dateFormatter
        .todayUtc()
        .isAfter(dateFormatter.dateUtc(benefit.validUntilDate), 'day')
    })
  }
  return false
}

module.exports = {
  updateBenefits,
  validateBenefitsByDate,
}
