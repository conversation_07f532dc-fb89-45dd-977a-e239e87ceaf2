function parseQuestions({ state, ...questions } = {}) {
  return Object.values(questions).map((question) => ({
    id: question.question_id.standard,
    instanceId: question.questioninstance_id.standard,
    body: question.body.display,
  }))
}

function parseQuestionsRequired({ state, ...performanceQuestions }) {
  return Object.values(performanceQuestions).reduce(
    (acc, performanceQuestion) => ({
      ...acc,
      [performanceQuestion.performancequestion_question_id.standard]:
        performanceQuestion.performancequestion_required.standard === '1',
    }),
    {}
  )
}

function getQuestionsWithAnswers(
  allQuestions,
  allAnswers,
  requiredQuestionsMap
) {
  return allQuestions.map((question) => {
    const answersForQuestion =
      allAnswers.find((answer) => answer.question_id === question.id) || {}

    return {
      id: question.instanceId, // No need to send question_id to the user, answers are attached to questioninstance_id
      body: question.body,
      title: answersForQuestion.title,
      answers: answersForQuestion.answers,
      required: requiredQuestionsMap[question.id],
    }
  })
}

const getTransactionFeeServiceCharge = (serviceChargeDetails) => {
  const transactionFeeEntry = Object.values(serviceChargeDetails).find(
    (serviceChargeDetailsEntry) =>
      ['order processing fee', 'order fee'].some((value) =>
        serviceChargeDetailsEntry.description?.standard
          .toLowerCase()
          .includes(value)
      ) || serviceChargeDetailsEntry.description?.standard === 'Transaction Fee'
  )

  return transactionFeeEntry
}

const fieldsToRedact = new Set([
  'userid',
  'userId',
  'user_name',
  'password',
  'comp_password',
  'first_name',
  'last_name',
  'email',
  'phone_number1',
  'street',
  'city',
  'zip',
  'account_name',
  'account_number',
  'sort_code',
  'cardholder_name',
  'cvv_code', // credit card cvv
])

const redactObjectEntries = (entries, [field, value]) => {
  const fieldName = field.split('::').pop()
  entries[field] = fieldsToRedact.has(fieldName)
    ? '-redacted-'
    : redactFieldValues(value)
  return entries
}

const redactFieldValues = (object) =>
  object && typeof object === 'object'
    ? Array.isArray(object)
      ? object.map(redactFieldValues)
      : Object.entries(object).reduce(redactObjectEntries, {})
    : object

module.exports = {
  parseQuestions,
  parseQuestionsRequired,
  getQuestionsWithAnswers,
  getTransactionFeeServiceCharge,
  redactFieldValues,
}
