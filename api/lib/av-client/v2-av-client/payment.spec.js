'use strict'

const sinon = require('sinon')

const {
  WORLDPAY_GATEWAY_NAME,
  WORLDPAY_DDC_OPERATION_NAME,
} = require('../../utils')
const { AvAtgError } = require('../exceptions')

const { insertOrder, processPaymentRedirectData } = require('./payment')

const ORDER_ID = 'order-id'
const DELIVERY_ID = 'delivery-id'
const ORDER_NUMBER = 'order-number'
const SEAT_ID = 'seat-id'
const DELIVERY_TIME = 'delivery-time'
const NAME = 'name'
const PA_REQUEST_URL = 'pa_request_URL'
const PA_REQUEST_DATA_FOR_DDC = {
  configuration: {
    gateway: WORLDPAY_GATEWAY_NAME,
    operation: WORLDPAY_DDC_OPERATION_NAME,
  },
  body: {
    Bin: 123,
    JWT: 'jwt',
  },
}
const PA_REQUEST_INFORMATION_FOR_DDC = JSON.stringify(PA_REQUEST_DATA_FOR_DDC)
const PA_REQUEST_DATA_FOR_3DS_CHALLENGE = { body: { JWT: 'jwt' } }
const PA_REQUEST_INFORMATION_FOR_3DS_CHALLENGE = JSON.stringify(
  PA_REQUEST_DATA_FOR_3DS_CHALLENGE
)

const OPT_OUT = false

describe('av-client - payment', () => {
  const apiRequestStubData = {
    data: {
      'Order::order_number': { standard: ORDER_NUMBER },
      'Admissions': { seat_id: SEAT_ID },
      'DeliveryMethodDetails': { delivery_time: DELIVERY_TIME, name: NAME },
      'Payments': {},
      'PerformanceDetails': {},
      'Tickets': {},
      'PriceTypes': {},
      'MiscItemDetails': {},
      'ServiceChargeDetails': {},
      'OrderItems': {},
    },
  }
  const expectedSuccessResponse = {
    orderNumber: 'order-number',
    admissions: { seat_id: SEAT_ID },
    deliveryMethodDetails: { delivery_time: DELIVERY_TIME, name: NAME },
    payments: {},
    performanceDetails: {},
    tickets: {},
    priceTypes: {},
    miscItemDetails: {},
    serviceChargeDetails: {},
    orderItems: {},
  }

  it('insertOrder', async () => {
    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      undefined,
      undefined,
      OPT_OUT,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])
    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder without data10 field if optOut is undefined', async () => {
    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])
    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with answers', async () => {
    const ANSWERS = { question: 'answer' }

    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      ANSWERS,
      undefined,
      undefined,
      undefined,
      OPT_OUT,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
            'Questions::question::answers': 'answer',
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with ipAddress', async () => {
    const ip = '127.0.0.1'

    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      undefined,
      undefined,
      OPT_OUT,
      ip
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
            'Marketing::data40': ip,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with referrerSource', async () => {
    const REFERRER_SOURCE = 'referrer-source'

    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      REFERRER_SOURCE,
      undefined,
      undefined,
      OPT_OUT,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
            'Marketing::data15': REFERRER_SOURCE,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with singleUsePromoCode', async () => {
    const SINGLE_USE_PROMOCODE = 'sp_31'

    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      SINGLE_USE_PROMOCODE,
      undefined,
      OPT_OUT,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
            'Marketing::data16': SINGLE_USE_PROMOCODE,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with showConfig', async () => {
    const SHOW_CONFIG = 'show-config'

    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      undefined,
      SHOW_CONFIG,
      OPT_OUT,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 0,
            'Marketing::data17': SHOW_CONFIG,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('insertOrder - with optOut true', async () => {
    const apiRequestStub = sinon.stub().resolves(apiRequestStubData)

    const response = await insertOrder(
      apiRequestStub,
      ORDER_ID,
      DELIVERY_ID,
      undefined,
      undefined,
      undefined,
      undefined,
      true,
      undefined
    )
    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: 'order-id',
          actions: [
            { method: 'insert', params: { notification: 'correspondence' } },
          ],

          set: {
            'Order::deliverymethod_id': DELIVERY_ID,
            'Marketing::data4': 'Bolt',
            'Marketing::data10': 1,
          },

          get: [
            'Order::order_number',
            'Admissions',
            'DeliveryMethodDetails',
            'PerformanceDetails',
            'Payments',
            'Tickets',
            'PriceTypes',
            'MiscItemDetails',
            'ServiceChargeDetails',
            'OrderItems',
          ],
        },
      },
    ])

    expect(response).toStrictEqual(expectedSuccessResponse)
  })

  it('processPaymentRedirectData - valid DDC', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const result = await processPaymentRedirectData(
      PA_REQUEST_URL,
      PA_REQUEST_INFORMATION_FOR_DDC,
      ORDER_ID,
      closeSessionStub
    )

    expect(closeSessionStub.notCalled).toBeTruthy()
    expect(result).toStrictEqual({
      redirectTo: PA_REQUEST_URL,
      orderId: ORDER_ID,
      ddcRequired: true,
      paymentRequestParams: {
        JWT: PA_REQUEST_DATA_FOR_DDC.body.JWT,
        Bin: PA_REQUEST_DATA_FOR_DDC.body.Bin,
      },
    })
  })

  it('processPaymentRedirectData - invalid DDC', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const erroneous_pa_request_information = JSON.stringify({
      configuration: {
        gateway: WORLDPAY_GATEWAY_NAME,
        operation: WORLDPAY_DDC_OPERATION_NAME,
      },
      body: {},
    })

    const promise = processPaymentRedirectData(
      PA_REQUEST_URL,
      erroneous_pa_request_information,
      ORDER_ID,
      closeSessionStub
    )

    await expect(promise).rejects.toStrictEqual(
      new AvAtgError('Device Data Collection Failed')
    )
    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('processPaymentRedirectData - valid 3DS Challenge', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const result = await processPaymentRedirectData(
      PA_REQUEST_URL,
      PA_REQUEST_INFORMATION_FOR_3DS_CHALLENGE,
      ORDER_ID,
      closeSessionStub
    )

    expect(closeSessionStub.notCalled).toBeTruthy()
    expect(result).toStrictEqual({
      redirectTo: PA_REQUEST_URL,
      orderId: ORDER_ID,
      paymentRequestParams: {
        JWT: PA_REQUEST_DATA_FOR_3DS_CHALLENGE.body.JWT,
      },
    })
  })

  it('processPaymentRedirectData - invalid 3DS Challenge', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const erroneous_pa_request_information_for_3ds_challenge = JSON.stringify({
      body: {},
    })

    const promise = processPaymentRedirectData(
      PA_REQUEST_URL,
      erroneous_pa_request_information_for_3ds_challenge,
      ORDER_ID,
      closeSessionStub
    )

    await expect(promise).rejects.toMatchObject({
      message: '3DS Challenge Failed',
    })
    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('processPaymentRedirectData - no DDC nor 3DS Challenge (current Verifone flow)', async () => {
    const result = await processPaymentRedirectData(
      PA_REQUEST_URL,
      '00022pa_request_information00007decoded',
      ORDER_ID
    )

    expect(result).toStrictEqual({
      redirectTo: PA_REQUEST_URL,
      orderId: ORDER_ID,
      paymentRequestParams: {
        pa_request_information: 'decoded',
      },
    })
  })
})
