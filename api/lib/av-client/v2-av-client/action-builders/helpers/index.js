const {
  getPaymentMethodId,
  getPaymentType,
  avPaymentTypes,
  avAccountHolder,
  avPaymentFrequency,
  avAccountPaymentStatus,
} = require('../../../../../config')

function paramsForDirectDebit(paymentMethodId, paymentMethod) {
  const { directDebit } = paymentMethod
  return {
    paymentMethodId,
    paymentType: avPaymentTypes.payment,
    Payment: {
      account_holder: avAccountHolder.yes,
      account_number: directDebit.accountNumber,
      sort_code: directDebit.sortCode,
      account_name: directDebit.accountName,
      account_status: avAccountPaymentStatus.active,
      payment_frequency: avPaymentFrequency.annual,
      bacs_reference_number: directDebit.bacsReference,
    },
  }
}

function paramsForGiftCertificate(paymentMethodId, paymentMethod) {
  const { giftCertificate } = paymentMethod
  return {
    paymentMethodId,
    paymentType: avPaymentTypes.payment,
    Payment: {
      gift_certificate: giftCertificate.voucherNumber,
      gift_certificate_redemption_number: giftCertificate.voucherRedemption,
      transaction_amount: giftCertificate.amount,
    },
  }
}

function paramsForCard(paymentMethodId, paymentMethod, callbackUrl) {
  const { cc } = paymentMethod
  return {
    paymentMethodId,
    paymentType: avPaymentTypes.payment,
    Payment: {
      account_number: cc.number,
      expiration_date: cc.expires,
      cvv_code: cc.cvv,
      cardholder_name: cc.name,
      transaction_amount: cc.amount,
      pa_response_URL: callbackUrl,
      swipe_indicator: 'Internet',
    },
  }
}

function paramsForPaypal(paymentMethodId, paymentMethod, callbackUrl) {
  const { paypal } = paymentMethod
  return {
    paymentMethodId,
    paymentType: avPaymentTypes.payment,
    Payment: {
      pa_response_URL: callbackUrl,
      transaction_amount: paypal.amount,
    },
  }
}

function getParamsForMethod(
  paymentMethod,
  callbackUrl,
  availablePaymentMethods
) {
  const getPaymentObj = getPaymentType(paymentMethod.paymentType)

  const paymentMethodId = getPaymentMethodId(
    getPaymentObj,
    paymentMethod.cc ? paymentMethod.cc.type : undefined,
    availablePaymentMethods
  )

  switch (paymentMethod.paymentType) {
    case 'directDebit':
      return paramsForDirectDebit(paymentMethodId, paymentMethod)
    case 'giftCertificate':
      return paramsForGiftCertificate(paymentMethodId, paymentMethod)
    case 'paypal':
      return paramsForPaypal(paymentMethodId, paymentMethod, callbackUrl)
    case 'creditCard':
      return paramsForCard(paymentMethodId, paymentMethod, callbackUrl)
    default:
      return 'No Payments found'
  }
}

module.exports = { getParamsForMethod }
