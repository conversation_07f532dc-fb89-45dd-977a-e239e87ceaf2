const { getParamsForMethod } = require('./helpers')

function addCustomer(customerId) {
  return {
    method: 'addCustomer',
    params: {
      Customer: {
        customer_id: customerId,
        organization_type: 1,
      },
    },
  }
}

function manageItems(item) {
  const { itemId, quantity } = item
  return {
    method: 'manageItems',
    params: {
      itemId,
      quantity,
    },
  }
}

function addPayments(paymentMethods, callbackUrl, availablePaymentMethods) {
  return paymentMethods.map((method) =>
    addPayment(method, callbackUrl, availablePaymentMethods)
  )
}

function insertOrder() {
  return {
    method: 'insert',
    params: {
      notification: 'correspondence',
    },
  }
}

function addPayment(paymentMethod, callbackUrl, availablePaymentMethods) {
  return {
    method: 'addPayment',
    params: getParamsForMethod(
      paymentMethod,
      callbackUrl,
      availablePaymentMethods
    ),
  }
}

module.exports = { addCustomer, addPayments, insertOrder, manageItems }
