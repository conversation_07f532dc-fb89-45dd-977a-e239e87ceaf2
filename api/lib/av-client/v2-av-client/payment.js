const {
  WORLDPAY_GATEWAY_NAME,
  WORLDPAY_DDC_OPERATION_NAME,
} = require('../../utils')
const { AvAtgError } = require('../exceptions')
const { tryParseJSON } = require('../../utils')
const { decodeAVStr } = require('../../av-utils')

const { orderBuilders } = require('./action-builders')

const getAnswersParams = (answers) =>
  Object.entries(answers).reduce(function (acc, pair) {
    const [key, value] = pair
    acc[`Questions::${key}::answers`] = value
    return acc
  }, {})

async function insertOrder(
  apiRequest,
  orderId,
  deliveryId,
  answers,
  referrerSource,
  singleUsePromoCode,
  showConfig,
  optOut,
  ip
) {
  const answersParams = answers ? getAnswersParams(answers) : {}

  const referrerParam = referrerSource
    ? {
        'Marketing::data15': referrerSource,
      }
    : {}
  const singleUsePromoCodeParam = singleUsePromoCode
    ? {
        'Marketing::data16': singleUsePromoCode,
      }
    : {}
  const configParam = showConfig
    ? {
        'Marketing::data17': showConfig,
      }
    : {}

  const ipAddress = ip
    ? {
        'Marketing::data40': ip,
      }
    : {}

  const orderRequest = {
    objectName: orderId,
    actions: [orderBuilders.insertOrder()],
    set: {
      'Order::deliverymethod_id': deliveryId,
      'Marketing::data4': 'Bolt',
      ...(optOut !== undefined && {
        'Marketing::data10': Number(Boolean(optOut)),
      }),
      ...referrerParam,
      ...singleUsePromoCodeParam,
      ...configParam,
      ...answersParams,
      ...ipAddress,
    },
    get: [
      'Order::order_number',
      'Admissions',
      'DeliveryMethodDetails',
      'PerformanceDetails',
      'Payments',
      'Tickets',
      'PriceTypes',
      'MiscItemDetails',
      'ServiceChargeDetails',
      'OrderItems',
    ],
  }

  const { data } = await apiRequest('order', { req: orderRequest })
  const orderNumber = data['Order::order_number'].standard

  return {
    orderNumber,
    payments: data.Payments,
    admissions: data.Admissions,
    performanceDetails: data.PerformanceDetails,
    deliveryMethodDetails: data.DeliveryMethodDetails,
    tickets: data.Tickets,
    priceTypes: data.PriceTypes,
    miscItemDetails: data.MiscItemDetails,
    serviceChargeDetails: data.ServiceChargeDetails,
    orderItems: data.OrderItems,
  }
}

async function processPaymentRedirectData(
  pa_request_URL,
  pa_request_information,
  orderId,
  closeSession
) {
  const paRequestData = tryParseJSON(pa_request_information)

  const result = {
    redirectTo: pa_request_URL,
    orderId,
  }

  if (paRequestData) {
    const {
      configuration,
      body: { Bin, JWT },
    } = paRequestData

    const isDDC = configuration
      ? configuration.gateway === WORLDPAY_GATEWAY_NAME &&
        configuration.operation === WORLDPAY_DDC_OPERATION_NAME
      : false

    if (Bin && JWT && isDDC) {
      result.paymentRequestParams = { Bin, JWT }
      result.ddcRequired = true
    } else if (JWT && !Bin && !isDDC) {
      result.paymentRequestParams = { JWT }
    } else {
      if (closeSession) {
        await closeSession().catch(() => {})
      }

      const errorMsg = isDDC
        ? 'Device Data Collection Failed'
        : '3DS Challenge Failed'

      throw new AvAtgError(errorMsg)
    }
  } else {
    result.paymentRequestParams = decodeAVStr(pa_request_information)
  }

  return result
}

module.exports = {
  insertOrder,
  processPaymentRedirectData,
}
