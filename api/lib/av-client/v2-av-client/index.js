const hyperid = require('hyperid')({ urlSafe: true, fixedLength: true })

const {
  AvPaymentTokenRequiredError,
} = require('../../../lib/av-client/exceptions')
const { loadAndUpdateCustomerFields } = require('../helpers/user')
const { parseQuestions } = require('../utils')
const {
  fetchOrderPaymentPaRequestData,
  getPayloadForAdditionalItem,
} = require('../helpers')
const { v2allocateBestAvailableSeat } = require('../v2-av-helpers')
const { log } = require('../../log')

const { v2ValidateVoucher } = require('./voucher')
const { v2InsertCustomer } = require('./customer')
const { processPaymentRedirectData, insertOrder } = require('./payment')
const { manageItems } = require('./action-builders/orderBuilders')
const avOrder = require('./av-order')

const PERFORMANCE_ID_LENGTH = 36

async function updateDirectDebitDate({
  paymentMethod,
  apiRequest,
  customerId,
  customerSessionId,
}) {
  if (paymentMethod.paymentType !== 'directDebit') {
    return
  }

  const collectionDate = paymentMethod.directDebit.collectionDate

  if (collectionDate) {
    // convert '01/12/2019' format to '2019-12-01'
    const date = collectionDate.split('/').reverse().join('-')

    await loadAndUpdateCustomerFields(
      apiRequest,
      customerId,
      customerSessionId,
      { 'Marketing::date1': date } // customer fields to update
    )
  }
}

// changes here may need to be reflected in v2membershipPurchaseCallback below
async function v2membershipPurchase(
  apiRequest,
  userPaymentMethods,
  membership,
  { customerId, customerSessionId, userAgent },
  deliveryMethodId,
  preferredVenue,
  callbackUrl
) {
  const orderId = hyperid()

  const hasTokenExToken = userPaymentMethods[0].txToken
  if (hasTokenExToken) {
    userPaymentMethods[0].cc.cvv = '{{{cvv}}}'
  }

  const availablePaymentMethodsResponse = await apiRequest(
    'order',
    {
      req: {
        actions: [manageItems(membership)],
        get: ['AvailablePaymentMethods'],
        objectName: orderId,
      },
    },
    { isV2Explicit: true }
  )
  log.info(availablePaymentMethodsResponse, 'availablePaymentMethodsResponse')
  const availablePaymentMethods = parseAvAvailablePaymentMethods(
    availablePaymentMethodsResponse
  )
  log.info(availablePaymentMethods, 'availablePaymentMethods')
  const membershipReq = avOrder.buildMembershipRequest(
    userPaymentMethods,
    customerId,
    orderId,
    callbackUrl,
    availablePaymentMethods,
    userAgent
  )

  const {
    data: {
      Questions,
      Customer,
      'Order::lastAddedPayments': lastAddedPayments,
    },
  } = await apiRequest(
    'order',
    { req: membershipReq },
    { isTokenExRequest: hasTokenExToken }
  )

  const questions = parseQuestions(Questions)

  const insertReq = avOrder.buildInsertRequest(
    questions[0].instanceId,
    preferredVenue,
    orderId,
    deliveryMethodId
  )

  try {
    await apiRequest('order', { req: insertReq })

    await updateDirectDebitDate({
      paymentMethod: userPaymentMethods[0],
      apiRequest,
      customerId,
      customerSessionId,
    })

    return {
      membershipNumber: Customer[customerId].customer_number.standard,
    }
  } catch (err) {
    if (err instanceof AvPaymentTokenRequiredError) {
      const paymentId = lastAddedPayments.standard[0]

      const { pa_request_URL, pa_request_information } =
        await fetchOrderPaymentPaRequestData(orderId, paymentId, apiRequest)

      const response = await processPaymentRedirectData(
        pa_request_URL,
        pa_request_information,
        orderId
      )

      return response
    } else {
      throw err
    }
  }
}

async function v2membershipPurchaseCallback(
  apiRequest,
  paymentMethods,
  { customerId, customerSessionId },
  orderId,
  paymentId
) {
  const insertReq = avOrder.buildCallbackInsertRequest(orderId)

  try {
    const {
      data: { Customer },
    } = await apiRequest('order', { req: insertReq })

    await updateDirectDebitDate({
      paymentMethod: paymentMethods[0],
      apiRequest,
      customerId,
      customerSessionId,
    })

    return {
      membershipNumber: Customer[customerId].customer_number.standard,
    }
  } catch (err) {
    if (err instanceof AvPaymentTokenRequiredError) {
      const { pa_request_URL, pa_request_information } =
        await fetchOrderPaymentPaRequestData(orderId, paymentId, apiRequest)

      const response = await processPaymentRedirectData(
        pa_request_URL,
        pa_request_information,
        orderId
      )

      return response
    } else {
      throw err
    }
  }
}

const parseAvAvailablePaymentMethods = (response) => {
  return Object.values(response?.data?.AvailablePaymentMethods || []).reduce(
    (availablePaymentMethods, { payment_method_id, description }) => {
      if (payment_method_id && description) {
        availablePaymentMethods.push({
          paymentMethodId: payment_method_id.standard,
          description: description.standard,
        })
      }

      return availablePaymentMethods
    },
    []
  )
}

const buildAddCustomerToOrderRequest = ({
  orderId,
  customerNumber,
  customerId,
}) => {
  const avCustomerObject = {}

  if (customerNumber) {
    avCustomerObject.Customer = {
      customer_number: customerNumber,
    }
  } else if (customerId) {
    avCustomerObject.Customer = {
      customer_id: customerId,
    }
  }

  const request = {
    req: {
      actions: [
        {
          method: 'addCustomer',
          params: avCustomerObject,
        },
      ],
      get: ['AvailablePaymentMethods', 'ServiceChargeDetails'],
      objectName: orderId,
    },
  }

  return request
}

async function addCustomerNumberToOrder(apiRequest, orderId, customerNumber) {
  const request = buildAddCustomerToOrderRequest({ orderId, customerNumber })
  const addCustomerResponse = await apiRequest('order', request, {
    isV2Explicit: true,
  })

  const availablePaymentMethods =
    parseAvAvailablePaymentMethods(addCustomerResponse)

  const serviceChargeDetails = addCustomerResponse.data.ServiceChargeDetails

  return { availablePaymentMethods, serviceChargeDetails }
}

async function addCustomerIdToOrder(apiRequest, orderId, customerId) {
  const request = buildAddCustomerToOrderRequest({ orderId, customerId })
  const addCustomerResponse = await apiRequest('order', request, {
    isV2Explicit: true,
  })

  const availablePaymentMethods =
    parseAvAvailablePaymentMethods(addCustomerResponse)

  const serviceChargeDetails = addCustomerResponse.data.ServiceChargeDetails

  return { availablePaymentMethods, serviceChargeDetails }
}

/*
 * assumes that none of the priceTypeIds share the seats available for a zone
 */
function getLoungeAvailabilityForPriceTypes(
  addSeatsInPriceType,
  avPricing,
  priceTypeIds
) {
  // initialise a total for each priceTypeId
  const totals = {}
  for (const priceTypeId of priceTypeIds) {
    totals[priceTypeId] = 0
  }

  delete avPricing.state
  const pricings = Object.values(avPricing)

  for (const { price_type_id, price_zone_ids } of pricings) {
    const priceTypeId = price_type_id.standard
    if (Object.prototype.hasOwnProperty.call(totals, priceTypeId)) {
      totals[priceTypeId] += price_zone_ids.standard.reduce(
        addSeatsInPriceType,
        0
      )
    }
  }
  return totals
}

function getLoungeAvailabilityByZones(availability) {
  const zones = {}
  delete availability.state
  for (const { price_zone, available_seats } of Object.values(availability)) {
    const priceZoneId = price_zone.standard
    zones[priceZoneId] = parseInt(available_seats.standard, 10)
  }
  return zones
}

function getSortedPriceZones(priceZones) {
  return Object.entries(priceZones)
    .sort(
      (priceZoneA, priceZoneB) =>
        priceZoneA[1].orderValue - priceZoneB[1].orderValue
    )
    .reduce(
      (
        newPriceZonesObject,
        [currentPriceZoneKey, { orderValue, ...currentPriceZoneValue }]
      ) => ({
        ...newPriceZonesObject,
        [currentPriceZoneKey]: currentPriceZoneValue,
      }),
      {}
    )
}

function getAvailableBistros(priceTypes, pricing, availabilities, legends) {
  const bistros = {}
  for (const key in priceTypes) {
    const priceTypeObj = { [key]: { priceZones: {} } }
    const foundPrice = pricing.find(
      (price) => price.price_type_id.standard === key
    )

    if (foundPrice) {
      foundPrice.price_zone_ids.standard.forEach((id) => {
        const foundAvailability = availabilities.find((avail) => {
          return (
            avail.price_zone.standard === id &&
            avail.available_seats?.standard !== '' &&
            parseInt(avail.available_seats?.standard, 10) > 0
          )
        })

        if (foundAvailability) {
          const foundLegend = legends.find(
            (legend) =>
              legend.value_legend_id.standard ===
              foundAvailability.price_zone.standard
          )

          if (foundLegend) {
            const priceZone = {
              priceZoneLabel: foundLegend.label.standard,
              availability: foundAvailability.available_seats?.standard,
              orderValue: foundLegend.value.standard,
            }
            priceTypeObj[key].priceZones[id] = priceZone
          }
        }
      })

      for (const priceTypeKey in priceTypeObj) {
        const priceZoneObj = priceTypeObj[priceTypeKey].priceZones

        priceTypeObj[priceTypeKey].priceZones =
          getSortedPriceZones(priceZoneObj)
      }

      Object.assign(bistros, priceTypeObj)
    }
  }

  return bistros
}

function getAvailableLoungesPriceTypes(priceTypes) {
  const loungPriceTypes = []
  for (const priceTypeKey in priceTypes) {
    if (
      !loungPriceTypes
        .map((avPriceType) => avPriceType.key)
        .includes(priceTypeKey)
    ) {
      loungPriceTypes.push(priceTypeKey)
    }
  }

  return loungPriceTypes
}

function getAvailableLounges(availability, pricing, priceTypeIds) {
  const availabilityByPriceZones = getLoungeAvailabilityByZones(availability)
  const addSeatsInPriceType = (subtotal, id) =>
    availabilityByPriceZones[id] + subtotal || subtotal

  return getLoungeAvailabilityForPriceTypes(
    addSeatsInPriceType,
    pricing,
    priceTypeIds
  )
}

function getBistroAndLoungeAvailabilities(
  data,
  loungePerformanceId,
  bistroPerformanceId
) {
  const result = {}

  if (loungePerformanceId) {
    result.lounges = { dbPerformanceId: loungePerformanceId }
  }

  if (bistroPerformanceId) {
    result.bistros = { dbPerformanceId: bistroPerformanceId }
  }

  data.forEach((item) => {
    if (item.id === bistroPerformanceId) {
      const bistros = getAvailableBistros(
        item.priceTypes,
        Object.values(item.pricing),
        Object.values(item.availability),
        Object.values(item.legends)
      )

      Object.assign(result.bistros, bistros)
    } else if (item.id === loungePerformanceId) {
      const availableLoungePriceTypes = getAvailableLoungesPriceTypes(
        item.priceTypes
      )

      result.lounges.seatsByPriceType = getAvailableLounges(
        item.availability,
        item.pricing,
        availableLoungePriceTypes
      )
    }
  })

  for (const key in result.lounges) {
    if (Object.keys(result.lounges[key]).length === 0) {
      delete result.lounges[key]
    }
  }

  return result
}

async function addAdditionalItemsToOrder(apiRequest, orderId, additionalItems) {
  for (const item of additionalItems) {
    const { type } = item
    if (type === 'standard' || type === 'ticket-protection') {
      const [path, payload] = getPayloadForAdditionalItem(orderId, item)
      await apiRequest(path, payload)
    } else if (type === 'lounge' || type === 'bistro') {
      const { details, quantity } = item
      if (
        !(
          details &&
          details.dbPerformanceId &&
          details.dbPerformanceId.length === PERFORMANCE_ID_LENGTH
        )
      ) {
        const errorMsg =
          'Invalid performance id while adding the lounge to the order'
        throw new Error(`${errorMsg}: "${details}"`)
      }

      for (let i = 0; i < quantity; i++) {
        await v2allocateBestAvailableSeat(apiRequest, orderId, item)
      }
    } else {
      throw new Error('Unknown type for additional Item in the order')
    }
  }
}

module.exports = {
  v2membershipPurchase,
  v2membershipPurchaseCallback,
  parseAvAvailablePaymentMethods,
  buildAddCustomerToOrderRequest,
  addCustomerNumberToOrder,
  addCustomerIdToOrder,
  insertOrder,
  getBistroAndLoungeAvailabilities,
  getSortedPriceZones,
  addAdditionalItemsToOrder,
  v2ValidateVoucher,
  v2InsertCustomer,
}
