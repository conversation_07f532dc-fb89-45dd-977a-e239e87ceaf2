const sinon = require('sinon')

const {
  AvPaymentTokenRequiredError,
} = require('../../../lib/av-client/exceptions')
const {
  data,
  responseMockData,
  priceZonesMockData,
  responsePriceZoneMockData,
} = require('../../../services/linked-availabilites/handlers/fixtures/av-linked-availabilities-input')
const { getPaymentMethodId } = require('../../../config')
const {
  WORLDPAY_GATEWAY_NAME,
  WORLDPAY_DDC_OPERATION_NAME,
} = require('../../utils')
const mockedAvAddCustomerToOrderResponse = require('../../test/fixtures/avAddCustomerResponse.json')

const {
  v2membershipPurchase,
  v2membershipPurchaseCallback,
  addAdditionalItemsToOrder,
} = require('./index')
const {
  parseAvAvailablePaymentMethods,
  buildAddCustomerToOrderRequest,
  addCustomerNumberToOrder,
  addCustomerIdToOrder,
  getBistroAndLoungeAvailabilities,
  getSortedPriceZones,
} = require('./')

const membership = {
  itemId: 'itemId',
  quantity: 'quantity',
}
const user = {
  customerId: 'customerId',
  customerSessionId: 'customerSessionId',
  userAgent: 'userAgent',
}
const deliveryMethodId = 'deliveryMethodId'
const preferredVenue = 'preferredVenue'
const callbackUrl = 'callbackUrl'
const membershipNumber = 'membershipNumber'
const cardPaymentMethod = {
  paymentType: 'creditCard',
  cc: {
    cvv: 'cvv',
    expires: 'expiry',
    name: 'card holder',
    number: 'card number',
    type: 'visa',
    amount: 35.0,
  },
}
const cardPaymentMethodWithTokenEx = {
  ...cardPaymentMethod,
  txToken: true,
}
const directDebitPaymentMethod = {
  paymentType: 'directDebit',
  directDebit: {
    accountNumber: 'account number',
    sortCode: 'sort code',
    accountName: 'direct debit name',
    bacsReference: 'bacs reference',
  },
}
const directDebitPaymentMethodWithDate = {
  ...directDebitPaymentMethod,
  directDebit: {
    ...directDebitPaymentMethod.directDebit,
    collectionDate: '04/03/2001',
  },
}
const questions = {
  q1: {
    question_id: {},
    questioninstance_id: { standard: 'venueQuestion' },
    body: {},
  },
}

const availablePaymentMethods = parseAvAvailablePaymentMethods(
  mockedAvAddCustomerToOrderResponse
)

const orderId = 'orderId'
const paymentId = 'paymentId'
const pa_request_information_Worldpay_DDC = {
  configuration: {
    gateway: WORLDPAY_GATEWAY_NAME,
    operation: WORLDPAY_DDC_OPERATION_NAME,
  },
  body: {
    Bin: 123,
    JWT: 'jwt',
  },
}
const pa_request_information_Worldpay_3DS_Challenge = {
  body: { JWT: 'jwt' },
}

describe('v2membershipPurchase', () => {
  it('membership purchase successfully inserts order - credit card', async () => {
    const paymentMethods = [cardPaymentMethod]
    const paymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentMethods[0].cc.type,
      availablePaymentMethods
    )

    const apiRequestStub = sinon.stub()
    const v1ApiRequestStub = sinon.stub()

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        Questions: questions,
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.calledThrice).toBeTruthy()

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId,
                paymentType: 0,
                Payment: {
                  account_number: 'card number',
                  expiration_date: 'expiry',
                  cvv_code: 'cvv',
                  cardholder_name: 'card holder',
                  transaction_amount: 35,
                  pa_response_URL: callbackUrl,
                  swipe_indicator: 'Internet',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: undefined },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })

    expect(v1ApiRequestStub.notCalled).toBeTruthy()
  })

  it('membership purchase successfully inserts order - credit card (TokenEx)', async () => {
    const paymentMethods = [cardPaymentMethodWithTokenEx]

    const apiRequestStub = sinon.stub()
    const v1ApiRequestStub = sinon.stub()

    const paymentMethodId = getPaymentMethodId(
      cardPaymentMethod.paymentType,
      cardPaymentMethod.cc.type,
      availablePaymentMethods
    )

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        Questions: questions,
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.calledThrice).toBeTruthy()

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId,
                paymentType: 0,
                Payment: {
                  account_number: 'card number',
                  expiration_date: 'expiry',
                  cvv_code: '{{{cvv}}}',
                  cardholder_name: 'card holder',
                  transaction_amount: 35,
                  pa_response_URL: callbackUrl,
                  swipe_indicator: 'Internet',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: true },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })

    expect(v1ApiRequestStub.notCalled).toBeTruthy()
  })

  it('membership purchase successfully inserts order - direct debit (without collection date)', async () => {
    const paymentMethods = [directDebitPaymentMethod]

    const apiRequestStub = sinon.stub()
    const v1ApiRequestStub = sinon.stub()

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        Questions: questions,
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.calledThrice).toBeTruthy()

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId: '11FD822F-A249-4446-91C8-AD248C568BC6',
                paymentType: 0,
                Payment: {
                  account_holder: '1',
                  account_number: 'account number',
                  sort_code: 'sort code',
                  account_name: 'direct debit name',
                  account_status: 0,
                  payment_frequency: 2,
                  bacs_reference_number: 'bacs reference',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: undefined },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })

    expect(v1ApiRequestStub.notCalled).toBeTruthy()
  })

  it('membership purchase successfully inserts order and updates date - direct debit (with collection date)', async () => {
    const paymentMethods = [directDebitPaymentMethodWithDate]

    const apiRequestStub = sinon.stub()
    const v1ApiRequestStub = sinon.stub()

    const paymentMethodId = getPaymentMethodId(
      directDebitPaymentMethod.paymentType,
      null,
      availablePaymentMethods
    )

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        Questions: questions,
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.callCount).toBe(5)

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId,
                paymentType: 0,
                Payment: {
                  account_holder: '1',
                  account_number: 'account number',
                  sort_code: 'sort code',
                  account_name: 'direct debit name',
                  account_status: 0,
                  payment_frequency: 2,
                  bacs_reference_number: 'bacs reference',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: undefined },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])
    expect(apiRequestStub.getCall(3).args).toMatchObject([
      'customer',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'load',
              params: { 'Customer::customer_id': 'customerId' },
            },
          ],
          get: [],
        },
      },
    ])
    expect(apiRequestStub.getCall(4).args).toMatchObject([
      'customer',
      {
        req: {
          actions: [
            {
              method: 'update',
            },
          ],
          objectName: /.+/,
          set: {
            'Marketing::date1': '2001-03-04',
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })

    expect(v1ApiRequestStub.notCalled).toBeTruthy()
  })

  it('membership purchase handles "3D Secure required" error on order insert with AV legacy formatted text for pa_request_information', async () => {
    const paymentMethods = [cardPaymentMethod]

    const paymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentMethods[0].cc.type,
      availablePaymentMethods
    )

    const apiRequestStub = sinon.stub()

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        'Questions': questions,
        'Customer': {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
        'Order::lastAddedPayments': { standard: [paymentId] },
      },
    })

    apiRequestStub.onThirdCall().throws(new AvPaymentTokenRequiredError({}))

    apiRequestStub.onCall(3).returns({
      data: {
        pa_request_URL: { standard: 'pa_request_URL' },
        pa_request_information: {
          standard: '00022pa_request_information00007decoded',
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.callCount === 4).toBeTruthy()

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId,
                paymentType: 0,
                Payment: {
                  account_number: 'card number',
                  expiration_date: 'expiry',
                  cvv_code: '{{{cvv}}}',
                  cardholder_name: 'card holder',
                  transaction_amount: 35,
                  pa_response_URL: callbackUrl,
                  swipe_indicator: 'Internet',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: undefined },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])

    expect(apiRequestStub.getCall(3).args).toMatchObject([
      'order',
      {
        req: {
          get: [
            'Payments::paymentId::pa_request_URL',
            'Payments::paymentId::pa_request_information',
          ],
        },
      },
    ])

    expect(result).toMatchObject({
      redirectTo: 'pa_request_URL',
      paymentRequestParams: { pa_request_information: 'decoded' },
      orderId: expect.stringMatching(/.+/),
    })
  })

  it('membership purchase handles Worldpay DDC redirect', async () => {
    const paymentMethods = [cardPaymentMethod]

    const paymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentMethods[0].cc.type,
      availablePaymentMethods
    )

    const apiRequestStub = sinon.stub()

    apiRequestStub.onFirstCall().returns(mockedAvAddCustomerToOrderResponse)

    apiRequestStub.onSecondCall().returns({
      data: {
        'Questions': questions,
        'Customer': {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
        'Order::lastAddedPayments': { standard: [paymentId] },
      },
    })

    apiRequestStub.onThirdCall().throws(new AvPaymentTokenRequiredError({}))

    apiRequestStub.onCall(3).returns({
      data: {
        pa_request_URL: { standard: 'pa_request_URL' },
        pa_request_information: {
          standard: JSON.stringify(pa_request_information_Worldpay_DDC),
        },
      },
    })

    const result = await v2membershipPurchase(
      apiRequestStub,
      paymentMethods,
      membership,
      user,
      deliveryMethodId,
      preferredVenue,
      callbackUrl
    )

    expect(apiRequestStub.callCount === 4).toBeTruthy()

    expect(apiRequestStub.firstCall.args).toMatchObject([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: membership.itemId,
                quantity: membership.quantity,
              },
            },
          ],
          get: ['AvailablePaymentMethods'],
          objectName: /.+/,
        },
      },
      { isV2Explicit: true },
    ])

    expect(apiRequestStub.secondCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: expect.stringMatching(/.+/),
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_id: user.customerId,
                  organization_type: 1,
                },
              },
            },
            {
              method: 'addPayment',
              params: {
                paymentMethodId,
                paymentType: 0,
                Payment: {
                  account_number: 'card number',
                  expiration_date: 'expiry',
                  cvv_code: '{{{cvv}}}',
                  cardholder_name: 'card holder',
                  transaction_amount: 35,
                  pa_response_URL: callbackUrl,
                  swipe_indicator: 'Internet',
                },
              },
            },
          ],
          get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
          session: {
            set: { userAgent: user.userAgent },
          },
        },
      },
      { isTokenExRequest: undefined },
    ])
    expect(apiRequestStub.thirdCall.args).toMatchObject([
      'order',
      {
        req: {
          objectName: /.+/,
          actions: [
            {
              method: 'insert',
              params: { notification: 'correspondence' },
            },
          ],
          get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
          set: {
            'Marketing::data4': 'Bolt',
            'Order::deliverymethod_id': deliveryMethodId,
            'Questions::venueQuestion::answers': preferredVenue,
          },
        },
      },
    ])

    expect(apiRequestStub.getCall(3).args).toMatchObject([
      'order',
      {
        req: {
          get: [
            'Payments::paymentId::pa_request_URL',
            'Payments::paymentId::pa_request_information',
          ],
        },
      },
    ])

    expect(result).toMatchObject({
      redirectTo: 'pa_request_URL',
      paymentRequestParams: {
        ...pa_request_information_Worldpay_DDC.body,
      },
      orderId: /.+/,
      ddcRequired: true,
    })
  })
})

describe('v2membershipPurchaseCallback', () => {
  it('membership purchase handles Worldpay 3DS Challenge redirect', async () => {
    const paymentMethods = [cardPaymentMethod]

    const apiRequestStub = sinon.stub()

    apiRequestStub.onFirstCall().throws(new AvPaymentTokenRequiredError({}))
    apiRequestStub.onSecondCall().resolves({
      data: {
        pa_request_URL: { standard: 'pa_request_URL' },
        pa_request_information: {
          standard: JSON.stringify(
            pa_request_information_Worldpay_3DS_Challenge
          ),
        },
      },
    })

    const result = await v2membershipPurchaseCallback(
      apiRequestStub,
      paymentMethods,
      user,
      orderId,
      paymentId
    )

    expect(apiRequestStub.calledTwice).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'insert',
              params: {
                notification: 'correspondence',
              },
            },
          ],
          get: ['Customer'],
          objectName: orderId,
          set: {
            'Marketing::data4': 'Bolt',
          },
        },
      },
    ])

    expect(apiRequestStub.secondCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: orderId,
          get: [
            'Payments::paymentId::pa_request_URL',
            'Payments::paymentId::pa_request_information',
          ],
        },
      },
    ])

    expect(result).toStrictEqual({
      redirectTo: 'pa_request_URL',
      orderId,
      paymentRequestParams: {
        ...pa_request_information_Worldpay_3DS_Challenge.body,
      },
    })
  })

  it('membership purchase callback successfully inserts order - credit card', async () => {
    const paymentMethods = [cardPaymentMethod]

    const apiRequestStub = sinon.stub()

    apiRequestStub.returns({
      data: {
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchaseCallback(
      apiRequestStub,
      paymentMethods,
      user,
      orderId,
      paymentId
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'insert',
              params: {
                notification: 'correspondence',
              },
            },
          ],
          get: ['Customer'],
          objectName: orderId,
          set: {
            'Marketing::data4': 'Bolt',
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })
  })

  it('membership purchase callback successfully inserts order - direct debit (without collection date)', async () => {
    const paymentMethods = [directDebitPaymentMethod]
    const apiRequestStub = sinon.stub()

    apiRequestStub.returns({
      data: {
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchaseCallback(
      apiRequestStub,
      paymentMethods,
      user,
      orderId,
      paymentId
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'insert',
              params: {
                notification: 'correspondence',
              },
            },
          ],
          get: ['Customer'],
          objectName: orderId,
          set: {
            'Marketing::data4': 'Bolt',
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })
  })

  it('membership purchase callback successfully inserts order and updates date - direct debit (with collection date)', async () => {
    const paymentMethods = [directDebitPaymentMethodWithDate]
    const apiRequestStub = sinon.stub()

    apiRequestStub.returns({
      data: {
        Customer: {
          [user.customerId]: {
            customer_number: { standard: membershipNumber },
          },
        },
      },
    })

    const result = await v2membershipPurchaseCallback(
      apiRequestStub,
      paymentMethods,
      user,
      orderId,
      paymentId
    )

    expect(apiRequestStub.callCount).toBe(3)

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'insert',
              params: {
                notification: 'correspondence',
              },
            },
          ],
          get: ['Customer'],
          objectName: orderId,
          set: {
            'Marketing::data4': 'Bolt',
          },
        },
      },
    ])
    expect(apiRequestStub.secondCall.args).toStrictEqual([
      'customer',
      {
        req: {
          objectName: user.customerSessionId,
          actions: [
            {
              method: 'load',
              params: { 'Customer::customer_id': 'customerId' },
            },
          ],
          get: [],
        },
      },
    ])
    expect(apiRequestStub.thirdCall.args).toStrictEqual([
      'customer',
      {
        req: {
          actions: [
            {
              method: 'update',
            },
          ],
          objectName: user.customerSessionId,
          set: {
            'Marketing::date1': '2001-03-04',
          },
        },
      },
    ])

    expect(result).toMatchObject({ membershipNumber })
  })
})

describe('av-client - parseAvAvailablePaymentMethods', () => {
  const expectedAvAvailablePaymentMethods = [
    {
      paymentMethodId: 'mockedVisaPaymentMethodId',
      description: 'Card - Visa',
    },
    {
      paymentMethodId: 'mockedMastercardPaymentMethodId',
      description: 'Card - Mastercard',
    },
    {
      paymentMethodId: 'mockedMaestroPaymentMethodId',
      description: 'Card - Maestro',
    },
    {
      paymentMethodId: 'mockedAmericanExpressPaymentMethodId',
      description: 'Card - American Express',
    },
  ]

  it('returns an empty array when provided with a faulty response', async () => {
    const faultyResponses = [null, undefined, {}, { data: {} }, '']
    faultyResponses.forEach((faultyResponse) => {
      const availablePaymentMethods =
        parseAvAvailablePaymentMethods(faultyResponse)

      expect(availablePaymentMethods).toStrictEqual([])
    })
  })

  it('returns the correct available payment methods and in the correct format', async () => {
    const availablePaymentMethods = parseAvAvailablePaymentMethods(
      mockedAvAddCustomerToOrderResponse
    )

    expect(availablePaymentMethods).toStrictEqual(
      expectedAvAvailablePaymentMethods
    )
  })
})

describe('av-client - buildAddCustomerToOrderRequest', () => {
  it('returns the correct request when provided with an orderId and a customerNumber', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      customerNumber: 'mockCustomerNumber',
    }

    const request = buildAddCustomerToOrderRequest(mockedParams)

    expect(request).toStrictEqual({
      req: {
        actions: [
          {
            method: 'addCustomer',
            params: {
              Customer: {
                customer_number: mockedParams.customerNumber,
              },
            },
          },
        ],
        get: ['AvailablePaymentMethods', 'ServiceChargeDetails'],
        objectName: mockedParams.orderId,
      },
    })
  })

  it('returns the correct request when provided with an orderId and a customerId', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      customerId: 'mockCustomerId',
    }

    const request = buildAddCustomerToOrderRequest(mockedParams)

    expect(request).toStrictEqual({
      req: {
        actions: [
          {
            method: 'addCustomer',
            params: {
              Customer: {
                customer_id: mockedParams.customerId,
              },
            },
          },
        ],
        get: ['AvailablePaymentMethods', 'ServiceChargeDetails'],
        objectName: mockedParams.orderId,
      },
    })
  })
})

describe('av-client - addCustomerNumberToOrder', () => {
  it('returns the AV available payment methods', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      customerNumber: 'mockCustomerNumber',
    }
    const apiRequestStub = sinon
      .stub()
      .resolves(mockedAvAddCustomerToOrderResponse)

    const result = await addCustomerNumberToOrder(
      apiRequestStub,
      mockedParams.orderId,
      mockedParams.customerNumber
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'addCustomer',
              params: {
                Customer: {
                  customer_number: mockedParams.customerNumber,
                },
              },
            },
          ],
          get: ['AvailablePaymentMethods', 'ServiceChargeDetails'],
          objectName: mockedParams.orderId,
        },
      },
      {
        isV2Explicit: true,
      },
    ])

    expect(result.availablePaymentMethods).toStrictEqual(
      availablePaymentMethods
    )
  })
})

describe('av-client - addCustomerIdToOrder', () => {
  it('returns the AV available payment methods', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      customerId: 'mockCustomerId',
    }
    const apiRequestStub = sinon
      .stub()
      .resolves(mockedAvAddCustomerToOrderResponse)

    const result = await addCustomerIdToOrder(
      apiRequestStub,
      mockedParams.orderId,
      mockedParams.customerId
    )

    expect(
      apiRequestStub.calledWithMatch(
        'order',
        {
          req: {
            actions: [
              {
                method: 'addCustomer',
                params: {
                  Customer: {
                    customer_id: mockedParams.customerId,
                  },
                },
              },
            ],
            get: ['AvailablePaymentMethods', 'ServiceChargeDetails'],
            objectName: mockedParams.orderId,
          },
        },
        {
          isV2Explicit: true,
        }
      )
    ).toBeTruthy()

    expect(result.availablePaymentMethods).toStrictEqual(
      availablePaymentMethods
    )
  })
})

describe('av-client - getBistroAndLoungeAvailabilities', () => {
  const loungePerformanceId = 'loungeId'
  const bistroPerformanceId = 'bistroId'

  it('returns a object with bistros and lounges if all parameters available', async () => {
    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data,
      loungePerformanceId,
      bistroPerformanceId
    )

    expect(availableBistrosAndLounges).toMatchObject(responseMockData)
  })

  it('returns no bistros and lounges if there is no data available', async () => {
    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      [],
      loungePerformanceId,
      bistroPerformanceId
    )

    const responseData = {
      bistros: {
        dbPerformanceId: bistroPerformanceId,
      },
      lounges: {
        dbPerformanceId: loungePerformanceId,
      },
    }

    expect(availableBistrosAndLounges).toStrictEqual(responseData)
  })

  it('returns no bistros if there is bistro id available', async () => {
    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data.filter((item) => item.id === loungePerformanceId),
      loungePerformanceId,
      bistroPerformanceId
    )

    const responseData = {
      bistros: {
        dbPerformanceId: bistroPerformanceId,
      },
      lounges: responseMockData.lounges,
    }

    expect(availableBistrosAndLounges).toStrictEqual(responseData)
  })

  it('returns no lounges if there is lounge id available', async () => {
    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data.filter((item) => item.id === bistroPerformanceId),
      loungePerformanceId,
      bistroPerformanceId
    )

    const responseData = {
      bistros: responseMockData.bistros,
      lounges: {
        dbPerformanceId: loungePerformanceId,
      },
    }

    expect(availableBistrosAndLounges).toMatchObject(responseData)
  })

  it('returns no lounges if loungeId is undefined', async () => {
    const loungePerformanceIdUndefiend = undefined

    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data.filter((item) => item.id === bistroPerformanceId),
      loungePerformanceIdUndefiend,
      bistroPerformanceId
    )

    const responseData = {
      bistros: responseMockData.bistros,
    }

    expect(availableBistrosAndLounges).toMatchObject(responseData)
  })

  it('returns no bistros if bistroId is undefined', async () => {
    const bistroPerformanceIdUndefiend = undefined

    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data.filter((item) => item.id === loungePerformanceId),
      loungePerformanceId,
      bistroPerformanceIdUndefiend
    )

    const responseData = {
      lounges: responseMockData.lounges,
    }

    expect(availableBistrosAndLounges).toMatchObject(responseData)
  })

  it('returns no bistros or lounges when bistroId and loungeId are undefined', async () => {
    const bistroPerformanceIdUndefiend = undefined
    const loungePerformanceIdUndefiend = undefined

    const availableBistrosAndLounges = getBistroAndLoungeAvailabilities(
      data,
      loungePerformanceIdUndefiend,
      bistroPerformanceIdUndefiend
    )

    const responseData = {}

    expect(availableBistrosAndLounges).toMatchObject(responseData)
  })
})

describe('av-client - getSortedPriceZones', () => {
  it('returns sorted price zones', async () => {
    const sortedPriceZones = getSortedPriceZones(priceZonesMockData)

    expect(sortedPriceZones).toStrictEqual(responsePriceZoneMockData)
  })
})

describe('av-client - addAdditionalItemsToOrder', () => {
  it('should make one request', async () => {
    const mockedAdditionalItems = [
      {
        type: 'standard',
        id: '1',
        quantity: 1,
        price: 12,
      },
    ]
    const mockedParams = {
      orderId: 'mockOrderId',
    }
    const apiRequestStub = sinon
      .stub()
      .resolves(mockedAvAddCustomerToOrderResponse)

    await addAdditionalItemsToOrder(
      apiRequestStub,
      mockedParams.orderId,
      mockedAdditionalItems
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
  })

  it('should make three requests', async () => {
    const mockedAdditionalItems = [
      {
        type: 'standard',
        id: '1',
        quantity: 1,
        price: 12,
      },
      {
        type: 'ticket-protection',
        id: '1',
        quantity: 1,
        price: 12,
      },
      {
        type: 'lounge',
        id: '1',
        quantity: 1,
        price: 12.2,
        details: {
          dbPerformanceId: 'A07268A2-679C-47FF-A87C-74E1B0B91E2D',
        },
      },
    ]
    const mockedParams = {
      orderId: 'mockOrderId',
    }
    const Admissions = {}
    const ServiceChargeDetails = {}
    const apiRequestStub = sinon.stub().resolves({
      data: { Admissions, ServiceChargeDetails },
    })

    await addAdditionalItemsToOrder(
      apiRequestStub,
      mockedParams.orderId,
      mockedAdditionalItems
    )

    expect(apiRequestStub.calledThrice).toBeTruthy()
  })

  it('should throw an error when pass unknown type for additional Item in the order', async () => {
    const mockedAdditionalItems = [
      {
        type: 'standard',
        id: '1',
        quantity: 1,
        price: 12,
      },
      {
        type: 'unknown type',
        id: '1',
        quantity: 1,
        price: 12,
      },
    ]
    const mockedParams = {
      orderId: 'mockOrderId',
    }
    const apiRequestStub = sinon.stub().resolves()
    const expectedError = new Error(
      'Unknown type for additional Item in the order'
    )

    await expect(
      addAdditionalItemsToOrder(
        apiRequestStub,
        mockedParams.orderId,
        mockedAdditionalItems
      )
    ).rejects.toStrictEqual(expectedError)
  })

  it('should requests to the api and throw an error when pass wrong details for additional Item in the order', async () => {
    const mockedAdditionalItems = [
      {
        type: 'standard',
        id: '1',
        quantity: 1,
        price: 12,
      },
      {
        type: 'ticket-protection',
        id: '1',
        quantity: 1,
        price: 12,
      },
      {
        type: 'lounge',
        id: '1',
        quantity: 1,
        price: 12.2,
      },
    ]
    const mockedParams = {
      orderId: 'mockOrderId',
    }
    const apiRequestStub = sinon.stub().resolves()
    const errorMsg =
      'Invalid performance id while adding the lounge to the order'
    const expectedError = new Error(
      `${errorMsg}: "${mockedAdditionalItems[0].details}"`
    )

    await expect(
      addAdditionalItemsToOrder(
        apiRequestStub,
        mockedParams.orderId,
        mockedAdditionalItems
      )
    ).rejects.toStrictEqual(expectedError)

    expect(apiRequestStub.calledTwice).toBeTruthy()
  })
})
