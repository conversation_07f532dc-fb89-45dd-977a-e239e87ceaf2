const sinon = require('sinon')

const { v2InsertCustomer, v2GetCustomerNumber } = require('./customer')

const futureDate = new Date()
futureDate.setFullYear(futureDate.getFullYear() + 10)

describe('av-client - v2InsertCustomer', () => {
  const body = {
    fname: '<PERSON>',
    lname: '<PERSON>',
    phone: '+213112345678',
    email: 'johns<PERSON>@testmail.com',
    street: '1asdasd asdasdasd',
    city: 'safasda',
    country: 'Australia',
    zip: '12312',
  }

  it('creates customer in AV for non guest with user data', async () => {
    const apiRequestStub = sinon
      .stub()
      .onCall(0)
      .resolves({
        data: {
          'Customer::default_contact_id': {
            standard: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
            input: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
            display: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
          },
          'Customer::customer_number': {
            standard: '',
            input: '',
            display: '',
          },
        },
      })
    apiRequestStub.onCall(1).resolves({
      version: '7.47.1',
      session: '3B97B772-3284-4770-9285-89CE5A14CE2F',
      data: {
        Customer: {
          customer_id: {
            standard: '3D9DBCC7-E8A4-4B48-B4A5-5E62FCCABA76',
            input: '3D9DBCC7-E8A4-4B48-B4A5-5E62FCCABA76',
            display: '3D9DBCC7-E8A4-4B48-B4A5-5E62FCCABA76',
          },
          default_contact_id: {
            standard: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
            input: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
            display: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
          },
          default_address_id: {
            standard: 'BF0D92CC-2907-4C59-91B2-EF99546FD569',
            input: 'BF0D92CC-2907-4C59-91B2-EF99546FD569',
            display: 'BF0D92CC-2907-4C59-91B2-EF99546FD569',
          },
          customer_number: {
            standard: '12969216',
            input: '12969216',
            display: '12969216',
          },
        },
      },
      return: [
        {
          method: 'insert',
          message: 'Customer created.',
        },
      ],
    })
    const response = await v2InsertCustomer(apiRequestStub, body)

    expect(
      apiRequestStub.calledWith('customer', {
        req: {
          set: {
            Contacts: {
              '+': {
                first_name: body.fname,
                last_name: body.lname,
                email: body.email,
                phone_number1: body.phone,
              },
            },
            Addresses: {
              '+': {
                street: body.street,
                city: body.city,
                state: body.state,
                zip: body.zip,
                country: body.country,
              },
            },
          },
          actions: false,
          get: ['Customer::default_contact_id', 'Customer::customer_number'],
          objectName: 'newCustomer',
        },
      })
    ).toBeTruthy()

    expect(response).toStrictEqual({
      customerNumber: '12969216',
      customerId: '3D9DBCC7-E8A4-4B48-B4A5-5E62FCCABA76',
    })
  })

  it('creates customer in AV for guest users without user data', async () => {
    const apiRequestStub = sinon
      .stub()
      .onCall(0)
      .resolves({
        version: '7.47.1',
        session: 'A038F576-56C1-47E3-8871-865D851F3656',
        data: {
          'Customer::default_contact_id': {
            standard: '17B6F76E-587E-4F2A-B7BC-D8AFA1C50128',
            input: '17B6F76E-587E-4F2A-B7BC-D8AFA1C50128',
            display: '17B6F76E-587E-4F2A-B7BC-D8AFA1C50128',
          },
          'Customer::customer_number': {
            standard: '12969254',
            input: '12969254',
            display: '12969254',
          },
          'Customer::customer_id': {
            standard: 'D8AFA1C50128-587E-4F2A-B7BC-17B6F76E',
            input: 'D8AFA1C50128-587E-4F2A-B7BC-17B6F76E',
            display: 'D8AFA1C50128-587E-4F2A-B7BC-17B6F76E',
          },
        },
        return: [
          {
            method: 'insert',
            message: 'Customer created.',
          },
        ],
      })
    const response = await v2InsertCustomer(apiRequestStub, body, true)

    expect(
      apiRequestStub.calledWith('customer', {
        req: {
          set: {
            Contacts: {
              '+': {
                first_name: body.fname,
                last_name: body.lname,
                email: body.email,
                phone_number1: body.phone,
              },
            },
            Addresses: {
              '+': {
                street: body.street,
                city: body.city,
                state: body.state,
                zip: body.zip,
                country: body.country,
              },
            },
          },
          actions: [{ method: 'insert', params: { expirePassword: 'false' } }],
          get: ['Customer::customer_number', 'Customer::customer_id'],
          objectName: 'newCustomer',
        },
      })
    ).toBeTruthy()

    expect(response).toStrictEqual({
      customerNumber: '12969254',
      customerId: 'D8AFA1C50128-587E-4F2A-B7BC-17B6F76E',
    })
  })

  it('gets customer number from AV', async () => {
    const apiRequestStub = sinon
      .stub()
      .onCall(0)
      .resolves({
        version: '7.47.1',
        session: '3B97B772-3284-4770-9285-89CE5A14CE2F',
        data: {
          Customer: {
            customer_number: {
              standard: '12969216',
              input: '12969216',
              display: '12969216',
            },
          },
        },
        return: [
          {
            method: 'load',
            message: 'Customer loaded.',
          },
        ],
      })

    const response = await v2GetCustomerNumber(
      apiRequestStub,
      'EED6FFBA-D46B-475F-A0E5-9671091EA9AF'
    )

    expect(
      apiRequestStub.calledWith('customer', {
        req: {
          actions: [
            {
              method: 'load',
              params: {
                Customer: {
                  customer_id: 'EED6FFBA-D46B-475F-A0E5-9671091EA9AF',
                },
              },
            },
          ],
          objectName: 'customer',
          get: ['Customer'],
        },
      })
    ).toBeTruthy()

    expect(response).toBe('12969216')
  })
})
