const { AvAtgError } = require('../exceptions')

exports.v2ValidateVoucher = async function (
  apiRequest,
  orderId,
  voucherNumber,
  voucherRedemptionNumber
) {
  const req = {
    set: {
      'Search::object': 'ts_gift_certificate',
      'Query': {
        ResultMember: {
          '+1': {
            name: 'gift_certificate_balance',
            order: '1',
          },
          '+2': {
            name: 'gift_certificate_expiry',
            order: '2',
          },
        },
        Clause: {
          '+1': {
            name: 'gift_certificate_number',
            type: 'matchCondition',
            oper: '=',
            value: voucherNumber,
          },
          '+2': {
            name: 'gift_certificate_redemption_number',
            type: 'matchCondition',
            oper: '=',
            value: voucherRedemptionNumber,
          },
        },
      },
    },
    actions: [
      {
        method: 'search',
        acceptWarnings: [4276],
      },
    ],
    get: ['Result', 'Query::total_records'],
  }

  const {
    data: { Result },
  } = await apiRequest('search', { req })

  if (!Result[1]) {
    throw new AvAtgError(
      'The voucher number / redemption code combination is invalid'
    )
  }
  const {
    gift_certificate_balance: { standard: balanceStr },
    gift_certificate_expiry: {
      standard: [expiryStr],
    },
  } = Result[1]
  const expiry = new Date(expiryStr)
  if (!(expiry > new Date())) {
    throw new AvAtgError('This voucher has expired')
  }

  const balance = parseFloat(balanceStr)
  if (isNaN(balance) || balance <= 0) {
    throw new AvAtgError('There is no balance left on this voucher')
  }
  return { balance, expiry }
}
