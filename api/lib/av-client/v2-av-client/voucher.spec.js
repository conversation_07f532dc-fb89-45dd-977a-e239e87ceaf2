'use strict'

const sinon = require('sinon')

const { AvAtgError } = require('../exceptions')

const { v2ValidateVoucher } = require('./voucher')

const apiRequestStub = sinon.stub()
const futureDate = new Date()
futureDate.setFullYear(futureDate.getFullYear() + 10)

function createApiRequestStub(
  orderTotal,
  isVoucherExpired,
  balance = '100.00'
) {
  apiRequestStub.resolves({
    data: {
      Result: {
        1: {
          gift_certificate_balance: { standard: balance },
          gift_certificate_expiry: {
            standard: [
              isVoucherExpired
                ? '2020-01-01T01:01:01.123'
                : futureDate.toISOString(),
            ],
          },
        },
      },
    },
  })
  return apiRequestStub
}

describe('av-client - v2ValidateVoucher', () => {
  it('validate voucher request returns valid voucher', async () => {
    const orderTotal = '23423'
    const certificateNumber = '123456'
    const redemptionNumber = 'abcdef'
    const apiRequestStub = createApiRequestStub(orderTotal)

    const response = await v2ValidateVoucher(
      apiRequestStub,
      12434,
      certificateNumber,
      redemptionNumber
    )

    expect(response).toStrictEqual({
      balance: 100,
      expiry: new Date(futureDate.toISOString()),
    })
  })

  it('validate voucher request throws error for expired voucher', async () => {
    const orderTotal = '23423'
    const certificateNumber = '123456'
    const redemptionNumber = 'abcdef'
    const apiRequestStub = createApiRequestStub(orderTotal, true)

    const promise = v2ValidateVoucher(
      apiRequestStub,
      12434,
      certificateNumber,
      redemptionNumber
    )

    await expect(promise).rejects.toThrow(
      new AvAtgError('This voucher has expired')
    )
  })

  it('validate voucher request throws error for invalid voucher', async () => {
    const certificateNumber = '123456'
    const redemptionNumber = 'abcdef'
    const apiRequestStub = sinon.stub().resolves({
      data: {
        Result: {},
      },
    })

    const promise = v2ValidateVoucher(
      apiRequestStub,
      12434,
      certificateNumber,
      redemptionNumber
    )

    await expect(promise).rejects.toThrow(
      new AvAtgError(
        'The voucher number / redemption code combination is invalid'
      )
    )
  })

  it('voucher request throws error for no balance voucher', async () => {
    const orderTotal = '23423'
    const certificateNumber = '123456'
    const redemptionNumber = 'abcdef'
    const apiRequestStub = createApiRequestStub(orderTotal, false, '0')

    const promise = v2ValidateVoucher(
      apiRequestStub,
      12434,
      certificateNumber,
      redemptionNumber
    )

    await expect(promise).rejects.toThrow(
      new AvAtgError('There is no balance left on this voucher')
    )
  })
})
