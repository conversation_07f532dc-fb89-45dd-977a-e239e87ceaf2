const { getCustomerMarketingPreferences } = require('./../helpers/user')

exports.v2InsertCustomer = async function (apiRequest, body, isGuest = false) {
  const customerMarketingPreferences = getCustomerMarketingPreferences(body)

  let req = {
    set: {
      ...(customerMarketingPreferences.length > 0 && {
        'Customer::marketing_preferences': customerMarketingPreferences,
      }),
      Contacts: {
        '+': {
          first_name: body.fname,
          last_name: body.lname,
          email: body.email,
          phone_number1: body.phone,
        },
      },
      Addresses: {
        '+': {
          street: body.street,
          city: body.city,
          state: body.state,
          zip: body.zip,
          country: body.country,
        },
      },
    },
    actions: isGuest && [
      {
        method: 'insert',
        params: {
          expirePassword: 'false',
        },
      },
    ],
    get: isGuest
      ? ['Customer::customer_number', 'Customer::customer_id']
      : ['Customer::default_contact_id', 'Customer::customer_number'],
    objectName: 'newCustomer',
  }

  const { data } = await apiRequest('customer', { req })

  if (isGuest) {
    return {
      customerNumber: data?.['Customer::customer_number'].standard,
      customerId: data?.['Customer::customer_id'].standard,
    }
  }

  const defaultContactId = data['Customer::default_contact_id'].standard

  req = {
    set: {
      Users: {
        '+': {
          user_name: body.email,
          password: body.password,
          comp_password: body.comp_password,
          contact_id: defaultContactId,
        },
      },
    },
    actions: [
      {
        method: 'insert',
        params: {
          expirePassword: 'false',
        },
      },
    ],
    objectName: 'newCustomer',
    get: ['Customer', 'Addresses', 'Contacts', 'Users'],
  }

  const {
    data: { Customer },
  } = await apiRequest('customer', { req })

  return {
    customerNumber: Customer?.customer_number.standard,
    customerId: Customer?.customer_id.standard,
  }
}

exports.v2GetCustomerNumber = async function (apiRequest, customerId) {
  const req = {
    actions: [
      {
        method: 'load',
        params: {
          Customer: {
            customer_id: customerId,
          },
        },
      },
    ],
    objectName: 'customer',
    get: ['Customer'],
  }

  const {
    data: { Customer },
  } = await apiRequest('customer', { req })

  return Customer?.customer_number.standard
}
