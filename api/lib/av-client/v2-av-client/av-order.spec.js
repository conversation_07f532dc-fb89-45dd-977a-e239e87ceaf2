const { getPaymentMethodId } = require('../../../config')
const mockedAvAddCustomerToOrderResponse = require('../../test/fixtures/avAddCustomerResponse.json')

const {
  buildMembershipRequest,
  buildInsertRequest,
  buildCallbackInsertRequest,
} = require('./av-order')
const { insertOrder } = require('./action-builders/orderBuilders')
const { parseAvAvailablePaymentMethods } = require('./')

const CARD_PAYMENT_METHOD = {
  paymentType: 'creditCard',
  cc: {
    cvv: 'cvv',
    expires: 'expires',
    name: 'name',
    number: 'number',
    postcode: 'postcode',
    type: 'visa',
    amount: 123,
  },
}
const CUSTOMER_ID = 'customer-id'
const ORDER_ID = 'order-id'
const CALLBACK_URL = 'callback-url'
const QUESTION_INSTANCE_ID = 'question-instance-od'
const PREFERRED_VENUE = 'preferred-venue'
const DELIVERY_METHOD_ID = 'delivery-method-id'
const REFERRER_SOURCE = 'referrer-source'
const SINGLE_USE_PROMOCODE = `single-use-code`
const SHOW_CONFIG = 'show-config'
const USER_AGENT = 'userAgent'

describe('buildMembershipRequest', () => {
  it('constructs expected payload for membership request', () => {
    const availablePaymentMethods = parseAvAvailablePaymentMethods(
      mockedAvAddCustomerToOrderResponse
    )

    const paymentMethodId = getPaymentMethodId(
      CARD_PAYMENT_METHOD.paymentType,
      CARD_PAYMENT_METHOD.cc.type,
      availablePaymentMethods
    )

    const result = buildMembershipRequest(
      [CARD_PAYMENT_METHOD],
      CUSTOMER_ID,
      ORDER_ID,
      CALLBACK_URL,
      availablePaymentMethods,
      USER_AGENT
    )

    const expected = {
      actions: [
        {
          method: 'addCustomer',
          params: {
            Customer: {
              customer_id: CUSTOMER_ID,
              organization_type: 1,
            },
          },
        },
        {
          method: 'addPayment',
          params: {
            Payment: {
              account_number: 'number',
              expiration_date: 'expires',
              cvv_code: 'cvv',
              cardholder_name: 'name',
              transaction_amount: 123,
              pa_response_URL: CALLBACK_URL,
              swipe_indicator: 'Internet',
            },
            paymentMethodId,
            paymentType: 0,
          },
        },
      ],
      get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
      session: {
        set: { userAgent: USER_AGENT },
      },
      objectName: ORDER_ID,
    }

    expect(result).toStrictEqual(expected)
  })
})

describe('buildInsertRequest', () => {
  it('constructs expected payload for insert request', () => {
    const result = buildInsertRequest(
      QUESTION_INSTANCE_ID,
      PREFERRED_VENUE,
      ORDER_ID,
      DELIVERY_METHOD_ID
    )

    const expected = {
      actions: [insertOrder()],
      get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data4': 'Bolt',
        'Order::deliverymethod_id': DELIVERY_METHOD_ID,
        [`Questions::${QUESTION_INSTANCE_ID}::answers`]: PREFERRED_VENUE,
      },
    }

    expect(result).toStrictEqual(expected)
  })

  it('constructs expected payload for insert request when referrerSource provided', () => {
    const result = buildInsertRequest(
      QUESTION_INSTANCE_ID,
      PREFERRED_VENUE,
      ORDER_ID,
      DELIVERY_METHOD_ID,
      REFERRER_SOURCE
    )

    const expected = {
      actions: [insertOrder()],
      get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data15': REFERRER_SOURCE,
        'Marketing::data4': 'Bolt',
        'Order::deliverymethod_id': DELIVERY_METHOD_ID,
        [`Questions::${QUESTION_INSTANCE_ID}::answers`]: PREFERRED_VENUE,
      },
    }

    expect(result).toStrictEqual(expected)
  })

  it('constructs expected payload for insert request when supc is provided', () => {
    const result = buildInsertRequest(
      QUESTION_INSTANCE_ID,
      PREFERRED_VENUE,
      ORDER_ID,
      DELIVERY_METHOD_ID,
      undefined,
      SINGLE_USE_PROMOCODE
    )

    const expected = {
      actions: [insertOrder()],
      get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data16': SINGLE_USE_PROMOCODE,
        'Marketing::data4': 'Bolt',
        'Order::deliverymethod_id': DELIVERY_METHOD_ID,
        [`Questions::${QUESTION_INSTANCE_ID}::answers`]: PREFERRED_VENUE,
      },
    }

    expect(result).toStrictEqual(expected)
  })

  it('constructs expected payload for insert request when showConfig provided', () => {
    const result = buildInsertRequest(
      QUESTION_INSTANCE_ID,
      PREFERRED_VENUE,
      ORDER_ID,
      DELIVERY_METHOD_ID,
      undefined,
      undefined,
      SHOW_CONFIG
    )

    const expected = {
      actions: [insertOrder()],
      get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data17': SHOW_CONFIG,
        'Marketing::data4': 'Bolt',
        'Order::deliverymethod_id': DELIVERY_METHOD_ID,
        [`Questions::${QUESTION_INSTANCE_ID}::answers`]: PREFERRED_VENUE,
      },
    }

    expect(result).toStrictEqual(expected)
  })

  it('constructs expected payload for insert request when referrerSource and showConfig and SUPC are provided', () => {
    const result = buildInsertRequest(
      QUESTION_INSTANCE_ID,
      PREFERRED_VENUE,
      ORDER_ID,
      DELIVERY_METHOD_ID,
      REFERRER_SOURCE,
      SINGLE_USE_PROMOCODE,
      SHOW_CONFIG
    )

    const expected = {
      actions: [insertOrder()],
      get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data15': REFERRER_SOURCE,
        'Marketing::data16': SINGLE_USE_PROMOCODE,
        'Marketing::data17': SHOW_CONFIG,
        'Marketing::data4': 'Bolt',
        'Order::deliverymethod_id': DELIVERY_METHOD_ID,
        [`Questions::${QUESTION_INSTANCE_ID}::answers`]: PREFERRED_VENUE,
      },
    }

    expect(result).toStrictEqual(expected)
  })
})

describe('buildCallbackInsertRequest', () => {
  it('constructs expected payload for callback insert request', () => {
    const result = buildCallbackInsertRequest(ORDER_ID)

    const expected = {
      actions: [insertOrder()],
      get: ['Customer'],
      objectName: ORDER_ID,
      set: {
        'Marketing::data4': 'Bolt',
      },
    }

    expect(result).toStrictEqual(expected)
  })
})
