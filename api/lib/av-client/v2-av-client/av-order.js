const { orderBuilders } = require('./action-builders')

function buildMembershipRequest(
  userPaymentMethods,
  customerId,
  orderId,
  callbackUrl,
  availablePaymentMethods,
  userAgent
) {
  const actions = [
    orderBuilders.addCustomer(customerId),
    ...orderBuilders.addPayments(
      userPaymentMethods,
      callbackUrl,
      availablePaymentMethods
    ),
  ]

  const body = {
    objectName: orderId,
    actions,
    get: ['Questions', 'Customer', 'Order::lastAddedPayments'],
    session: {
      set: { userAgent },
    },
  }

  return body
}

function buildInsertRequest(
  questionInstanceId,
  preferredVenue,
  orderId,
  deliveryMethodId,
  referrerSource,
  singleUsePromoCode,
  showConfig
) {
  const referrerParam = referrerSource
    ? {
        'Marketing::data15': referrerSource,
      }
    : {}
  const singleUsePromoCodeParam = singleUsePromoCode
    ? {
        'Marketing::data16': singleUsePromoCode,
      }
    : {}
  const configParam = showConfig
    ? {
        'Marketing::data17': showConfig,
      }
    : {}
  return {
    objectName: orderId,
    actions: [orderBuilders.insertOrder()],
    get: ['Order::order_number', 'Order::payment_total', 'OrderItems'],
    set: {
      'Marketing::data4': 'Bolt',
      'Order::deliverymethod_id': deliveryMethodId,
      [`Questions::${questionInstanceId}::answers`]: preferredVenue,
      ...referrerParam,
      ...singleUsePromoCodeParam,
      ...configParam,
    },
  }
}

function buildCallbackInsertRequest(orderId) {
  return {
    objectName: orderId,
    actions: [orderBuilders.insertOrder()],
    get: ['Customer'],
    set: {
      'Marketing::data4': 'Bolt',
    },
  }
}

module.exports = {
  buildMembershipRequest,
  buildInsertRequest,
  buildCallbackInsertRequest,
}
