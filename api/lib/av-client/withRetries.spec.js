const sinon = require('sinon')

const { AvNetworkGenericError } = require('./exceptions')
const { withRetries } = require('./withRetries')

const dummyObject = { hello: 'world' }
const wait = (clock) => async (time, error) =>
  new Promise((resolve, reject) => {
    setTimeout(() => (error ? reject(error) : resolve(dummyObject)), time)
    clock.tick(time + 10)
  })

const connectionAbortedError = new AvNetworkGenericError({
  code: 'ECONNABORTED',
})

const debugLogs = false // enable for console logging

describe('withRetries', () => {
  const sandbox = sinon.createSandbox()
  let clock, spiedWait, log
  jest.setTimeout(10000)

  beforeEach(async () => {
    clock = sandbox.useFakeTimers()
    spiedWait = sandbox.spy(wait(clock))
    log = debugLogs
      ? console
      : {
          debug: sandbox.stub(),
          error: sandbox.stub(),
          warn: sandbox.stub(),
          info: sandbox.stub(),
        }
  })

  afterEach(async () => {
    sandbox.restore()
  })

  it('a fast query (without an error) should not be retried', async () => {
    const res = await withRetries(() => spiedWait(100))({ log, retries: 3 })

    expect(res).toStrictEqual(dummyObject)
    expect(spiedWait.calledOnce).toBeTruthy()
  })

  it('an ECONNABORTED error without retry should not be retried', async () => {
    const res = withRetries(() => spiedWait(3000, connectionAbortedError))({
      log,
      retries: 0,
    })

    await expect(res).rejects.toBeTruthy()
    expect(spiedWait.calledOnce).toBeTruthy()
  })

  it('a random error should not be retried', async () => {
    const res = withRetries(() => spiedWait(3000, new AvNetworkGenericError()))(
      {
        log,
        retries: 3,
      }
    )

    await expect(res).rejects.toBeTruthy()

    expect(spiedWait.calledOnce).toBeTruthy()
  })

  it('an ECONNABORTED error should NOT be retried if before timeout threshold', async () => {
    const res = withRetries(() => spiedWait(100, connectionAbortedError))({
      log,
      retries: 3,
    })

    await expect(res).rejects.toBeTruthy()
    expect(spiedWait.calledOnce).toBeTruthy()
  })

  it('an ECONNABORTED error should be retried if after timeout threshold', async () => {
    const res = withRetries(() => spiedWait(3000, connectionAbortedError))({
      log,
      retries: 3,
    })

    await expect(res).rejects.toBeTruthy()
    expect(spiedWait.callCount).toBe(4)
  })
})
