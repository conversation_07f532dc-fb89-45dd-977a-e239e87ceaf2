const withLogging = require('./withLogging')
const redactFieldValues = require('./utils')

jest.mock('./utils')

const log = {
  info: jest.fn(),
  debug: jest.fn(),
  error: jest.fn(),
}

const fn = jest.fn().mockResolvedValue({
  status: 200,
  statusText: 'OK',
  headers: {
    'content-length': 0,
  },
  request: {
    socket: {
      localPort: 1234,
    },
  },
  data: {},
})

const options = {
  retries: 0,
  retry: 0,
  log,
  url: 'https://api.availity.com/v3/health',
  body: {
    foo: 'bar',
  },
}

const logRequest = withLogging(fn)

describe('withLogging', () => {
  describe('success case', () => {
    let result

    beforeEach(async () => {
      redactFieldValues.redactFieldValues.mockReturnValue({
        foo: 'bar',
      })
      result = await logRequest(options)
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('logs the request body at start of request', async () => {
      expect(log.info).toHaveBeenNthCalledWith(
        1,
        {
          avRequestId: expect.any(String),
          redactedBody: {
            foo: 'bar',
          },
        },
        'AV client request start [0/0]: https://api.availity.com/v3/health'
      )
    })

    it('logs the request and response details on success', async () => {
      expect(log.info).toHaveBeenNthCalledWith(
        2,
        {
          avRequestId: expect.any(String),
          status: 200,
          statusText: 'OK',
          requestDuration: expect.any(Number),
          responseSize: 0,
          socket: 1234,
          transactionKey: undefined,
          transactionAttributes: undefined,
        },
        expect.stringContaining('AV client request complete in')
      )
    })

    it('debug logs the request and response details on success', async () => {
      expect(log.debug).toHaveBeenCalledWith(
        {
          avRequestId: expect.any(String),
          request: {
            retries: 0,
            retry: 0,
            log,
            url: 'https://api.availity.com/v3/health',
            body: {
              foo: 'bar',
            },
          },
          responseHeaders: {
            'content-length': 0,
          },
          responseBody: {},
        },
        'AV request: https://api.availity.com/v3/health'
      )
    })

    it('returns the response', async () => {
      expect(result).toStrictEqual({
        status: 200,
        statusText: 'OK',
        headers: {
          'content-length': 0,
        },
        request: {
          socket: {
            localPort: 1234,
          },
        },
        data: {},
      })
    })
  })

  describe('when the request body cannot be redacted', () => {
    beforeEach(async () => {
      const options = {
        retries: 0,
        retry: 0,
        log,
        url: 'https://api.availity.com/v3/health',
      }
      redactFieldValues.redactFieldValues.mockImplementation(() => {
        throw new Error('redact error')
      })
      await logRequest(options)
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('catches the redact error and logs it', async () => {
      expect(log.error).toHaveBeenCalledWith(
        'Error while redacting error config data',
        new Error('redact error')
      )
    })

    it('logs the request body at start of request', async () => {
      expect(log.info).toHaveBeenNthCalledWith(
        1,
        {
          avRequestId: expect.any(String),
          redactedBody: undefined,
        },
        'AV client request start [0/0]: https://api.availity.com/v3/health'
      )
    })

    it('logs the request and response details on success', async () => {
      expect(log.info).toHaveBeenNthCalledWith(
        2,
        {
          avRequestId: expect.any(String),
          status: 200,
          statusText: 'OK',
          requestDuration: expect.any(Number),
          responseSize: 0,
          socket: 1234,
          transactionKey: undefined,
          transactionAttributes: undefined,
        },
        expect.stringContaining('AV client request complete in')
      )
    })
  })
})
