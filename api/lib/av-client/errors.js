class GenericError extends E<PERSON>r {}
tag(GenericError, 'isGenericError')

class ValidationError extends E<PERSON>r {}
tag(ValidationError, 'isValidationError')

class SeatUnavailable extends Error {}
tag(SeatUnavailable, 'isSeatUnavailableError')

class InvalidCardError extends Error {}
tag(InvalidCardError, 'isInvalidCardError')

class ThreeDSRequired extends Error {}
tag(ThreeDSRequired, 'isThreeDSRequiredError')

class NotFound extends Error {}
tag(NotFound, 'isNotFoundError')

function tag(Cls, prop) {
  Object.defineProperty(Cls.prototype, 'name', { value: Cls.name })
  Object.defineProperty(Cls.prototype, 'isAVError', { value: true })
  Object.defineProperty(Cls.prototype, prop, { value: true })
}

module.exports = {
  GenericError,
  ValidationError,
  SeatUnavailable,
  InvalidCardError,
  ThreeDSRequired,
  NotFound,
}
