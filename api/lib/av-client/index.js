'use strict'

const qs = require('querystring')
const { Agent } = require('https')

const axios = require('axios')
const setCookie = require('set-cookie-parser')

const { requestRetries, requestTimeoutNoRetries } = require('../../config')
const { getEnvs } = require('../environment')

const { withRetries } = require('./withRetries')
const withLogging = require('./withLogging')
const avHelpers = require('./helpers')
const v2AvHelpers = require('./v2-av-helpers')
const v2AvClient = require('./v2-av-client')
const {
  getAvErrorFromResponse,
  throwOnAvError,
  AvInvalidLoginError,
} = require('./exceptions')
const { getAvSecrets } = require('./helpers/av-secrets')

const { TOKENEX_ID, TOKENEX_APIKEY, TOKENEX_API_URL } = getEnvs([
  'TOKENEX_ID',
  'TOKENEX_APIKEY',
  'TOKENEX_API_URL',
])

const {
  AV_BASE_URL: AV_ENDPOINT_V2,
  AV_USER: SECRET_AV_UN,
  AV_PASSWORD: SECRET_AV_PW,
  AV_API_KEY: SECRET_AV_API_KEY,
} = getAvSecrets()

const createAgent = () =>
  new Agent({
    keepAlive: true,
    maxSockets: 1,
  })

const client = ({ apiKey = SECRET_AV_API_KEY, log } = {}) => {
  log.debug('Load AV-client')

  const key = `?api_key=${apiKey}`

  const makeRequest = withRetries(withLogging(httpsRequest))

  const openSession = async (
    { log, cookie, session, userId = SECRET_AV_UN, password = SECRET_AV_PW },
    isV2,
    role
  ) => {
    const url = buildUrl('session/authenticateUser', isV2)

    const httpsAgent = createAgent()

    const body = {
      'userid': userId,
      password,
      'SET::role': role || 'Websales',
    }

    const requestOptions = {
      isV2,
      url,
      body,
      httpsAgent,
      log,
      transactionKey: '/External/AV/authenticate',
    }

    if (session) {
      requestOptions.transactionKey = getAvTransactionKey(url, body)
      requestOptions.transactionAttributes = {
        avSessionID: session || 'no-session',
      }
    }
    if (cookie) {
      requestOptions.cookie = cookie
    }

    const { headers, data } = await makeRequest(requestOptions)
    const response = isV2 ? data : data.response
    const error = getAvErrorFromResponse(response, url)

    const { session: retSession } = response

    if (headers['set-cookie']) {
      cookie = setCookie.parse(headers['set-cookie'], {
        decodeValues: true,
        map: false,
        silent: false,
      })
    }

    const apiRequest = createApiRequest({
      cookie,
      session: retSession,
      httpsAgent,
      log,
      isV2,
    })
    const closeSession = createCloseSession({
      cookie,
      session: retSession,
      httpsAgent,
      log,
      isV2,
    })
    const sessionObj = {
      cookie,
      session: retSession,
      apiRequest,
      closeSession,
    }

    if (isV2 && role && role !== 'Websales') {
      await apiRequest('user', {
        req: {
          session: {
            set: {
              role,
            },
          },
        },
      })
    }
    if (error) {
      // needed for password reset
      if (error instanceof AvInvalidLoginError) {
        error.sessionObj = sessionObj
      }
      throw error
    } else {
      return sessionObj
    }
  }

  const restoreSession = ({ cookie, session, log, isV2 }) => {
    const httpsAgent = createAgent()

    return {
      apiRequest: createApiRequest({
        cookie,
        httpsAgent,
        log,
        session,
        isV2,
      }),
      closeSession: createCloseSession({
        cookie,
        httpsAgent,
        log,
        session,
        isV2,
      }),
    }
  }

  return {
    openSession,
    restoreSession,
    ...avHelpers,
    ...v2AvHelpers,
    ...v2AvClient,
  }

  function createRequest({
    retries = 0,
    cookie,
    httpsAgent,
    log,
    session,
    isV2,
  }) {
    return async function avApiRequest(cmd, requestBody, options = {}) {
      const { isTokenExRequest = false, isV2Explicit = false } = options
      const url = buildUrl(cmd, isV2 || isV2Explicit)

      const requestContent = {
        isV2: isV2 || isV2Explicit,
        url: isTokenExRequest ? TOKENEX_API_URL : url,
        cookie,
        body: requestBody,
        httpsAgent,
        log,
        retries,
        transactionKey: getAvTransactionKey(url, requestBody),
        transactionAttributes: {
          avSessionID: session || 'no-session',
        },
      }

      const makeRequestContent = isTokenExRequest
        ? {
            ...requestContent,
            isTokenExRequest,
            TX_URL: url,
            TX_TokenExID: TOKENEX_ID,
            TX_APIKey: TOKENEX_APIKEY,
          }
        : requestContent

      const { data } = await makeRequest(makeRequestContent)

      // check for V1 or V2 shape
      // it might be that v2 never return an error msg
      // with HTTP status 200, if so the check only needs to be made on V1
      // at this point.
      throwOnAvError(data.response || data, url)
      return data
    }
  }

  /*
  configObj: { cookie, httpsAgent, log, session, isV2 }
  */
  function createApiRequest(configObj) {
    const requester = createRequest(configObj)
    requester.retry = createRequest({ ...configObj, retries: requestRetries })
    return requester
  }

  function createCloseSession({ cookie, httpsAgent, log, session, isV2 }) {
    return async function closeSession() {
      const url = buildUrl('session/logout')
      const res = await makeRequest({
        isV2,
        url,
        cookie,
        httpsAgent,
        log,
        transactionKey: '/External/AV/logout',
        transactionAttributes: {
          avSessionID: session || 'no-session',
        },
      })

      const response = isV2 ? res.data : res.data.response
      throwOnAvError(response, url)

      httpsAgent.destroy()
      return res
    }
  }

  function buildUrl(cmd) {
    const api = AV_ENDPOINT_V2
    return `${api}${cmd}${key}`
  }
}

function getAvTransactionKey(url, data) {
  let transactionKey = 'unknownAPI'
  // This is needed for address and contact nodes because
  // they are run against the customerID
  if ('ADDNODE' in data) {
    transactionKey = `add${data.ADDNODE}`
  } else if ('PARAM::paymentMethodId' in data) {
    transactionKey = 'addPayment'
  } else if (data.req?.actions?.[0]?.params?.paymentMethodId) {
    transactionKey = 'addPayment'
  } else if (data.req?.set && 'Order::deliverymethod_id' in data.req.set) {
    transactionKey = 'insertOrder'
  } else if ('SET::Customer::communication_preferences' in data) {
    transactionKey = 'insertCustomer'
  } else {
    transactionKey = url
      .replace(AV_ENDPOINT_V2, '')
      .replace(/\?api_key=.*$/g, '')
    if (data.req?.actions?.[0]?.method) {
      transactionKey += '/' + data.req.actions[0].method
    }
  }
  return `/External/AV/${transactionKey}`
}

const httpsRequest = ({
  isV2,
  log,
  body,
  timeout = requestTimeoutNoRetries,
  cookie = [],
  isTokenExRequest,
  TX_URL,
  TX_TokenExID,
  TX_APIKey,
  ...opts
}) => {
  const contentType =
    isV2 && body && !body.userid
      ? 'application/json'
      : 'application/x-www-form-urlencoded'

  body =
    contentType === 'application/json'
      ? JSON.stringify(body.req)
      : qs.stringify(body)

  const headers = {
    'Content-Type': contentType,
    'Accept': 'application/json',
    'Accept-Encoding': 'gzip',
    'Cache-Control': 'no-cache',
    'Cookie': cookie.map(({ name, value }) => `${name}=${value}`).join('; '),
  }

  const tokenExHeaders = isTokenExRequest && {
    ...headers,
    TX_URL,
    TX_TokenExID,
    TX_APIKey,
  }

  const tokenExBody =
    isTokenExRequest && body.replace(/%7B/g, '{').replace(/%7D/g, '}')

  const options = {
    method: 'POST',
    timeout,
    ...opts,
    data: tokenExBody || body,
    headers: tokenExHeaders || headers,
  }

  return axios(options)
}

module.exports = client
