const { serverLogger } = require('@atg-digital/server-logger-library')

const { requestTimeout, requestTimeoutNoRetries } = require('../../config')

const withRetries = (fn) => async (options) => {
  const { retries = 0, retry = 0, timeout = requestTimeout } = options
  const log = serverLogger.getInstance()

  if (!retries) {
    return fn({ ...options, timeout: requestTimeoutNoRetries })
  }

  // use long timeout on final retry
  const isLastRetry = retry >= retries

  // for now Axios does not have a good timeout reporting,
  // so comparing to timeout to be sure it is one
  const start = Date.now()
  try {
    return await fn({
      ...options,
      retries,
      retry,
      timeout: isLastRetry ? requestTimeoutNoRetries : timeout,
    })
  } catch (e) {
    const end = Date.now()
    const duration = end - start

    if (e.cause.code === 'ECONNABORTED' && duration > timeout) {
      if (retry < retries) {
        log.warn(`AV client request TIMEOUT retry ${retry + 1}/${retries}`)
        return withRetries(fn)({ ...options, retry: retry + 1 })
      } else {
        log.warn(
          `AV client request TIMEOUT, retried ${retries} times, no more retries.`
        )
        throw e
      }
    }
    // ignore error, just rethrow it
    throw e
  }
}

exports.withRetries = withRetries
