const hyperid = require('hyperid')({ urlSafe: true, fixedLength: true })
const { serverLogger } = require('@atg-digital/server-logger-library')

const { mapValues } = require('../utils')
const errors = require('../av-client/errors')
const {
  paymentTypeByReserve,
  avCardDescriptionToCardType,
  avPaymentTypeByReserve,
  boltCreditCardBrands,
  giftCardKeyWords,
} = require('../../config')
const DateFormatter = require('../dateFormatter')

const { AvAtgError } = require('./exceptions')
const { parseQuestions } = require('./utils')

function adyenPaymentType(paymentMethod) {
  const { description = {} } = paymentMethod
  const descriptionStandard = description.standard || ''

  if (descriptionStandard.includes('Card')) {
    return avPaymentTypeByReserve.CARD
  } else if (descriptionStandard.includes('Apple Pay')) {
    return avPaymentTypeByReserve.APPLEPAY
  } else if (descriptionStandard.includes('Google Pay')) {
    return avPaymentTypeByReserve.GOOGLEPAY
  }

  return 'unknown'
}

function parsePaymentMethods(paymentMethods) {
  const uniquePaymentMethods = Object.values(paymentMethods || {}).reduce(
    (availablePaymentMethods, paymentMethod) => {
      const { type = {}, description = {} } = paymentMethod
      if (type && paymentTypeByReserve[type.standard]) {
        const paymentType = paymentTypeByReserve[type.standard]
        const isCardPaymentMethod =
          paymentType === 'CARD' ||
          (description.standard.includes('Card') &&
            !giftCardKeyWords.some((keyWord) =>
              description.standard.toLowerCase().includes(keyWord)
            ))
        const paymentMethodType = isCardPaymentMethod
          ? avCardDescriptionToCardType[description.standard]
          : paymentType === 'ADYEN'
            ? adyenPaymentType(paymentMethod)
            : avPaymentTypeByReserve[paymentType]
        const paymentMethodDetails = {
          paymentMethodId: paymentMethod.payment_method_id.standard,
          type: avPaymentTypeByReserve[paymentType],
          ...(paymentType === 'CARD' && {
            [avPaymentTypeByReserve[paymentType]]: {
              brand: boltCreditCardBrands[paymentMethodType],
            },
          }),
          ...(paymentType === 'ADYEN' && {
            [avPaymentTypeByReserve[paymentType]]: {
              ...(adyenPaymentType(paymentMethod) === 'creditCard' && {
                brand: boltCreditCardBrands[paymentMethodType],
              }),
              type: adyenPaymentType(paymentMethod),
              config: paymentMethod.gateway_config.standard,
            },
          }),
        }

        if (!paymentMethodType) {
          return availablePaymentMethods
        }

        availablePaymentMethods.set(
          `${paymentMethodType}_${paymentType}`,
          paymentMethodDetails
        )
      }

      return availablePaymentMethods
    },
    new Map()
  )

  return Array.from(uniquePaymentMethods.values())
}

function parseUniqueAvailablePaymentMethods(paymentMethods) {
  const uniquePaymentMethods = Object.values(paymentMethods || {}).reduce(
    (availablePaymentMethods, { type }) => {
      if (type && paymentTypeByReserve[type.standard]) {
        availablePaymentMethods.add(paymentTypeByReserve[type.standard])
      }

      return availablePaymentMethods
    },
    new Set()
  )
  return Array.from(uniquePaymentMethods)
}

function parseUniqueCardTypes(paymentMethods) {
  const cardTypeValues = Object.values(paymentMethods || {})
    .filter(
      (paymentMethod) =>
        paymentMethod?.type?.standard === 'CreditCard' ||
        paymentMethod?.type?.standard === 'Adyen'
    )
    .map((paymentMethod) => paymentMethod.description.standard)
    .map((avCardType) => avCardDescriptionToCardType[avCardType])
    .filter(Boolean)

  return [...new Set(cardTypeValues)]
}

function buildBaseAvailabilityBody(performanceId, promoCodeId) {
  const params = {
    performance_ids: performanceId,
    entered_promo_code_id: promoCodeId,
  }

  const actions = [
    {
      method: 'loadAvailability',
      params,
    },
  ]

  return {
    actions,
    get: [
      'availability',
      'pricetypes',
      'pricing',
      `performances::${performanceId}::start_date`,
      `performances::${performanceId}::popupmessage`,
      /** Uplift - dynamicPricingFlag */
      `performances::${performanceId}::data7`,
      /** Uplift - bubblePricing */
      `performances::${performanceId}::multidata2`,
      /** Uplift - serviceFeeThreshold */
      `performances::${performanceId}::comment2`,
      'legends',
    ],
  }
}

const getBaseAvailabilityResponseUnwrapper =
  (performanceId) =>
  ({ data }) => {
    const {
      legends,
      availability,
      pricing,
      pricetypes: priceTypes,
      [`performances::${performanceId}::popupmessage`]: {
        display: popupMessage,
      },
      [`performances::${performanceId}::start_date`]: {
        display: dateString,
        standard: dateTimeFull,
      },
      [`performances::${performanceId}::data7`]: {
        standard: dynamicPricingFlag = '',
      },
      [`performances::${performanceId}::multidata2`]: {
        standard: bubblePricing = '',
      },
      [`performances::${performanceId}::comment2`]: {
        standard: serviceFeeThreshold = '',
      },
    } = data

    delete priceTypes.state
    delete pricing.state
    delete availability.state
    delete legends.state

    const restructuredLegends = Object.values(legends).map((legend) => {
      return {
        id: legend['value_legend_id']
          ? legend['value_legend_id'].standard
          : null,
        group: legend['group'] ? legend['group'].standard : null,
        description: legend['description']
          ? legend['description'].standard
          : null,
      }
    })

    return {
      legends: restructuredLegends,
      availability,
      pricing,
      priceTypes,
      popupMessage,
      performanceDate: {
        dateString: dateString[0],
        dateTimeFull: dateTimeFull[0],
      },
      uplift: {
        /** @type {boolean} */
        dynamicPricing: String(dynamicPricingFlag).toLowerCase() === 'on',
        /** @type {string[]} */
        bubblePricing,
        /** @type {string} */
        serviceFeeThreshold,
      },
    }
  }

async function v2LoadAvailabilityBest(
  apiRequest,
  performanceId,
  promoCodeId = ''
) {
  const req = buildBaseAvailabilityBody(performanceId, promoCodeId)

  const responseUnwrapper = getBaseAvailabilityResponseUnwrapper(performanceId)
  return responseUnwrapper(await apiRequest('map', { req }))
}

async function v2LoadAvailabilityGenAd(apiRequest, performanceId, promoCodeId) {
  const req = buildBaseAvailabilityBody(performanceId, promoCodeId)
  req.get.push('legends')

  const rawResponse = await apiRequest('map', { req })
  const response =
    getBaseAvailabilityResponseUnwrapper(performanceId)(rawResponse)
  response.legends = rawResponse.data.legends
  delete response.legends.state

  return response
}

async function v2loadSeatmapScreen(apiRequest, params) {
  const { performanceId, screenId, promoCodeId } = params
  const req = buildSeatmapBody(performanceId, screenId, promoCodeId)
  const {
    data: {
      admissions,
      pricing,
      pricetypes: priceTypes,
      [`performances::${performanceId}::start_date`]: {
        display: dateString,
        standard: dateTimeFull,
      },
      [`performances::${performanceId}::popupmessage`]: {
        display: popupMessage,
      },
      [`performances::${performanceId}::data7`]: {
        standard: dynamicPricingFlag = '',
      },
      [`performances::${performanceId}::multidata2`]: {
        standard: bubblePricing = '',
      },
      [`performances::${performanceId}::comment2`]: {
        standard: serviceFeeThreshold = '',
      },
      legends,
    },
  } = await apiRequest('map', { req })

  delete legends.state

  const restructuredLegends = Object.values(legends).map((legend) => {
    return {
      id: legend['value_legend_id'] ? legend['value_legend_id'].standard : null,
      group: legend['group'] ? legend['group'].standard : null,
      description: legend['description']
        ? legend['description'].standard
        : null,
    }
  })

  if (!admissions) {
    throw new AvAtgError('AV failure, no admissions data')
  }

  delete admissions.state
  delete pricing.state
  delete priceTypes.state

  return {
    admissions,
    pricing,
    priceTypes,
    popupMessage,
    performanceDate: {
      dateString: dateString[0],
      dateTimeFull: dateTimeFull[0],
    },
    legends: restructuredLegends,
    uplift: {
      /** @type {boolean} */
      dynamicPricing: String(dynamicPricingFlag).toLowerCase() === 'on',
      /** @type {string[]} */
      bubblePricing,
      /** @type {string} */
      serviceFeeThreshold,
    },
  }
}

async function v2loadSeatmapExtract(apiRequest, params) {
  const { performanceId, promoCodeId } = params
  const req = buildSeatmapBodyWithExtract(performanceId, promoCodeId)
  const {
    data: {
      pricing,
      pricetypes: priceTypes,
      [`performances::${performanceId}::start_date`]: {
        display: [dateString],
        standard: [dateTimeFull],
      },
      [`performances::${performanceId}::popupmessage`]: {
        display: popupMessage,
      },
      [`performances::${performanceId}::data7`]: {
        standard: dynamicPricingFlag = '',
      },
      [`performances::${performanceId}::multidata2`]: {
        standard: bubblePricing = '',
      },
      [`performances::${performanceId}::comment2`]: {
        standard: serviceFeeThreshold = '',
      },
      legends,
    },
    return: destructuredAdmissions,
  } = await apiRequest('map', { req })
  if (!destructuredAdmissions[0]?.values[0]?.value) {
    throw new AvAtgError('AV failure, no admissions data')
  }
  const admissions = destructuredAdmissions[0].values[0].value
  delete legends.state

  const restructuredLegends = Object.values(legends).map((legend) => {
    return {
      id: legend['value_legend_id'] ? legend['value_legend_id'].standard : null,
      group: legend['group'] ? legend['group'].standard : null,
      description: legend['description']
        ? legend['description'].standard
        : null,
    }
  })

  delete admissions.state
  delete pricing.state
  delete priceTypes.state

  return {
    admissions,
    pricing,
    priceTypes,
    popupMessage,
    performanceDate: {
      dateString,
      dateTimeFull,
    },
    legends: restructuredLegends,
    uplift: {
      /** @type {boolean} */
      dynamicPricing: String(dynamicPricingFlag).toLowerCase() === 'on',
      /** @type {string[]} */
      bubblePricing,
      /** @type {string} */
      serviceFeeThreshold,
    },
  }
}

async function reserveSeatsV3(apiRequest, { performance, seats, userAgent }) {
  const orderId = hyperid()

  const reqBody = buildReserveBodyv3(performance, seats, orderId, userAgent)

  const { data } = await apiRequest('order', { req: reqBody })

  const {
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    questions,
    serviceChargeDetails,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  } = extractObjectsFromOrder(data)

  return {
    orderId,
    serviceChargeDetails,
    questions,
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  }
}

const extractObjectsFromOrder = (data) => {
  const paymentMethodsMadeAvailable = parsePaymentMethods(
    data.AvailablePaymentMethods
  )
  const paymentMethods = parseUniqueAvailablePaymentMethods(
    data.AvailablePaymentMethods
  )
  const paymentMethodsIds = Object.keys(
    data.AvailablePaymentMethods || {}
  ).filter((key) => key !== 'state')
  const cardTypes = parseUniqueCardTypes(data.AvailablePaymentMethods)
  const admissions = data.Admissions
  const serviceChargeDetails = data.ServiceChargeDetails
  const questions = parseQuestions(data.Questions)
  const deliveryMethodDetails = data.DeliveryMethodDetails
  const order = data.Order

  return {
    serviceChargeDetails,
    questions,
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    cardTypes,
    order,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  }
}

async function reserveBestAvailable(
  apiRequest,
  bestAvailable,
  performanceId,
  userAgent
) {
  const orderId = hyperid()

  const req = buildReserveBestAvailableBody(
    bestAvailable,
    orderId,
    performanceId,
    userAgent
  )
  const resp = await apiRequest('order', { req })
  if (resp.exception) {
    throw new AvAtgError('AV failure, general admission reservation failed')
  }

  const {
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    questions,
    serviceChargeDetails,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  } = extractObjectsFromOrder(resp.data)

  return {
    orderId,
    serviceChargeDetails,
    questions,
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    order,
    cardTypes,
    venueId:
      resp.data[`PerformanceDetails::${[performanceId]}::venue_id`].standard,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  }
}

async function reserveGeneralAdmissions(
  apiRequest,
  generalAdmissions,
  performanceId,
  userAgent
) {
  const orderId = hyperid()

  const req = buildReserveAdmissionBody(
    generalAdmissions,
    orderId,
    performanceId,
    userAgent
  )
  const resp = await apiRequest('order', { req })
  if (resp.exception) {
    throw new AvAtgError('AV failure, general admission reservation failed')
  }

  const {
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    questions,
    serviceChargeDetails,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  } = extractObjectsFromOrder(resp.data)

  return {
    orderId,
    serviceChargeDetails,
    questions,
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  }
}

async function reserveMixedAdmissions(
  apiRequest,
  generalAdmissions,
  seats,
  performanceId,
  userAgent
) {
  const orderId = hyperid()

  const genAdReq = buildReserveAdmissionBody(
    generalAdmissions,
    orderId,
    performanceId,
    userAgent
  )
  const seatReq = buildReserveBodyv3(performanceId, seats, orderId, userAgent)
  const req = {
    actions: [...genAdReq.actions, ...seatReq.actions],
    objectName: orderId,
    get: seatReq.get,
    session: {
      set: { userAgent },
    },
  }

  const resp = await apiRequest('order', { req })
  if (resp.exception) {
    throw new AvAtgError('AV failure, mixed admission reservation failed')
  }

  const {
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    questions,
    serviceChargeDetails,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  } = extractObjectsFromOrder(resp.data)

  return {
    orderId,
    serviceChargeDetails,
    questions,
    admissions,
    deliveryMethodDetails,
    paymentMethods,
    order,
    cardTypes,
    paymentMethodsIds,
    paymentMethodsMadeAvailable,
  }
}

function groupBestAvailableAdmissionsBySameStandAndZone(admissions) {
  return Object.values(
    admissions.reduce((acc, admission) => {
      const key = `${admission.standName}::${admission.priceZone}`

      const ticket = {
        priceTypeId: admission.priceTypeId,
        hiddenPromoCodeId: admission.hiddenPromoCodeId,
        promoCodeId: admission.promoCodeId,
        number: admission.number,
      }

      if (acc[key]) {
        acc[key].tickets.push(ticket)
      } else {
        acc[key] = {
          priceZone: admission.priceZone,
          standName: admission.standName,
          holdOverrideId: admission.holdOverrideId,
          tickets: [ticket],
        }
      }
      return acc
    }, {})
  )
}

function buildReserveBestAvailableBody(
  admissions,
  orderId,
  performanceId,
  userAgent
) {
  const groupedAdmissions =
    groupBestAvailableAdmissionsBySameStandAndZone(admissions)

  return {
    actions: groupedAdmissions.map((admission) => ({
      method: 'getBestAvailable',
      params: {
        perfVector: performanceId,
        reqRows: '1',
        optNum: '1',
        priceValue: admission.priceZone,
        ...(admission.standName && { section_stand: admission.standName }),
        ...(admission.holdOverrideId && {
          holdValue: admission.holdOverrideId,
        }),
        ...admission.tickets.reduce((acc, ticket) => {
          acc[
            `reqNum::${ticket.priceTypeId}${
              ticket.hiddenPromoCodeId || ticket.promoCodeId
                ? '::' + (ticket.hiddenPromoCodeId || ticket.promoCodeId)
                : ''
            }`
          ] = String(ticket.number)
          return acc
        }, {}),
      },
    })),
    objectName: orderId,
    get: [
      'Admissions',
      'ServiceChargeDetails',
      'Questions',
      'DeliveryMethodDetails',
      'AvailablePaymentMethods',
      'Order',
      `PerformanceDetails::${[performanceId]}::venue_id`,
    ],
    session: {
      set: { userAgent },
    },
  }
}

function buildReserveAdmissionBody(
  admissions,
  orderId,
  performanceId,
  userAgent
) {
  return {
    actions: [
      ...admissions.map((admission) => {
        const action = {
          method: 'getBestAvailable',
          params: {
            perfVector: performanceId,
            reqRows: '1',
            optNum: '1',
            priceValue: admission.priceZone,
            section_name: admission.sectionName,
          },
        }
        action.params[
          `reqNum::${admission.priceTypeId}${
            admission.hiddenPromoCodeId || admission.promoCodeId
              ? '::' + (admission.hiddenPromoCodeId || admission.promoCodeId)
              : ''
          }`
        ] = String(admission.number)
        return action
      }),
    ],
    objectName: orderId,
    get: [
      'Admissions',
      'ServiceChargeDetails',
      'Questions',
      'DeliveryMethodDetails',
      'AvailablePaymentMethods',
      'Order',
    ],
    session: {
      set: { userAgent },
    },
  }
}

function buildReserveBodyv3(performanceId, seats, orderId, userAgent) {
  const params = { performanceID: performanceId }

  for (const seatId in seats) {
    const { priceTypeId, promoCodeId, hiddenPromoCodeId } = seats[seatId]

    const addSeat =
      hiddenPromoCodeId || promoCodeId
        ? `addSeatID::${priceTypeId}::${hiddenPromoCodeId || promoCodeId}`
        : `addSeatID::${priceTypeId}`

    const existingKey = params[addSeat]

    if (existingKey) {
      existingKey.push(seatId)
    } else {
      params[addSeat] = [seatId]
    }
  }

  const actions = [{ method: 'manageAdmissions', params }]

  return {
    actions,
    objectName: orderId,
    get: [
      'Admissions',
      'ServiceChargeDetails',
      'Questions',
      'DeliveryMethodDetails',
      'AvailablePaymentMethods',
      'Order',
    ],
    session: {
      set: { userAgent },
    },
  }
}

function mapAdmission(admission) {
  const { section_description: section, seat, row, total } = admission
  const { message } = admission
  message.input = message.display
  return mapValues((v) => v.input, { section, seat, row, total, message })
}

async function v2allocateBestAvailableSeat(apiRequest, orderId, item) {
  const dbPerformanceId = item.dbPerformanceId ?? item.details.dbPerformanceId
  const priceZoneId = item.priceZoneId ?? ''
  const req = buildAvailableAllocationBody(
    dbPerformanceId,
    item.id,
    priceZoneId,
    orderId
  )
  try {
    const {
      data: { Admissions, ServiceChargeDetails },
    } = await apiRequest(
      'order',
      { req },
      {
        isV2Explicit: true,
      }
    )

    delete Admissions.state
    delete ServiceChargeDetails.state
    const seats = mapValues(mapAdmission, Admissions)

    return {
      seats,
      orderId,
    }
  } catch (err) {
    const errMessage = err.message.replace('AV request error: ', '')
    const formattedMessage = errMessage.replace('.. ', '. \n')
    throw new errors.GenericError(formattedMessage || 'An error has occurred')
  }
}

async function v2AddPromoCodeToSession(apiRequest, performanceId, promoCodeId) {
  const req = buildPromoSession(performanceId, promoCodeId)
  const response = await apiRequest('map', { req })

  return response
}

async function v2ClearPromoCodeFromSession(apiRequest, performanceId) {
  const req = buildClearPromoSession(performanceId)
  const response = await apiRequest('map', { req })

  return response
}

function buildPromoSession(performanceId, promoCodeId) {
  const params = {
    performance_ids: performanceId,
    entered_promo_code_id: promoCodeId,
  }

  const actions = [
    {
      method: 'loadAvailability',
      params,
    },
  ]

  const body = {
    actions,
    get: ['Questions', 'pricing'],
  }
  return body
}

function buildClearPromoSession(performanceId) {
  const params = {
    performance_ids: performanceId,
    clearPromoCodes: '1',
  }

  const actions = [
    {
      method: 'loadAvailability',
      params,
    },
  ]

  const body = {
    actions,
    get: [],
  }
  return body
}

function buildSeatmapBody(performanceId, screen, promoCodeId) {
  const params = {
    performance_ids: performanceId,
    screen_id: screen,
    entered_promo_code_id: promoCodeId,
  }

  const actions = [
    {
      method: 'loadMap',
      params,
    },
  ]

  const body = {
    actions,
    get: [
      'pricetypes',
      'pricing',
      'admissions',
      'legends',
      `performances::${performanceId}::start_date`,
      `performances::${performanceId}::popupmessage`,
      `performances::${performanceId}::data7`, // Dynamic Pricing Flag - for Uplift
      `performances::${performanceId}::multidata2`, // Uplift Bubble Pricing
      `performances::${performanceId}::comment2`, // US Service Fee Thresholds
    ],
  }

  return body
}

function buildSeatmapBodyWithExtract(performanceId, promoCodeId) {
  const params = {
    performance_ids: performanceId,
    entered_promo_code_id: promoCodeId,
    templateName: 'Bolt Map Extract',
    contentType: 'application/json',
  }

  const actions = [
    {
      method: 'extract',
      params,
    },
  ]

  const body = {
    actions,
    get: [
      'pricetypes',
      'pricing',
      'admissions',
      'legends',
      `performances::${performanceId}::start_date`,
      `performances::${performanceId}::popupmessage`,
      `performances::${performanceId}::data7`, // Dynamic Pricing Flag - for Uplift
      `performances::${performanceId}::multidata2`, // Uplift Bubble Pricing
      `performances::${performanceId}::comment2`, // US Service Fee Thresholds
    ],
  }

  return body
}

function buildAvailableAllocationBody(
  performanceId,
  avId,
  priceZoneId,
  orderId
) {
  const params = {
    perfVector: performanceId,
    optNum: '1',
    priceValue: priceZoneId,
  }
  params[`reqNum::${avId}`] = '1'

  const actions = [
    {
      method: 'getBestAvailable',
      params,
    },
  ]

  const body = {
    actions,
    objectName: orderId,
    get: ['Order', 'Admissions', 'ServiceChargeDetails', 'Questions'],
  }

  return body
}

function buildPromoCodeSearchBody(promoCodeAccessCode) {
  const actions = [{ method: 'search', acceptWarnings: '4276' }]

  const filter = {
    '+1': {
      name: 'promocode_access_code',
      type: 'matchCondition',
      oper: '=',
      operator_type: 'AND',
      value: promoCodeAccessCode,
    },
    '+2': {
      name: 'OneTimeAccessCode.accesscode_access_code',
      type: 'matchCondition',
      oper: '=',
      operator_type: 'OR',
      value: promoCodeAccessCode,
    },
  }
  const query = {
    ResultMember: {
      '+1': { name: 'promocode_id', order: 1 },
      '+2': { name: 'promocode_description', order: 2 },
      '+3': { name: 'promocode_short_description', order: 3 },
      '+4': { name: 'promocode_start_date', order: 4 },
      '+5': { name: 'promocode_end_date', order: 5 },
      '+6': { name: 'promocode_access_code', order: 6 },
      '+7': { name: 'OneTimeAccessCode.accesscode_access_code', order: 7 },
    },
    Filter: filter,
  }

  const set = {
    'Search::object': 'ts_promotion',
    'Search::page_size': 1,
    'Query::current_page': 1,
    'Query': query,
  }
  const body = {
    set,
    actions,
    get: ['Result'],
  }

  return body
}

function buildBubbleAvailabilitySearchBody(performanceId, bubbleIds) {
  const query = {
    ResultMember: {
      '+1': {
        name: 'admission_hold_value_id',
        order: 1,
        option: 5,
      },
      '+2': {
        name: 'admission_price_value_id',
        order: 2,
        option: 5,
      },
      '+3': {
        name: 'admission_id',
        order: 3,
        function: 101,
      },
      '+4': {
        name: 'OrderAdmission.orderadmission_admission_id',
        order: 4,
        function: 101,
      },
      '+5': {
        name: 'Seat.Section.section_stand',
        order: 5,
      },
    },
    Clause: {
      '+1': {
        name: 'admission_performance_id',
        type: 'matchCondition',
        oper: '=',
        value: performanceId,
      },
      '+2': {
        name: 'admission_hold_value_id',
        type: 'matchCondition',
        oper: '=',
        value: bubbleIds,
      },
    },
  }

  const body = {
    set: {
      'Search::object': 'ts_admission',
      'Search::page_size': 100,
      'Query': query,
    },
    actions: [
      {
        method: 'search',
        acceptWarnings: ['4276'],
      },
    ],
    get: ['Result'],
  }

  return body
}

function extractPromoCodeData(promoCodeData) {
  delete promoCodeData.data.Result.state

  const promoCodeResult = Object.values(promoCodeData.data.Result)[0]
  if (promoCodeResult) {
    const dateFormatter = new DateFormatter()
    const { todayUtc, dateUtc } = dateFormatter

    const startDate = dateUtc(promoCodeResult.promocode_start_date.standard)
    const endDate = promoCodeResult.promocode_end_date.standard
      ? dateUtc(promoCodeResult.promocode_end_date.standard)
      : null

    const isBeforeStartDate = todayUtc().isBefore(startDate)
    const isAfterEndDate = endDate ? todayUtc().isAfter(endDate) : false

    if (isBeforeStartDate || isAfterEndDate) {
      throw new AvAtgError('Invalid Promocode', 400)
    }

    return {
      id: promoCodeResult.promocode_id.standard,
      startDate: promoCodeResult.promocode_start_date.standard,
      endDate: promoCodeResult.promocode_end_date.standard,
      description: promoCodeResult.promocode_description.standard,
      shortDescription: promoCodeResult.promocode_short_description.standard,
      accessCode: promoCodeResult.promocode_access_code.standard,
    }
  } else {
    throw new AvAtgError('Invalid Promocode', 400)
  }
}

function extractBubbleAvailabilityData(bubbleAvailabilityData) {
  const bubbleAvailabilityResult = bubbleAvailabilityData.data.Result

  const data = Object.values(bubbleAvailabilityResult).reduce((acc, bubble) => {
    const holdId = bubble?.admission_hold_value_id?.standard

    if (holdId) {
      const total = Number(bubble?.COUNT_admission_id?.standard || '0')
      const sold = Number(
        bubble?.['COUNT_OrderAdmission.orderadmission_admission_id']
          ?.standard || '0'
      )
      const available = total - sold

      const stand = bubble?.['Seat.Section.section_stand']?.standard
      const zoneId = bubble?.admission_price_value_id?.standard

      if (!acc[holdId]) {
        acc[holdId] = {}
      }

      const previous = acc[holdId]

      // Group all the zones and stands into one item

      const zones = [...(acc[holdId].zones || []), zoneId]
      const uniqueZones = [...new Set(zones)]

      const standAvailability = [
        ...(previous.standAvailability || []),
        { stand, total, available },
      ]

      acc[holdId] = {
        holdId,
        total: total + (previous.total || 0),
        available: available + (previous.available || 0),
        zones: uniqueZones,
        standAvailability,
      }
    }

    return acc
  }, {})

  return data
}

async function v2PromoCodeSearch(apiRequest, promoCodeAccessCode) {
  const req = buildPromoCodeSearchBody(promoCodeAccessCode)
  const response = await apiRequest('search', { req })

  return extractPromoCodeData(response)
}

async function v2BubbleAvailabilitySearch(
  apiRequest,
  performanceId,
  bubbleIds
) {
  try {
    const req = buildBubbleAvailabilitySearchBody(performanceId, bubbleIds)

    const response = await apiRequest('search', { req })
    return extractBubbleAvailabilityData(response)
  } catch (error) {
    throw new AvAtgError(error)
  }
}

async function v2ContentPromoCodeExtract(apiRequest, seriesName, promoCodeId) {
  const params = {
    templateName: 'Bolt Promocode Content Extract JSON',
    contentType: 'application/json',
    series_filter: seriesName,
    match_promo_only: '1',
    entered_promo_code_id: promoCodeId,
  }

  const actions = [
    {
      method: 'extract',
      params,
    },
  ]

  const set = { 'SearchCriteria::object_type_filter': 'P' }

  const body = { actions, set }
  const { return: response } = await apiRequest('content', { req: body })

  return response
}

async function removeServiceCharge(apiRequest, orderId, serviceChargeId) {
  const actions = [
    {
      method: 'manageAdmissions',
    },
  ]

  const body = {
    actions,
    objectName: orderId,
    get: ['OrderPerformanceCharges', 'ServiceChargeDetails', 'Order::due'],
    set: {
      ['ServiceChargeDetails']: { [serviceChargeId]: null },
    },
  }

  await apiRequest('order', { req: body }, { isV2Explicit: true })
}

function buildPaymentClientConfigBody(paymentMethodsIds) {
  return {
    objectName: 'myPaymentMethod',
    actions: [
      ...paymentMethodsIds.map((id) => ({
        method: 'getPaymentClientConfig',
        params: {
          payment_method_id: id,
        },
      })),
    ],
  }
}

function parsePaymentConfig(data) {
  const log = serverLogger.getInstance()

  try {
    return JSON.parse(data.value)
  } catch (error) {
    log.error('fraud-prevention: Payment client config parse failed')
    return {}
  }
}

// Function provided by AV - https://ambassadors.atlassian.net/browse/BE-561
const generateProfilingId = () =>
  'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })

function extractFraudPreventionParamsFromPaymentConfig(data) {
  let organisationId
  let profileDomain

  data.return.forEach((method) =>
    method.values.forEach((val) => {
      const paymentConfig = parsePaymentConfig(val)
      const deviceFingerprint = paymentConfig.config.device_fingerprint

      if (deviceFingerprint?.domain && deviceFingerprint?.organization_id) {
        organisationId = deviceFingerprint.organization_id
        profileDomain = deviceFingerprint.domain
      }
    })
  )

  if (organisationId && profileDomain) {
    return { organisationId, profileDomain, profilingId: generateProfilingId() }
  }
}

async function getFraudPreventionParams(apiRequest, paymentMethodsIds) {
  const reqBody = buildPaymentClientConfigBody(paymentMethodsIds)
  const data = await apiRequest('paymentMethod', { req: reqBody })

  return extractFraudPreventionParamsFromPaymentConfig(data)
}

module.exports = {
  v2LoadAvailabilityGenAd,
  v2LoadAvailabilityBest,
  v2loadSeatmapScreen,
  v2loadSeatmapExtract,
  v2PromoCodeSearch,
  v2ContentPromoCodeExtract,
  v2AddPromoCodeToSession,
  v2allocateBestAvailableSeat,
  v2BubbleAvailabilitySearch,
  buildAvailableAllocationBody,
  extractPromoCodeData,
  reserveSeatsV3,
  reserveGeneralAdmissions,
  reserveMixedAdmissions,
  reserveBestAvailable,
  buildReserveBodyv3,
  buildReserveAdmissionBody,
  removeServiceCharge,
  parsePaymentMethods,
  parseUniqueAvailablePaymentMethods,
  parseUniqueCardTypes,
  buildBubbleAvailabilitySearchBody,
  extractBubbleAvailabilityData,
  v2ClearPromoCodeFromSession,
  extractObjectsFromOrder,
  buildReserveBestAvailableBody,
  getFraudPreventionParams,
  generateProfilingId,
}
