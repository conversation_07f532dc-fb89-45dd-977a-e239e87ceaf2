class AvError extends <PERSON>rror {}
class AvNetworkError extends Av<PERSON>rror {}

/*
 * url is used throughout and may be overkill since the stack trace
 * already points to the AV function where the network response threw
 */

class AvAtgError extends AvError {
  constructor(message, statusCode = 422) {
    super(message)
    Object.assign(this, {
      name: this.constructor.name,
      statusCode,
    })
  }
}

class AvNetworkGenericError extends AvNetworkError {
  constructor(cause, url, statusCode = 502) {
    super('Network AV error')
    Object.assign(this, {
      name: this.constructor.name,
      cause,
      statusCode,
      url,
    })
  }
}

class AvExceptionError extends AvError {
  static getMsg(context, message) {
    if (!context) {
      return message
    }
    // sometimes the context has square brackets, sometimes it doesn't
    const removeSquareBracketsAndTrim = /^\s*\[?|\]?\s*$/g
    const ctx = context.replace(removeSquareBracketsAndTrim, '')
    return `[${ctx}] : ${message}`
  }

  constructor(
    { method, type, severity, number, message, context },
    url,
    statusCode = 400
  ) {
    super()
    Object.assign(this, {
      name: this.constructor.name,
      message: AvExceptionError.getMsg(context, message),
      method,
      severity,
      type,
      number,
      statusCode,
      url,
    })
  }
}

// result = { status: '99', message: 'Session Expired', level: 'error' }
class AvResultError extends AvError {
  constructor(result, url, statusCode = 502) {
    super()
    Object.assign(this, result, {
      name: this.constructor.name,
      statusCode,
      url,
    })
  }
}

class AvPaymentAmountError extends AvAtgError {
  constructor({
    orderTotal,
    grandTotal,
    ticketProtection,
    additionalItems,
    performance,
    seats,
    ...others
  }) {
    super()
    const underOver = grandTotal < orderTotal ? 'overpay' : 'underpay'
    Object.assign(this, others, {
      name: this.constructor.name,
      submittedAmount: orderTotal,
      expectedAmount: grandTotal,
      message: `Attempted to ${underOver}`,
      statusCode: 400,
      ticketProtection,
      additionalItems,
      performance,
      seats,
    })
  }
}

class AvPaymentAvailableMethodsError extends AvAtgError {
  constructor({ performanceId, ...others }) {
    super()
    Object.assign(this, others, {
      name: this.constructor.name,
      message: `No available payment methods`,
      statusCode: 500,
      performanceId,
    })
  }
}

class AvPaymentError extends AvExceptionError {
  constructor(exception, url, statusCode = 402) {
    super(exception, url, statusCode)
  }
}

class AvInvalidLoginError extends AvExceptionError {
  constructor(exception, url, statusCode = 401) {
    super(exception, url, statusCode)
  }
}

class AvPerformanceNotFoundError extends AvExceptionError {
  constructor(exception, url, statusCode = 404) {
    super(exception, url, statusCode)
  }
}

class AvInvalidPromoCodeError extends AvExceptionError {
  constructor(exception, url, statusCode = 404) {
    super(exception, url, statusCode)
  }
}

// when to distinguish/promote an AV exception to its own exception class?
class AvValidationError extends AvExceptionError {
  constructor(exception, url, statusCode = 422) {
    super(exception, url, statusCode)
  }
}

/*
 * change AV's message for this one
 * I dislike this strategy. We'd be better off renaming according to a code
 * in the browser:
 * msg = response.av.exception == 5012
 *  ? 'custom message'
 *  : response.message
 */
class AvEmailAlreadyRegisteredError extends AvValidationError {
  constructor(exception, url) {
    super(exception, url)
    /*
     * just to be sure that there isn't another message with the same
     * exception number
     */
    const matchGroups = this.message.match(/^User ("\S+") already/)
    const email = matchGroups && matchGroups[1]
    if (email) {
      this.message = 'an account already exists for this email'
    } else {
      this.message = ''
    }
  }
}

class AvSeatUnavailableError extends AvExceptionError {
  constructor(exception, url, statusCode = 410) {
    super(exception, url, statusCode)
  }
}

class AvBadSessionError extends AvResultError {
  constructor(result, url) {
    super(result, url, 440)
  }
}

class AvPaymentInvalidCardError extends AvPaymentError {}
class AvPaymentTokenRequiredError extends AvPaymentError {}
class AvPaymentCancelledError extends AvPaymentError {}
class AvPaymentDirectDebitError extends AvPaymentError {}

const exceptionNumberToError = {
  1424: AvPaymentInvalidCardError,
  1484: AvPaymentDirectDebitError,
  1496: AvPaymentInvalidCardError,
  2001: AvInvalidLoginError,
  2018: AvPaymentCancelledError,
  4294: AvPaymentTokenRequiredError,
  5012: AvEmailAlreadyRegisteredError,
  5014: AvPerformanceNotFoundError,
  5063: AvPaymentInvalidCardError,
  5066: AvValidationError,
  5076: AvSeatUnavailableError,
  5362: AvSeatUnavailableError,
  5420: AvPerformanceNotFoundError,
  5501: AvInvalidPromoCodeError,
  5525: AvValidationError,
  6138: AvValidationError,
  6141: AvValidationError,
}

// not sure how reliable this will be
// The status numbers seem too generic
const resultStatusToError = {
  99: AvBadSessionError,
}

function getAvResultError(result, url) {
  const Ctor = resultStatusToError[result.status] || AvResultError
  return new Ctor(result, url)
}

function getAvSessionError(result, url) {
  const Ctor = resultStatusToError[result.errorCode] || AvBadSessionError
  return new Ctor(result, url)
}

function getAvExceptionError(exception, url) {
  const Ctor = exceptionNumberToError[exception.number] || AvExceptionError
  return new Ctor(exception, url)
}

const getAvExceptionFromResponse = ({ exception }, url) =>
  exception && getAvExceptionError(exception, url)

// `result` is not always present
// V2 calls seem to have a `return` key very similar in content to `result`
const getAvResultErrorFromResponse = ({ result }, url) =>
  result && result.status > 0 && getAvResultError(result, url)

const getAvResultErrorForSessionExpired = (response, url) =>
  response.message === 'Session Expired' && getAvSessionError(response, url)

const getAvErrorFromResponse = (response, url) =>
  getAvExceptionFromResponse(response, url) ||
  getAvResultErrorFromResponse(response, url) ||
  getAvResultErrorForSessionExpired(response, url)

const throwOnError = (getError) => (response, url) => {
  const error = getError(response, url)
  if (error) {
    throw error
  }
}

const getAvExceptionErrorResponse = ({ number, message }) => ({
  av: { exception: number },
  message,
})

const getAvStatusErrorResponse = ({ status, message }) => ({
  av: { status },
  message,
})

// map known exception to an error response
const errorResponses = new Map([
  [AvExceptionError, getAvExceptionErrorResponse],
  [AvResultError, getAvStatusErrorResponse],
])

module.exports = {
  AvError,
  AvNetworkError,
  AvNetworkGenericError,
  AvBadSessionError,
  AvAtgError,
  AvExceptionError,
  AvPaymentError,
  AvPaymentAmountError,
  AvInvalidLoginError,
  AvInvalidPromoCodeError,
  AvValidationError,
  AvSeatUnavailableError,
  AvPaymentInvalidCardError,
  AvPaymentTokenRequiredError,
  AvPaymentCancelledError,
  AvPerformanceNotFoundError,
  AvPaymentAvailableMethodsError,
  throwOnAvException: throwOnError(getAvExceptionFromResponse),
  throwOnAvResultError: throwOnError(getAvResultErrorFromResponse),
  throwOnAvError: throwOnError(getAvErrorFromResponse),
  getAvExceptionError,
  getAvResultError,
  getAvResultErrorFromResponse,
  getAvExceptionFromResponse,
  getAvErrorFromResponse,
  errorResponses,
  throwOnError,
}
