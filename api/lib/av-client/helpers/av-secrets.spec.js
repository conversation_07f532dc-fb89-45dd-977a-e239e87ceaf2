const { getAvSecrets } = require('./av-secrets')
const { MissingEnvVarError } = require('./../errors')

describe('getAvSecrets', () => {
  it('should return parsed AV credentials when AV_CREDENTIALS is defined', () => {
    const mockCredentials = {
      AV_API_KEY: 'test-api-key',
      AV_BASE_URL: 'base-url',
      AV_USER: 'test-user',
      AV_PASSWORD: 'test-password',
    }
    process.env['AV_CREDENTIALS'] = JSON.stringify(mockCredentials)
    const result = getAvSecrets()

    expect(result).toStrictEqual(mockCredentials)
  })

  it('should throw MissingEnvVarError when AV_CREDENTIALS is undefined', () => {
    process.env['AV_CREDENTIALS'] = undefined

    expect(() => {
      getAvSecrets()
    }).toThrow(MissingEnvVarError)
  })

  it('should throw SyntaxError when AV_CREDENTIALS is not a valid JSON', () => {
    process.env['AV_CREDENTIALS'] = 'invalidJSON'

    expect(() => {
      getAvSecrets()
    }).toThrow(SyntaxError)
  })
})
