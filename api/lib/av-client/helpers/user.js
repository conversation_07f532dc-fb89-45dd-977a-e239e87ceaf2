const customerPath = 'customer'
const getCustomerFieldsV2 = (sessionId, customerId, fields) => [
  customerPath,
  {
    objectName: sessionId,
    actions: [
      {
        method: 'load',
        params: { 'Customer::customer_id': customerId },
      },
    ],
    get: fields,
  },
]

// expects fields to have a shape like this: { 'Marketing::date1': dateStr }
const setCustomerFieldsV2 = (sessionId, fields) => [
  customerPath,
  {
    objectName: sessionId,
    actions: [{ method: 'update' }],
    set: fields,
  },
]

// why does av-client.httpsRequest expect the body wrapped in the key "req"?
const toReq =
  (buildBody) =>
  (...args) => {
    const [path, body] = buildBody(...args)
    return [path, { req: body }]
  }

const getCustomerFieldsGetReqV2 = toReq(getCustomerFieldsV2)
const getCustomerFieldsSetReqV2 = toReq(setCustomerFieldsV2)

async function loadAndUpdateCustomerFields(
  apiRequest,
  customerId,
  sessionId,
  fields
) {
  // load method required by AV. Stateful architecture?!
  const loadReq = getCustomerFieldsGetReqV2(sessionId, customerId, [])
  const setReq = getCustomerFieldsSetReqV2(sessionId, fields)
  // this request returns undefined
  await apiRequest(...loadReq)
  return await apiRequest(...setReq)
}

const getCustomerMarketingPreferences = (body) => {
  const customerMarketingPreferences = []

  if (body.unsubscribe) {
    customerMarketingPreferences.push('1')
  }

  if (body.optOutUs) {
    customerMarketingPreferences.push('4')
  }

  return customerMarketingPreferences
}

module.exports = {
  getCustomerFieldsGetReqV2,
  getCustomerFieldsSetReqV2,
  loadAndUpdateCustomerFields,
  getCustomerMarketingPreferences,
}
