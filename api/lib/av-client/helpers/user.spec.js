'use strict'

const sinon = require('sinon')

const userHelpers = require('./user')

const customerSessionId = 'aaaaaaaa-nnnnn-yyyyy'
const customerId = 'aaaaaaaa'

describe('av-client - user', () => {
  it('loadAndUpdateCustomerFields - call load and update customer', async () => {
    const apiRequestSpy = sinon.spy()
    const customerFieldToUpdate = { 'Marketing::date1': 'date' }
    await userHelpers.loadAndUpdateCustomerFields(
      apiRequestSpy,
      customerId,
      customerSessionId,
      customerFieldToUpdate
    )
    const firstCall = apiRequestSpy.getCall(0)
    const secondCall = apiRequestSpy.getCall(1)

    expect(firstCall.args).toStrictEqual([
      'customer',
      {
        req: {
          objectName: customerSessionId,
          actions: [
            {
              method: 'load',
              params: {
                'Customer::customer_id': customerId,
              },
            },
          ],
          get: [],
        },
      },
    ])
    expect(secondCall.args).toStrictEqual([
      'customer',
      {
        req: {
          objectName: customerSessionId,
          actions: [
            {
              method: 'update',
            },
          ],
          set: customerFieldToUpdate,
        },
      },
    ])
  })
})
