'use strict'

process.env.TICKET_PROTECTION_MISC_ITEM_ID = 'TICKET_PROTECTION_MISC_ITEM_ID'

const sinon = require('sinon')

const { AvPaymentAmountError } = require('../exceptions')
const { parseAvAvailablePaymentMethods } = require('../v2-av-client')
const { getPaymentMethodId } = require('../../../config')
const mockedAvAddCustomerResponse = require('../../test/fixtures/avAddCustomerResponse.json')

const {
  addPaymentsToOrder,
  v2getRequiredQuestionsForPerformance,
  fetchOrderPaymentPaRequestData,
  setPaymentTokenInfo,
  getPayloadForAdditionalItem,
  addDynamicTicketProtection,
} = require('./index')

const availablePaymentMethods = parseAvAvailablePaymentMethods(
  mockedAvAddCustomerResponse
)

const orderId = 'orderId'
const paymentId = 'paymentId'

function createApiRequestStub(orderTotal) {
  const apiRequestStub = sinon.stub()
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: orderId,
        get: ['Order::grand_total'],
      },
    })
    .resolves({
      data: { 'Order::grand_total': { standard: orderTotal } },
    })

  // profilingId mock
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'mockedVisaPaymentMethodId',
              'paymentType': 0,
              'Payment::account_number': 'number',
              'Payment::expiration_date': 'expires',
              'Payment::cvv_code': '{{{cvv}}}',
              'Payment::cardholder_name': 'name',
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::swipe_indicator': 'Internet',
              'Payment::transaction_amount': '35.50',
              'Payment::profiling_id': 'profilingId',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // card mock visa
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'mockedVisaPaymentMethodId',
              'paymentType': 0,
              'Payment::account_number': 'number',
              'Payment::expiration_date': 'expires',
              'Payment::cvv_code': '{{{cvv}}}',
              'Payment::cardholder_name': 'name',
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::swipe_indicator': 'Internet',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // paypal mock
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
              'paymentType': 0,
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // mock
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
              'paymentType': 0,
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // voucher
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': '2B29E6F8-4F66-45AB-A296-A22092706483',
              'paymentType': 0,
              'Payment::gift_certificate_number': 'voucherNumber',
              'Payment::gift_certificate_redemption_number':
                'voucherRedemption',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // multiple payments
  // card mock mastercard
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'mockedMastercardPaymentMethodId',
              'paymentType': 0,
              'Payment::account_number': 'number',
              'Payment::expiration_date': 'expires',
              'Payment::cvv_code': '{{{cvv}}}',
              'Payment::cardholder_name': 'name',
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::swipe_indicator': 'Internet',
              'Payment::transaction_amount': '15.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  // voucher multiple mock
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': '2B29E6F8-4F66-45AB-A296-A22092706483',
              'paymentType': 0,
              'Payment::gift_certificate_number': 'voucherNumber',
              'Payment::gift_certificate_redemption_number':
                'voucherRedemption',
              'Payment::transaction_amount': '20.00',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  //mock googlepay
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': '8D56EA9F-9EE8-4E00-8593-AA7C7FFAB626',
              'paymentType': 0,
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::cardholder_name': 'GOOGLE PAY USER',
              'Payment::client_to_server_token':
                '{\n    "apiVersion": 2,\n    "apiVersionMinor": 0,\n    "paymentType": "googlePay",\n    "googlePay": {\n        "amount": 35.5\n    },\n    "paymentMethodData": {\n        "type": "CARD",\n        "description": "Visa••••5492",\n        "info": {\n            "cardNetwork": "\\"VISA\\"",\n            "cardDetails": "5492",\n            "assuranceDetails": {\n                "accountVerified": true,\n                "cardHolderAuthenticated": false\n            },\n            "cardHolderName": "GOOGLE PAY USER"\n        },\n        "tokenizationData": {\n            "type": "PAYMENT_GATEWAY",\n            "token": "{\\"signature\\":\\"MEYCIQDYrOkK3BrMN/XA3/ECKF5sU+smYwAdQ27zWge8Yc1sOAIhAIW3FbHi3TidnC45NUa0lsQssIUogH8s2FaMZP8s209H\\",\\"protocolVersion\\":\\"ECv1\\",\\"signedMessage\\":\\"{\\\\\\"encryptedMessage\\\\\\":\\\\\\"Dsk9YZbkBPD8pCLoJc36OijW1CVFFdtdJ6lPMrFFVljdvCusbGY6j7mTBahBhfJMIqismIrL06wJI3p198i1lG4slYtMnp1geehrEEx++Ei062aLmUwQEWIhHtWEVfCudw7CGj6vyIecYr3C7e7552rkBU3h/89tp3ghJ2jngrcPBMoSZw5orGnkZh2ESp1W34rIWeiaMtXsXtjmH24xfRXN5QOtZA4sQbhGmz+VCMdIKwavEa2YPfECnagz9DPoQngsgV+18FrgnvHV7TameYajo7QJ6hqOM21Cztm/Zo/KJW72QbxoVmpbb4dwQPt1lCUoMetpWArFIbpDnHp/1yeDH3VVKj19/Yq/kGCR2bHlB7zj51Hj74WYHAC+Iwuel3V6JTTMSPZQs/2/en61vtLU2/XIVGe6AMAl0LPkozz89rUvDT4LAViU1kPtto9gXzgGCh+2YXircA\\\\\\\\u003d\\\\\\\\u003d\\\\\\",\\\\\\"ephemeralPublicKey\\\\\\":\\\\\\"BPyhURRpEjfR66IfLcBEQLv6LrwIPjn7UZAUh7UFvOlpneWk1X951ZpKZFDbqj4Cc24dqX4t6um4AQ4+KWZOpNA\\\\\\\\u003d\\\\\\",\\\\\\"tag\\\\\\":\\\\\\"aCjaBLS/9RIwfF8JHhZ/t96zLbyC+mNed2OujSQ9zQ8\\\\\\\\u003d\\\\\\"}\\"}"\n        }\n    }\n}',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  //mock applepay
  apiRequestStub
    .withArgs(`order`, {
      req: {
        objectName: 'orderId',
        actions: [
          {
            method: 'addPayment',
            params: {
              'paymentMethodId': 'B784E376-0833-4DCA-BA01-19CEF5BD9503',
              'paymentType': 0,
              'Payment::pa_response_URL': 'callbackURL',
              'Payment::cardholder_name': 'APPLE PAY USER',
              'Payment::client_to_server_token':
                '{\n    "paymentType": "applePay",\n    "applePay": {\n        "amount": 35.5\n    },\n    "cardHolderName": "APPLE PAY USER",\n    "token": {\n        "paymentData": {\n            "data": "tylTHngaVkSnrSMhEEh1ExTnD7N3WbekIWgSzYQicaCWq4i2LVy5+RvceQjgn2dVmGkM/dXKCzlU+YoqsdZMgav/AeSgMowFe5Rx0qCNkeqjhA87g90XR12f6BNX6xeJXKtse2zdUFxiO10mwsF8XrYpRzVwtkbuuoEA4A+rpxKqwrQYcx0YrrM9I+5Z6ERKg3+k6FClaWnIndNe5WjRHks4yFbewSuqBUGOHmbhMmovf/oT806YyQiQOnAoY0Q9VHPizWOh/Wg2shUWij4m82JmR2WFXCRbbCreRuOlMh/35ZLkaGkuZWkEgjsjW75fiiWch5XFLNfCE9gSJd9PtVRlcYU9BuWHvSylUILImkQBoGA9Viz98h3IlQ1ATNTJcBsrMDafO1AUZSOz3kprEddMFiINXwmJP/ANtzCJh88=",\n            "signature": "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",\n            "version": "EC_v1",\n            "header": {\n                "ephemeralPublicKey": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEbQBUi2d2ZNkFP5o6Fw+ANgLbTnXZ7sLnhH2g1FV57UNBrwOy1N7bnSta5kSz4Qv9ifi3AKyanNv49uYHD3NtMQ==",\n                "publicKeyHash": "vOPk2mcX3Ww3RjsYKFuIxWCDAI1aCN/0afaGDW5v0tM=",\n                "transactionId": "3c53f273878a851c132ddbd1857f3b7d1300f6dcaca89ffff3e41236f6fed694"\n            }\n        },\n        "paymentMethod": {\n            "displayName": "Visa 0326",\n            "network": "Visa",\n            "type": "debit"\n        },\n        "transactionIdentifier": "3C53F273878A851C132DDBD1857F3B7D1300F6DCACA89FFFF3E41236F6FED694"\n    }\n}',
              'Payment::transaction_amount': '35.50',
            },
          },
        ],
        get: ['Order::lastAddedPayments'],
      },
    })
    .resolves({
      data: {
        'Order::lastAddedPayments': {
          standard: ['paymentId'],
        },
      },
    })

  return apiRequestStub
}

describe('av-client - addPaymentsToOrder - payment methods', () => {
  it('uses the correct payload for credit card payment', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentDetails = {
      cvv: 'cvv',
      expires: 'expires',
      name: 'name',
      number: 'number',
      postcode: 'postcode',
      type: 'visa',
      amount: orderTotal,
    }
    const paymentMethods = [
      {
        paymentType: 'creditCard',
        txToken: true,
        cc: paymentDetails,
      },
    ]

    const paymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentDetails.type,
      availablePaymentMethods
    )

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: 'orderId',
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': 'mockedVisaPaymentMethodId',
                'paymentType': 0,
                'Payment::account_number': 'number',
                'Payment::expiration_date': 'expires',
                'Payment::cvv_code': '{{{cvv}}}',
                'Payment::cardholder_name': 'name',
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::swipe_indicator': 'Internet',
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: true, isV2: true },
    ])

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': paymentMethodId,
                'paymentType': 0,
                'Payment::account_number': paymentDetails.number,
                'Payment::expiration_date': paymentDetails.expires,
                'Payment::cvv_code': '{{{cvv}}}',
                'Payment::cardholder_name': paymentDetails.name,
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::swipe_indicator': 'Internet',
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: true, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'creditCard',
        methodId: paymentMethodId,
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload for existing profilingId', async () => {
    const orderTotal = 35.5
    const profilingId = 'profilingId'
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentDetails = {
      cvv: 'cvv',
      expires: 'expires',
      name: 'name',
      number: 'number',
      postcode: 'postcode',
      type: 'visa',
      amount: orderTotal,
    }
    const paymentMethods = [
      {
        paymentType: 'creditCard',
        txToken: true,
        cc: paymentDetails,
      },
    ]

    const paymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentDetails.type,
      availablePaymentMethods
    )

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      profilingId
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: 'orderId',
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': 'mockedVisaPaymentMethodId',
                'paymentType': 0,
                'Payment::account_number': 'number',
                'Payment::expiration_date': 'expires',
                'Payment::cvv_code': '{{{cvv}}}',
                'Payment::cardholder_name': 'name',
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::swipe_indicator': 'Internet',
                'Payment::transaction_amount': '35.50',
                'Payment::profiling_id': 'profilingId',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: true, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'creditCard',
        methodId: paymentMethodId,
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload for apple pay payment', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = [
      {
        paymentType: 'applePay',
        applePay: {
          amount: 35.5,
        },
        cardHolderName: 'APPLE PAY USER',
        token: {
          paymentData: {
            data: 'tylTHngaVkSnrSMhEEh1ExTnD7N3WbekIWgSzYQicaCWq4i2LVy5+RvceQjgn2dVmGkM/dXKCzlU+YoqsdZMgav/AeSgMowFe5Rx0qCNkeqjhA87g90XR12f6BNX6xeJXKtse2zdUFxiO10mwsF8XrYpRzVwtkbuuoEA4A+rpxKqwrQYcx0YrrM9I+5Z6ERKg3+k6FClaWnIndNe5WjRHks4yFbewSuqBUGOHmbhMmovf/oT806YyQiQOnAoY0Q9VHPizWOh/Wg2shUWij4m82JmR2WFXCRbbCreRuOlMh/35ZLkaGkuZWkEgjsjW75fiiWch5XFLNfCE9gSJd9PtVRlcYU9BuWHvSylUILImkQBoGA9Viz98h3IlQ1ATNTJcBsrMDafO1AUZSOz3kprEddMFiINXwmJP/ANtzCJh88=',
            signature:
              '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',
            version: 'EC_v1',
            header: {
              ephemeralPublicKey:
                'MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEbQBUi2d2ZNkFP5o6Fw+ANgLbTnXZ7sLnhH2g1FV57UNBrwOy1N7bnSta5kSz4Qv9ifi3AKyanNv49uYHD3NtMQ==',
              publicKeyHash: 'vOPk2mcX3Ww3RjsYKFuIxWCDAI1aCN/0afaGDW5v0tM=',
              transactionId:
                '3c53f273878a851c132ddbd1857f3b7d1300f6dcaca89ffff3e41236f6fed694',
            },
          },
          paymentMethod: {
            displayName: 'Visa 0326',
            network: 'Visa',
            type: 'debit',
          },
          transactionIdentifier:
            '3C53F273878A851C132DDBD1857F3B7D1300F6DCACA89FFFF3E41236F6FED694',
        },
      },
    ]

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': 'B784E376-0833-4DCA-BA01-19CEF5BD9503',
                'paymentType': 0,
                'Payment::cardholder_name': 'APPLE PAY USER',
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::client_to_server_token': JSON.stringify(
                  paymentMethods[0],
                  null,
                  4
                ),
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: undefined, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'applePay',
        methodId: 'B784E376-0833-4DCA-BA01-19CEF5BD9503',
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload for google pay payment', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = [
      {
        apiVersion: 2,
        apiVersionMinor: 0,
        paymentType: 'googlePay',
        googlePay: {
          amount: orderTotal,
        },
        paymentMethodData: {
          type: 'CARD',
          description: 'Visa••••5492',
          info: {
            cardNetwork: '"VISA"',
            cardDetails: '5492',
            assuranceDetails: {
              accountVerified: true,
              cardHolderAuthenticated: false,
            },
            cardHolderName: 'GOOGLE PAY USER',
          },
          tokenizationData: {
            type: 'PAYMENT_GATEWAY',
            token:
              '{"signature":"MEYCIQDYrOkK3BrMN/XA3/ECKF5sU+smYwAdQ27zWge8Yc1sOAIhAIW3FbHi3TidnC45NUa0lsQssIUogH8s2FaMZP8s209H","protocolVersion":"ECv1","signedMessage":"{\\"encryptedMessage\\":\\"Dsk9YZbkBPD8pCLoJc36OijW1CVFFdtdJ6lPMrFFVljdvCusbGY6j7mTBahBhfJMIqismIrL06wJI3p198i1lG4slYtMnp1geehrEEx++Ei062aLmUwQEWIhHtWEVfCudw7CGj6vyIecYr3C7e7552rkBU3h/89tp3ghJ2jngrcPBMoSZw5orGnkZh2ESp1W34rIWeiaMtXsXtjmH24xfRXN5QOtZA4sQbhGmz+VCMdIKwavEa2YPfECnagz9DPoQngsgV+18FrgnvHV7TameYajo7QJ6hqOM21Cztm/Zo/KJW72QbxoVmpbb4dwQPt1lCUoMetpWArFIbpDnHp/1yeDH3VVKj19/Yq/kGCR2bHlB7zj51Hj74WYHAC+Iwuel3V6JTTMSPZQs/2/en61vtLU2/XIVGe6AMAl0LPkozz89rUvDT4LAViU1kPtto9gXzgGCh+2YXircA\\\\u003d\\\\u003d\\",\\"ephemeralPublicKey\\":\\"BPyhURRpEjfR66IfLcBEQLv6LrwIPjn7UZAUh7UFvOlpneWk1X951ZpKZFDbqj4Cc24dqX4t6um4AQ4+KWZOpNA\\\\u003d\\",\\"tag\\":\\"aCjaBLS/9RIwfF8JHhZ/t96zLbyC+mNed2OujSQ9zQ8\\\\u003d\\"}"}',
          },
        },
      },
    ]

    const paymentDetails = {
      cardHolderName: 'GOOGLE PAY USER',
      token: JSON.stringify(paymentMethods[0], null, 4),
      type: 'googlePay',
      amount: orderTotal,
    }

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': '8D56EA9F-9EE8-4E00-8593-AA7C7FFAB626',
                'paymentType': 0,
                'Payment::cardholder_name': paymentDetails.cardHolderName,
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::client_to_server_token': paymentDetails.token,
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: undefined, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'googlePay',
        methodId: '8D56EA9F-9EE8-4E00-8593-AA7C7FFAB626',
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload for paypal payment', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = [
      {
        paymentType: 'paypal',
        payPal: { amount: orderTotal },
      },
    ]

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
                'paymentType': 0,
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: undefined, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'paypal',
        methodId: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload for gift voucher payment', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = [
      {
        paymentType: 'giftCertificate',
        giftCertificate: {
          voucherNumber: 'voucherNumber',
          voucherRedemption: 'voucherRedemption',
          amount: orderTotal,
        },
      },
    ]

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': '2B29E6F8-4F66-45AB-A296-A22092706483',
                'paymentType': 0,
                'Payment::gift_certificate_number': 'voucherNumber',
                'Payment::gift_certificate_redemption_number':
                  'voucherRedemption',
                'Payment::transaction_amount': '35.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: undefined, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'giftCertificate',
        methodId: '2B29E6F8-4F66-45AB-A296-A22092706483',
        amount: '35.50',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload when multiple payment methods', async () => {
    const orderTotal = 35.5
    const voucherAmount = 20
    const cardAmount = orderTotal - voucherAmount

    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentDetails = {
      cvv: 'cvv',
      expires: 'expires',
      name: 'name',
      number: 'number',
      postcode: 'postcode',
      type: 'mastercard',
      amount: cardAmount,
    }
    const paymentMethods = [
      {
        paymentType: 'creditCard',
        txToken: true,
        cc: paymentDetails,
      },
      {
        paymentType: 'giftCertificate',
        giftCertificate: {
          voucherNumber: 'voucherNumber',
          voucherRedemption: 'voucherRedemption',
          amount: voucherAmount,
        },
      },
    ]

    const ccPaymentMethodId = getPaymentMethodId(
      paymentMethods[0].paymentType,
      paymentDetails.type,
      availablePaymentMethods
    )
    const gfPaymentMethodId = getPaymentMethodId(
      paymentMethods[1].paymentType,
      null,
      availablePaymentMethods
    )

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      {},
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': ccPaymentMethodId,
                'paymentType': 0,
                'Payment::account_number': paymentDetails.number,
                'Payment::expiration_date': paymentDetails.expires,
                'Payment::cvv_code': '{{{cvv}}}',
                'Payment::cardholder_name': paymentDetails.name,
                'Payment::pa_response_URL': 'callbackURL',
                'Payment::swipe_indicator': 'Internet',
                'Payment::transaction_amount': '15.50',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: true, isV2: true },
    ])

    expect(apiRequestStub.secondCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          actions: [
            {
              method: 'addPayment',
              params: {
                'paymentMethodId': gfPaymentMethodId,
                'paymentType': 0,
                'Payment::gift_certificate_number': 'voucherNumber',
                'Payment::gift_certificate_redemption_number':
                  'voucherRedemption',
                'Payment::transaction_amount': '20.00',
              },
            },
          ],
          get: ['Order::lastAddedPayments'],
        },
      },
      { isTokenExRequest: undefined, isV2: true },
    ])

    expect(result).toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'creditCard',
        methodId: ccPaymentMethodId,
        amount: '15.50',
      },
      {
        paymentId: 'paymentId',
        type: 'giftCertificate',
        methodId: gfPaymentMethodId,
        amount: '20.00',
      },
    ])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('uses the correct payload without payment methods', async () => {
    const orderTotal = 0
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = []

    const result = await addPaymentsToOrder(
      apiRequestStub,
      orderId,
      'callbackURL',
      {},
      orderTotal,
      logStub,
      'ticketProtection',
      paymentMethods,
      'additionalItems',
      availablePaymentMethods,
      0,
      undefined
    )

    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          get: ['Order::grand_total'],
        },
      },
    ])

    expect(result).toStrictEqual([])

    expect(logStub.error.notCalled).toBeTruthy()
  })

  it('throws an error if unsupported payment method', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentMethods = [
      {
        paymentType: 'cheque',
      },
    ]

    await expect(
      addPaymentsToOrder(
        apiRequestStub,
        'orderId',
        'callbackURL',
        {},
        orderTotal,
        logStub,
        'ticketProtection',
        paymentMethods,
        'additionalItems',
        {},
        undefined
      )
    ).rejects.toThrow('Unsupported payment method: cheque')

    expect(apiRequestStub.notCalled).toBeTruthy()
  })

  it('throws an error if unsupported credit card type', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const paymentDetails = {
      cvv: 'cvv',
      expires: 'expires',
      name: 'name',
      number: 'number',
      postcode: 'postcode',
      type: 'jcb',
      amount: orderTotal,
    }
    const paymentMethods = [
      {
        paymentType: 'creditCard',
        cc: paymentDetails,
      },
    ]

    await expect(
      addPaymentsToOrder(
        apiRequestStub,
        orderId,
        'callbackURL',
        {},
        orderTotal,
        logStub,
        'ticketProtection',
        paymentMethods,
        'additionalItems',
        availablePaymentMethods,
        {},
        undefined
      )
    ).rejects.toThrow('Unsupported payment method: creditCard - jcb')

    expect(apiRequestStub.notCalled).toBeTruthy()
  })
})

describe('av-client - addPaymentsToOrder - total check', () => {
  it('logs an error if sent order total is less than API grand total', async () => {
    const grandTotal = 100.3
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(grandTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const error = new AvPaymentAmountError({
      grandTotal,
      orderTotal,
      ticketProtection: 'ticketProtection',
      additionalItems: 'additionalItems',
    })

    await expect(
      addPaymentsToOrder(
        apiRequestStub,
        orderId,
        'callbackURL',
        {},
        orderTotal,
        logStub,
        'ticketProtection',
        'paypal',
        'additionalItems',
        {},
        undefined
      )
    ).rejects.toThrow(error)
  })

  it('logs an error if sent order total is greater than API grand total', async () => {
    const grandTotal = 2.25
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(grandTotal)
    const logStub = {
      error: sinon.stub(),
    }

    const error = new AvPaymentAmountError({
      grandTotal,
      orderTotal,
      ticketProtection: 'ticketProtection',
      additionalItems: 'additionalItems',
    })

    await expect(
      addPaymentsToOrder(
        apiRequestStub,
        orderId,
        'callbackURL',
        {},
        orderTotal,
        logStub,
        'ticketProtection',
        'paypal',
        'additionalItems',
        {},
        undefined
      )
    ).rejects.toThrow(error)
  })

  it('does not log an error if sent order total is equal to API grand total', async () => {
    const orderTotal = 35.5
    const apiRequestStub = createApiRequestStub(orderTotal)
    const logStub = {
      error: sinon.stub(),
    }

    expect(logStub.error.notCalled).toBeTruthy()

    await expect(
      addPaymentsToOrder(
        apiRequestStub,
        orderId,
        'callbackURL',
        {},
        orderTotal,
        logStub,
        'ticketProtection',
        'paypal',
        'additionalItems',
        {},
        undefined
      )
    ).resolves.toStrictEqual([
      {
        paymentId: 'paymentId',
        type: 'paypal',
        methodId: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
        amount: '35.50',
      },
    ])
  })
})

describe('av-client - v2getRequiredQuestionsForPerformance', () => {
  it('makes the expected request and parses the response', async () => {
    const performanceId = 'performanceId'
    const apiRequestStub = sinon.stub().resolves({
      data: {
        Result: {
          1: {
            performancequestion_question_id: {
              standard: '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E',
            },
            performancequestion_required: {
              standard: '0',
            },
          },
          state: '0',
        },
      },
    })

    const result = await v2getRequiredQuestionsForPerformance(
      apiRequestStub,
      performanceId
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(
      apiRequestStub.calledWithExactly('search', {
        req: {
          set: {
            'Search::object': 'ts_performance_question',
            'Search::page_size': 50,
            'Search::extract_max_rows': 100,
            'Query': {
              ResultMember: {
                '+1': {
                  name: 'performancequestion_question_id',
                  order: '1',
                },
                '+2': {
                  name: 'performancequestion_required',
                  order: '2',
                },
              },
              Clause: {
                '+1': {
                  name: 'performancequestion_performance_id',
                  type: 'matchCondition',
                  oper: '=',
                  value: performanceId,
                },
              },
            },
          },
          actions: [
            {
              method: 'search',
              acceptWarnings: [4276],
            },
          ],
          get: ['Result'],
        },
      })
    ).toBeTruthy()
    expect(result).toStrictEqual({
      '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E': false,
    })
  })
})

describe('av-client - fetchOrderPaymentPaRequestData', () => {
  it('returns an object with the values for pa_request_URL and pa_request_information', async () => {
    const apiRequestStub = sinon.stub()

    apiRequestStub.returns({
      data: {
        pa_request_URL: { standard: 'pa_request_URL' },
        pa_request_information: { standard: 'pa_request_information' },
      },
    })

    const result = await fetchOrderPaymentPaRequestData(
      orderId,
      paymentId,
      apiRequestStub
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          objectName: orderId,
          get: [
            `Payments::${paymentId}::pa_request_URL`,
            `Payments::${paymentId}::pa_request_information`,
          ],
        },
      },
    ])
    expect(result).toStrictEqual({
      pa_request_URL: 'pa_request_URL',
      pa_request_information: 'pa_request_information',
    })
  })
})

describe('av-client - setPaymentTokenInfo', () => {
  it('properly sets the pa_response_information on the order payments and also fetches the lastAddedPayment', async () => {
    const lastAddedPayment = 'last added payment'
    const apiRequestStub = sinon.stub().resolves({
      data: { ['Order::lastAddedPayments']: { standard: [lastAddedPayment] } },
    })
    const avStr = 'avStr'

    const result = await setPaymentTokenInfo(apiRequestStub, orderId, avStr)

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      `order`,
      {
        req: {
          objectName: orderId,
          set: {
            'Payments::pa_response_information': avStr,
          },
          get: ['Order::lastAddedPayments'],
        },
      },
    ])
    expect(result).toStrictEqual(lastAddedPayment)
  })
})

describe('av-client - getPayloadForAdditionalItem', () => {
  it('returns the correct request when provided with an orderId and a quantity and id', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      item: {
        quantity: 1,
        id: 'standard',
      },
    }

    const result = getPayloadForAdditionalItem(
      mockedParams.orderId,
      mockedParams.item
    )

    expect(result).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: mockedParams.item.id,
                quantity: mockedParams.item.quantity.toString(),
              },
            },
          ],
          objectName: mockedParams.orderId,
        },
      },
    ])
  })
})

describe('av-client - getPayloadForAdditionalItem with price', () => {
  it('returns the correct request when provided with an orderId and a quantity, id, price', async () => {
    const mockedParams = {
      orderId: 'mockOrderId',
      item: {
        quantity: 1,
        id: 'standard',
        price: 2,
      },
    }

    const result = getPayloadForAdditionalItem(
      mockedParams.orderId,
      mockedParams.item
    )

    expect(result).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: mockedParams.item.id,
                quantity: mockedParams.item.quantity.toString(),
                price: mockedParams.item.price.toString(),
              },
            },
          ],
          objectName: mockedParams.orderId,
        },
      },
    ])
  })
})

describe('av-client - addDynamicTicketProtection', () => {
  it('returns the correct request when provided with an orderId and a quantity, id, price', async () => {
    const apiRequestStub = sinon.stub().resolves({
      data: {
        Result: {
          1: {
            performancequestion_question_id: {
              standard: '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E',
            },
            performancequestion_required: {
              standard: '0',
            },
          },
          state: '0',
        },
      },
    })

    const orderId = 'mockOrderId'
    const ticketProtectionFee = 2.49

    const mocketSeats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    const mockedGeneralAdmissions = [
      {
        number: 1,
      },
    ]

    const mockedParams = {
      orderId: 'mockOrderId',
      item: {
        quantity: 1,
        id: 'TICKET_PROTECTION_MISC_ITEM_ID',
        price: 2 * 2.49,
      },
    }

    await addDynamicTicketProtection(
      apiRequestStub,
      orderId,
      mocketSeats,
      mockedGeneralAdmissions,
      ticketProtectionFee
    )

    expect(apiRequestStub.calledOnce).toBeTruthy()
    expect(apiRequestStub.firstCall.args).toStrictEqual([
      'order',
      {
        req: {
          actions: [
            {
              method: 'manageItems',
              params: {
                itemId: mockedParams.item.id,
                quantity: '1',
                price: (2 * 2.49).toString(),
              },
            },
          ],
          objectName: mockedParams.orderId,
        },
      },
      { isV2Explicit: true },
    ])
  })
})
