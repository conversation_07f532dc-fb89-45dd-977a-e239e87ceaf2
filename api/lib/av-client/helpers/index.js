/**
 * This file is a temporary home for these functions, end goal is to tie them up
 * with av-client. For now, av-client just imports them and re-exports.
 */
const { AvPaymentAmountError } = require('../exceptions')
const { getPaymentMethodId, getPaymentType } = require('../../../config')
const { countSeats } = require('../../../services/reserve/utils')
const { parseQuestionsRequired } = require('../utils')

const userHelpers = require('./user')

const TICKET_PROTECTION_MISC_ITEM_ID =
  process.env.TICKET_PROTECTION_MISC_ITEM_ID

function getPayloadForAdditionalItem(orderId, { id, quantity, price }) {
  let priceParam = {}
  if (price) {
    priceParam = { price: price.toString() }
  }
  return [
    'order',
    {
      req: {
        actions: [
          {
            method: 'manageItems',
            params: {
              itemId: id,
              quantity: quantity.toString(),
              ...priceParam,
            },
          },
        ],
        objectName: orderId,
      },
    },
  ]
}

async function addDynamicTicketProtection(
  apiRequest,
  orderId,
  seats,
  generalAdmissions,
  ticketProtectionFee,
  bestAvailable
) {
  const { paidNumSeats } = countSeats(seats, generalAdmissions, bestAvailable)

  const [path, payload] = getPayloadForAdditionalItem(orderId, {
    id: TICKET_PROTECTION_MISC_ITEM_ID,
    quantity: '1',
    price: (paidNumSeats * ticketProtectionFee).toFixed(2),
  })

  await apiRequest(path, payload, { isV2Explicit: true })
}

const ccParams = (cc, callbackURL) => ({
  'Payment::account_number': cc.number,
  'Payment::expiration_date': cc.expires,
  'Payment::cvv_code': cc.cvv,
  'Payment::cardholder_name': cc.name,
  'Payment::pa_response_URL': callbackURL,
  'Payment::swipe_indicator': 'Internet',
})

const paypalParams = (_, callbackURL) => ({
  'Payment::pa_response_URL': callbackURL,
})

const applePayParams = (_, callbackURL, userPaymentMethod) => ({
  'Payment::pa_response_URL': callbackURL,
  'Payment::cardholder_name': userPaymentMethod.cardHolderName,
  'Payment::client_to_server_token': JSON.stringify(userPaymentMethod, null, 4),
})

const googlePayParams = (_, callbackURL, userPaymentMethod) => ({
  'Payment::pa_response_URL': callbackURL,
  'Payment::cardholder_name':
    userPaymentMethod.paymentMethodData.info.cardHolderName,
  'Payment::client_to_server_token': JSON.stringify(userPaymentMethod, null, 4),
})

const giftCertificateParams = (cc, callbackURL, userPaymentMethod) => ({
  'Payment::gift_certificate_number':
    userPaymentMethod.giftCertificate.voucherNumber,
  'Payment::gift_certificate_redemption_number':
    userPaymentMethod.giftCertificate.voucherRedemption,
  'Payment::transaction_amount': userPaymentMethod.giftCertificate.amount,
})

const paymentParamsFor = {
  creditCard: ccParams,
  paypal: paypalParams,
  giftCertificate: giftCertificateParams,
  googlePay: googlePayParams,
  applePay: applePayParams,
}

async function addPayment(
  apiRequest,
  orderId,
  callbackURL,
  cc = {},
  paymentAmount,
  log,
  userPaymentMethod,
  availablePaymentMethods,
  profilingId
) {
  userPaymentMethod.paymentType =
    userPaymentMethod.paymentType.charAt(0).toLowerCase() +
    userPaymentMethod.paymentType.slice(1)

  const paymentMethodId = getPaymentMethodId(
    userPaymentMethod.paymentType,
    cc.type,
    availablePaymentMethods
  )

  if (paymentMethodId === undefined) {
    throw Error(
      `Unsupported payment method: ${userPaymentMethod.paymentType}${
        cc.type ? ` - ${cc.type}` : ''
      }`
    )
  }

  const hasTokenExToken = userPaymentMethod.txToken
  if (hasTokenExToken) {
    cc.cvv = '{{{cvv}}}'
  }

  const paymentParams = paymentParamsFor[userPaymentMethod.paymentType](
    cc,
    callbackURL,
    userPaymentMethod
  )

  const paymentType = getPaymentType(userPaymentMethod.paymentType)

  paymentAmount = (
    userPaymentMethod[paymentType].amount || paymentAmount
  ).toFixed(2)

  const paymentData = {
    objectName: orderId,
    actions: [
      {
        method: 'addPayment',
        params: {
          paymentMethodId,
          paymentType: 0,
          ...paymentParams,
          ...(paymentAmount > 0 && {
            'Payment::transaction_amount': paymentAmount,
          }),
          ...(profilingId && {
            'Payment::profiling_id': profilingId,
          }),
        },
      },
    ],
    get: ['Order::lastAddedPayments'],
  }

  try {
    const response = await apiRequest(
      `order`,
      { req: paymentData },
      {
        isTokenExRequest: hasTokenExToken,
        isV2: true,
      }
    )

    return {
      paymentId: response.data['Order::lastAddedPayments'].standard[0],
      type: userPaymentMethod.paymentType,
      methodId: paymentMethodId,
      amount: paymentAmount,
    }
  } catch (error) {
    log.error(error)
  }
}

async function addPaymentsToOrder(
  apiRequest,
  orderId,
  callbackURL,
  cc = {},
  orderTotal,
  log,
  ticketProtection,
  userPaymentMethods,
  additionalItems,
  availablePaymentMethods,
  sanitiziedReqDetails = {},
  profilingId
) {
  const userMethods = Array.isArray(userPaymentMethods)
    ? userPaymentMethods
    : [{ paymentType: userPaymentMethods, cc, payPal: {} }]

  const paymentIds = await Promise.all(
    userMethods.map((userMethod) =>
      addPayment(
        apiRequest,
        orderId,
        callbackURL,
        userMethod.cc,
        orderTotal,
        log,
        userMethod,
        availablePaymentMethods,
        profilingId
      )
    )
  )

  const response = await apiRequest(`order`, {
    req: {
      objectName: orderId,
      get: ['Order::grand_total'],
    },
  })

  const grandTotal = parseFloat(response?.data?.['Order::grand_total'].standard)

  if (grandTotal !== orderTotal) {
    throw new AvPaymentAmountError({
      grandTotal,
      orderTotal,
      ticketProtection,
      additionalItems,
      sanitiziedReqDetails,
    })
  }

  return paymentIds
}

async function fetchOrderDetails(apiRequest, orderId) {
  const { data } = await apiRequest(`order`, {
    req: {
      objectName: orderId,
      get: [
        'Order::grand_total',
        'Order::deliverymethod_id',
        'AvailablePaymentMethods',
        'Customer',
        'Payments',
      ],
    },
  })

  const orderTotal = data['Order::grand_total'].standard
  const deliveryId = data['Order::deliverymethod_id'].standard
  const availablePaymentMethods = data['AvailablePaymentMethods']
  const customer = data['Customer']
  const payments = data['Payments']

  return {
    orderTotal,
    deliveryId,
    customer,
    availablePaymentMethods,
    payments,
  }
}

async function setPaymentTokenInfo(apiRequest, orderId, avStr) {
  const { data } = await apiRequest(`order`, {
    req: {
      objectName: orderId,
      set: {
        'Payments::pa_response_information': avStr,
      },
      get: ['Order::lastAddedPayments'],
    },
  })

  return data['Order::lastAddedPayments'].standard[0]
}

async function v2getRequiredQuestionsForPerformance(apiRequest, performanceId) {
  const {
    data: { Result },
  } = await apiRequest('search', {
    req: {
      set: {
        'Search::object': 'ts_performance_question',
        'Search::page_size': 50,
        'Search::extract_max_rows': 100,
        'Query': {
          ResultMember: {
            '+1': {
              name: 'performancequestion_question_id',
              order: '1',
            },
            '+2': {
              name: 'performancequestion_required',
              order: '2',
            },
          },
          Clause: {
            '+1': {
              name: 'performancequestion_performance_id',
              type: 'matchCondition',
              oper: '=',
              value: performanceId,
            },
          },
        },
      },
      actions: [
        {
          method: 'search',
          acceptWarnings: [4276],
        },
      ],
      get: ['Result'],
    },
  })

  return parseQuestionsRequired(Result)
}

async function fetchOrderPaymentPaRequestData(orderId, paymentId, apiRequest) {
  const result = await apiRequest(`order`, {
    req: {
      objectName: orderId,
      get: [
        `Payments::${paymentId}::pa_request_URL`,
        `Payments::${paymentId}::pa_request_information`,
      ],
    },
  })

  const mappedData = Object.keys(result.data).reduce((acc, key) => {
    if (key.includes('pa_request_URL')) {
      acc.pa_request_URL = result.data[key]
    } else if (key.includes('pa_request_information')) {
      acc.pa_request_information = result.data[key]
    }
    return acc
  }, {})

  return {
    pa_request_URL: mappedData.pa_request_URL.standard,
    pa_request_information: mappedData.pa_request_information.standard,
  }
}

module.exports = {
  getPayloadForAdditionalItem,
  addDynamicTicketProtection,
  addPaymentsToOrder,
  fetchOrderDetails,
  setPaymentTokenInfo,
  v2getRequiredQuestionsForPerformance,
  fetchOrderPaymentPaRequestData,
  ...userHelpers,
}
