const qs = require('querystring')
const { URL } = require('url')
const crypto = require('crypto')

const { throwOnAvError, AvNetworkGenericError } = require('./exceptions')
const { redactFieldValues } = require('./utils')

function throwNarrowerError(error, url) {
  const { response } = error

  if (response && response.data) {
    throwOnAvError(response.data, url)
  }

  // if `throwOnAvError` doesn't throw lets throw a generic error
  throw new AvNetworkGenericError(error, url)
}

const removeApiKeyUrlParam = (url) => {
  try {
    const newUrl = new URL(url)
    newUrl.searchParams.delete('api_key')
    return newUrl.href
  } catch (_) {
    return url
  }
}

// the underlying httpRequest/axios will throw when ! (statusCode >= 200 && statusCode < 300)
// https://github.com/axios/axios/blob/1b07fb9365d38a1a8ce7427130bf9db8101daf09/lib/defaults.js#L78
async function logRequest(fn, options) {
  const { retries = 0, retry = 0, log, url } = options
  const sanitizedUrl = removeApiKeyUrlParam(url)
  const avRequestId = crypto
    .randomBytes(10)
    .toString('base64')
    .replace(/[^a-zA-Z0-9]/g, '')
    .slice(0, 10)
  const startTime = Date.now()

  try {
    let redactedBody
    try {
      redactedBody = redactFieldValues(options.body)
    } catch (redactErr) {
      log.error('Error while redacting error config data', redactErr)
    }
    log.info(
      {
        avRequestId,
        redactedBody,
      },
      `AV client request start [${retry}/${retries}]: ${sanitizedUrl}`
    )

    const resp = await fn(options)
    const duration = Date.now() - startTime
    log.info(
      {
        avRequestId,
        status: resp.status,
        statusText: resp.statusText,
        requestDuration: duration,
        responseSize: resp.headers['content-length'],
        socket: resp.request.socket.localPort,
        transactionKey: options.transactionKey,
        transactionAttributes: options.transactionAttributes,
      },
      `AV client request complete in ${duration}ms: ${sanitizedUrl}`
    )
    log.debug(
      {
        avRequestId,
        request: options,
        responseHeaders: resp.headers,
        responseBody: resp.data,
      },
      `AV request: ${sanitizedUrl}`
    )
    return resp
  } catch (error) {
    const configData = error?.config?.data

    if (configData) {
      try {
        const parser =
          error.config?.headers['Content-Type'] === 'application/json'
            ? JSON
            : qs

        // Reassign `error.config` / delete non-essential data from being
        // logged, reducing log ingestion
        const { headers, data, url } = error.config
        error.config = { headers, data, url }

        const objectWithRedactions = redactFieldValues(parser.parse(configData))

        error.config.data = objectWithRedactions

        // Extract `error.config.data.actions` values
        if (Array.isArray(error.config.data?.actions)) {
          error.config.data.actions = error.config.data.actions[0]
        }
      } catch (redactErr) {
        log.error({ redactErr }, 'Error while redacting error config data')
      }
    }

    const duration = Date.now() - startTime

    log.error(error)

    log.error(
      {
        avRequestId,
        requestDuration: duration,
        error,
        errorResponse: error.response
          ? typeof error.response.data === 'object'
            ? error.response.data
            : String(error.response.data)
          : '-no response data-',
        // Code commented out to reduce log ingestion
        // errorResponseHeaders: error.response
        //   ? error.response.headers
        //   : '-no response headers-',
        errorMessage: error.message || String(error),
        transactionKey: options.transactionKey,
        transactionAttributes: options.transactionAttributes,
      },
      `AV client request error in ${duration}ms: ${sanitizedUrl}`
    )
    throwNarrowerError(error, sanitizedUrl)
  }
}

module.exports = (fn) => logRequest.bind(null, fn)
