const sinon = require('sinon')
const axios = require('axios')

const clearRequireCache = require('../test/clear-require-cache')

const { AvBadSessionError, AvInvalidLoginError } = require('./exceptions')

const debugLogs = false // enable for console logging

describe('avClient', () => {
  const sandbox = sinon.createSandbox()

  const consoleLogger = debugLogs ? console.log : () => {} // eslint-disable-line no-console

  const adapter = sandbox.stub(axios.defaults, 'adapter')

  const logStub = {
    debug: sandbox.stub(),
    error: sandbox.stub(),
    info: sandbox.stub(),
  }

  const httpErrorsStub = {
    badGateway: sandbox.stub(),
    badRequest: sandbox.stub(),
    unprocessableEntity: sandbox.stub(),
  }

  afterEach(async () => {
    sandbox.reset()
  })

  afterAll(() => {
    sandbox.restore()
    clearRequireCache()
  })

  beforeEach(() => {
    process.env.AV_CREDENTIALS =
      '{"AV_API_KEY":"","AV_BASE_URL":"https://AV_ENDPOINT_V2/","AV_USER":"","AV_PASSWORD":""}'
    process.env.TOKENEX_ID = 'TOKENEX_ID'
    process.env.TOKENEX_APIKEY = 'TOKENEX_APIKEY'
    process.env.TOKENEX_API_URL = 'TOKENEX_API_URL'

    for (const log in logStub) {
      logStub[log] = sandbox
        .stub()
        .callsFake(consoleLogger.bind(null, `[${log}]`))
    }

    for (const err in httpErrorsStub) {
      httpErrorsStub[err] = sandbox
        .stub()
        .callsFake((msg) => new Error(`${err}: ${msg}`))
    }
  })

  const stubAvResponse = ({
    data,
    headers = {},
    status = 200,
    statusText = 'OK',
    error = false,
  }) => {
    const options = {
      data,
      status,
      statusText,
      headers,
      request: {
        socket: {
          localPort: 65555,
        },
      },
    }

    if (error) {
      const rejectError = new Error('stubbed error')
      rejectError.response = options
      return adapter.rejects(rejectError)
    }

    return adapter.resolves(options)
  }

  const testClient = () => {
    return {
      ...require('./index')({
        apiKey: 'test-api-key',
        httpErrors: httpErrorsStub,
        log: logStub,
      }),
      makeRequest: () => 'someValue',
    }
  }

  it('openSession resolves with expected session and methods', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        response: {
          result: { status: 0 },
          session: fakeSession,
        },
      },
      headers: {
        'set-cookie': [
          'session_cookie_1=session1',
          'session_cookie_2=session2',
        ],
      },
    })

    const avClient = testClient()
    const result = await avClient.openSession({ log: logStub })

    expect(result.cookie).toStrictEqual([
      { name: 'session_cookie_1', value: 'session1' },
      { name: 'session_cookie_2', value: 'session2' },
    ])
    expect(result.session).toBe(fakeSession)
    expect(result.apiRequest).toBeInstanceOf(Function)
    expect(result.closeSession).toBeInstanceOf(Function)
    expect(logStub.info.callCount).toBe(2)
    expect(!logStub.info.firstCall.args[1].includes('api_key')).toBeTruthy()
    expect(!logStub.info.secondCall.args[1].includes('api_key')).toBeTruthy()
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
  })

  it('openSession resolves with expected session and methods using v2 API', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        result: { status: 0 },
        session: fakeSession,
      },
      headers: {
        'set-cookie': [
          'session_cookie_1=session1',
          'session_cookie_2=session2',
        ],
      },
    })

    const avClient = testClient()
    const result = await avClient.openSession({ log: logStub }, true)

    expect(result.cookie).toStrictEqual([
      { name: 'session_cookie_1', value: 'session1' },
      { name: 'session_cookie_2', value: 'session2' },
    ])

    expect(result.session).toBe(fakeSession)
    expect(result.apiRequest).toBeInstanceOf(Function)
    expect(result.closeSession).toBeInstanceOf(Function)
    expect(logStub.info.callCount).toBe(2)
    expect(!logStub.info.firstCall.args[1].includes('api_key')).toBeTruthy()
    expect(!logStub.info.secondCall.args[1].includes('api_key')).toBeTruthy()
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
    expect(adapter.lastCall.args[0].url).toBe(
      `https://AV_ENDPOINT_V2/session/authenticateUser?api_key=test-api-key`
    )
  })

  it('openSession makes a second api call when called with v2 and a valid role', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        result: { status: 0 },
        session: fakeSession,
      },
      headers: {
        'set-cookie': [
          'session_cookie_1=session1',
          'session_cookie_2=session2',
        ],
      },
    })

    const avClient = testClient()
    const result = await avClient.openSession(
      { log: logStub },
      true,
      'testRole'
    )

    expect(result.cookie).toStrictEqual([
      { name: 'session_cookie_1', value: 'session1' },
      { name: 'session_cookie_2', value: 'session2' },
    ])

    expect(result.session).toBe(fakeSession)
    expect(result.apiRequest).toBeInstanceOf(Function)
    expect(result.closeSession).toBeInstanceOf(Function)
    expect(logStub.info.callCount).toBe(4)
    expect(!logStub.info.firstCall.args[1].includes('api_key')).toBeTruthy()
    expect(!logStub.info.secondCall.args[1].includes('api_key')).toBeTruthy()
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
    expect(adapter.lastCall.args[0].data).toBe(
      '{"session":{"set":{"role":"testRole"}}}'
    )
    expect(adapter.lastCall.args[0].url).toBe(
      `https://AV_ENDPOINT_V2/user?api_key=test-api-key`
    )
  })

  it('openSession throws AvInvalidLoginError if the wrong session', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        response: {
          session: fakeSession,
          exception: {
            number: 2001,
          },
        },
      },
      headers: {
        'set-cookie': [
          'session_cookie_1=session1',
          'session_cookie_2=session2',
        ],
      },
    })

    const avClient = testClient()

    await expect(avClient.openSession({ log: logStub })).rejects.toBeInstanceOf(
      AvInvalidLoginError
    )

    expect(logStub.info.callCount).toBe(2)
    expect(!logStub.info.firstCall.args[1].includes('api_key')).toBeTruthy()
    expect(!logStub.info.secondCall.args[1].includes('api_key')).toBeTruthy()
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
  })

  it('openSession rejects with 504 when cookies not present', async () => {
    stubAvResponse({
      data: {},
      headers: {},
    })

    const avClient = testClient()

    await expect(avClient.openSession({ log: logStub })).rejects.toBeTruthy()
    expect(logStub.info.callCount).toBe(2)
    expect(logStub.error.callCount).toBe(0)
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
  })

  it('openSession rejects when 504 from AV/Mashery', async () => {
    stubAvResponse({
      data: {},
      headers: {},
      status: 504,
      statusText: 'Bad gateway test',
      error: true,
    })

    const avClient = testClient()

    await expect(avClient.openSession({ log: logStub })).rejects.toBeTruthy()
    expect(logStub.info.callCount).toBe(1)
    expect(logStub.error.callCount).toBe(2)
    expect(logStub.error.secondCall.args[0].transactionKey).toBe(
      '/External/AV/authenticate'
    )
    expect(!logStub.error.secondCall.args[1].includes('api_key')).toBeTruthy()
  })

  it('openSession returns correctly instrumented apiRequest', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        response: {
          session: fakeSession,
          result: { status: 0, message: '' },
        },
      },
      headers: {
        'set-cookie': [
          'session_cookie_1=session1',
          'session_cookie_2=session2',
        ],
      },
    })

    const avClient = testClient()

    const result = await avClient.openSession({ log: logStub })

    const apiResponse = await result.apiRequest('dummy-test-value', {})

    expect(apiResponse.response.session).toBe(fakeSession)
    expect(logStub.info.args[3][0].transactionKey).toBe(
      '/External/AV/dummy-test-value'
    )
    expect(logStub.info.args[3][0].transactionAttributes).toMatchObject({
      avSessionID: fakeSession,
    })
  })

  it('restoreSession throws an error if the wrong session', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        response: {
          result: { status: 99 },
          session: 'another session',
        },
      },
    })

    const avClient = testClient()

    const result = await avClient.restoreSession({
      log: logStub,
      session: fakeSession,
    })

    await expect(result.apiRequest('test', {})).rejects.toBeInstanceOf(
      AvBadSessionError
    )
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/test'
    )
    expect(logStub.info.secondCall.args[0].transactionAttributes).toStrictEqual(
      {
        avSessionID: fakeSession,
      }
    )
  })

  it('restoreSession returns correctly instrumented apiRequest', async () => {
    const fakeSession = 'should-be-a-session-uuid'
    stubAvResponse({
      data: {
        response: {
          session: fakeSession,
          result: { status: 0, message: '' },
        },
      },
    })

    const avClient = testClient()

    const result = await avClient.restoreSession({
      log: logStub,
      session: fakeSession,
    })

    const apiResponse = await result.apiRequest('test', {})

    expect(apiResponse.response.session).toBe(fakeSession)
    expect(logStub.info.secondCall.args[0].transactionKey).toBe(
      '/External/AV/test'
    )
    expect(logStub.info.secondCall.args[0].transactionAttributes).toStrictEqual(
      {
        avSessionID: fakeSession,
      }
    )
  })

  it('apiRequest without isTokenExRequest option does not add TokenEx values', async () => {
    stubAvResponse({
      data: { response: {} },
    })
    const avClient = testClient()
    const session = await avClient.openSession({
      log: logStub,
    })

    await session.apiRequest(
      'nonTokenExTest',
      { cvv: '{{{cvv}}}' },
      { isTokenExRequest: false }
    )

    expect(adapter.calledTwice).toBeTruthy()
    expect(adapter.secondCall.args[0].headers.TX_URL).toBeUndefined()
    expect(adapter.secondCall.args[0].headers.TX_TokenExID).toBeUndefined()
    expect(adapter.secondCall.args[0].headers.TX_APIKey).toBeUndefined()
    expect(adapter.secondCall.args[0]).toMatchObject({
      url: `https://AV_ENDPOINT_V2/nonTokenExTest?api_key=test-api-key`,
      data: 'cvv=%7B%7B%7Bcvv%7D%7D%7D',
    })
  })

  it('apiRequest with isTokenExRequest option makes correct TokenEx request', async () => {
    stubAvResponse({
      data: { response: {} },
    })
    const avClient = testClient()
    const session = await avClient.openSession({
      log: logStub,
    })

    await session.apiRequest(
      'tokenExTest',
      { cvv: '{{{cvv}}}' },
      { isTokenExRequest: true }
    )

    expect(adapter.calledTwice).toBeTruthy()
    expect(adapter.secondCall.args[0]).toMatchObject({
      headers: {
        TX_URL: `https://AV_ENDPOINT_V2/tokenExTest?api_key=test-api-key`,
        TX_TokenExID: 'TOKENEX_ID',
        TX_APIKey: 'TOKENEX_APIKEY',
      },
      url: 'TOKENEX_API_URL',
      data: 'cvv={{{cvv}}}',
    })
  })
})
