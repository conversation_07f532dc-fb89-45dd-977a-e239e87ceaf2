'use strict'

const sinon = require('sinon')

const mockedPaymentMethods = require('../test/fixtures/availablePaymentMethods.json')
const mockedPaymentMethodsAdyen = require('../test/fixtures/availablePaymentMethodsAdyen.json')
const mockedPaymentMethodsMixed = require('../test/fixtures/availablePaymentMethodsMixed.json')

const { AvAtgError } = require('./exceptions')
const {
  buildAvailableAllocationBody,
  v2allocateBestAvailableSeat,
  parseUniqueAvailablePaymentMethods,
  parseUniqueCardTypes,
  reserveGeneralAdmissions,
  buildReserveBodyv3,
  v2PromoCodeSearch,
  v2loadSeatmapScreen,
  buildReserveAdmissionBody,
  reserveMixedAdmissions,
  v2BubbleAvailabilitySearch,
  v2loadSeatmapExtract,
  extractBubbleAvailabilityData,
  buildReserveBestAvailableBody,
  getFraudPreventionParams,
  parsePaymentMethods,
} = require('./v2-av-helpers')

const USER_AGENT = 'userAgent'

describe('buildReserveBestAvailableBody', () => {
  const performanceId = 'A07268A2-679C-47FF-A87C-74E1B0B91E2D'
  const orderId = 'N5Zt3bjgTvGM7-h-_pKg9w-0000000000'

  it('group admissions with same stand and zone into one action', () => {
    const admissions = [
      {
        number: 2,
        standName: 'Stalls',
        ticketPrice: '57.00',
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '',
        description: 'Full Price',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '57.00',
      },
      {
        number: 1,
        standName: 'Stalls',
        ticketPrice: '57.00',
        priceZone: '78ED7F73-73C7-45F8-A8CC-16D93A3C6F21',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '',
        description: 'Full Price',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '57.00',
      },
      {
        number: 2,
        standName: 'Stalls',
        ticketPrice: '47.00',
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        priceTypeId: '9EFD791D-17EF-494A-B25C-9447C7050CCD',
        promoCodeId: '',
        benefitId: '',
        description: '16-25s Rate',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '47.00',
      },
      {
        number: 1,
        standName: 'Stalls',
        ticketPrice: '82.00',
        priceZone: '78ED7F73-73C7-45F8-A8CC-16D93A3C6F21',
        priceTypeId: '767DF1A1-F9D2-4058-8D8B-1F0170C96399',
        promoCodeId: '',
        benefitId: '',
        description:
          'Champagne Experience - Includes Ambassador Lounge entry, glass of champagne, savoury nibbles and an ice-cream',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '82.00',
      },
      {
        number: 1,
        standName: 'Stalls',
        ticketPrice: '102.00',
        priceZone: '78ED7F73-73C7-45F8-A8CC-16D93A3C6F21',
        priceTypeId: 'D2D9B575-C355-4CFF-85F5-0552630AFAC6',
        promoCodeId: '',
        benefitId: '',
        description:
          'Ultimate Experience - Includes Ambassador Lounge entry,  half bottle of champagne, box of chocolates and ATG gift',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '102.00',
      },
      {
        number: 1,
        standName: 'Stalls',
        ticketPrice: '28.50',
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        priceTypeId: '2BBD7CC8-FBCA-4E9C-9C21-6E4F7D646F22',
        promoCodeId: '',
        benefitId: '',
        description: 'Under 16s - must be accompanied by an adult',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '28.50',
      },
      {
        number: 1,
        standName: 'Stalls',
        ticketPrice: '72.00',
        priceZone: '78ED7F73-73C7-45F8-A8CC-16D93A3C6F21',
        priceTypeId: '8CAE2830-29E1-4D67-AD5D-66E2C3AF90C4',
        promoCodeId: '',
        benefitId: '',
        description:
          'VIP Experience - Includes Ambassador Lounge entry, welcome drink and savoury nibbles',
        savingTotal: 0,
        holdOverrideId: '',
        ticketCharge: '0.00',
        ticketBasePrice: '72.00',
      },
    ]
    const expectedResult = {
      actions: [
        {
          method: 'getBestAvailable',
          params: {
            'perfVector': 'A07268A2-679C-47FF-A87C-74E1B0B91E2D',
            'reqRows': '1',
            'optNum': '1',
            'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            'section_stand': 'Stalls',
            'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': '2',
            'reqNum::9EFD791D-17EF-494A-B25C-9447C7050CCD': '2',
            'reqNum::2BBD7CC8-FBCA-4E9C-9C21-6E4F7D646F22': '1',
          },
        },
        {
          method: 'getBestAvailable',
          params: {
            'perfVector': 'A07268A2-679C-47FF-A87C-74E1B0B91E2D',
            'reqRows': '1',
            'optNum': '1',
            'priceValue': '78ED7F73-73C7-45F8-A8CC-16D93A3C6F21',
            'section_stand': 'Stalls',
            'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': '1',
            'reqNum::767DF1A1-F9D2-4058-8D8B-1F0170C96399': '1',
            'reqNum::D2D9B575-C355-4CFF-85F5-0552630AFAC6': '1',
            'reqNum::8CAE2830-29E1-4D67-AD5D-66E2C3AF90C4': '1',
          },
        },
      ],
      objectName: 'N5Zt3bjgTvGM7-h-_pKg9w-0000000000',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
        'PerformanceDetails::A07268A2-679C-47FF-A87C-74E1B0B91E2D::venue_id',
      ],
      session: {
        set: {
          userAgent: 'userAgent',
        },
      },
    }

    const buildBodyResult = buildReserveBestAvailableBody(
      admissions,
      orderId,
      performanceId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })
})

describe('parsePaymentMethods', () => {
  it('returns available payment methods', () => {
    const paymentMethods = parsePaymentMethods(mockedPaymentMethods)

    expect(paymentMethods).toStrictEqual([
      {
        creditCard: {
          brand: 'mastercard',
        },
        paymentMethodId: 'DED108B5-A82D-43D9-A579-FFCACDBDA883',
        type: 'creditCard',
      },
      {
        paymentMethodId: '2B29E6F8-4F66-45AB-A296-A22092706483',
        type: 'giftCertificate',
      },
      {
        creditCard: {
          brand: 'americanExpress',
        },
        paymentMethodId: 'E9151D89-8686-4881-8C47-555B91E067DC',
        type: 'creditCard',
      },
      {
        creditCard: {
          brand: 'maestro',
        },
        paymentMethodId: '57A5E7CD-B464-4AAF-B755-9D1EB4819792',
        type: 'creditCard',
      },
      {
        creditCard: {
          brand: 'visa',
        },
        paymentMethodId: 'D8B9F7EC-92EE-4AFE-ACA1-FF40045FBA1C',
        type: 'creditCard',
      },
      {
        paymentMethodId: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
        type: 'paypal',
      },
      {
        paymentMethodId: 'B784E376-0833-4DCA-BA01-19CEF5BD9503',
        type: 'applePay',
      },
      {
        paymentMethodId: '8D56EA9F-9EE8-4E00-8593-AA7C7FFAB626',
        type: 'googlePay',
      },
      {
        creditCard: {
          brand: 'discover',
        },
        paymentMethodId: '17D94207-5E51-4DD0-8BA2-9EA6370A7449',
        type: 'creditCard',
      },
    ])
  })

  it('returns available payment methods with Adyen', () => {
    const paymentMethods = parsePaymentMethods(mockedPaymentMethodsAdyen)

    expect(paymentMethods).toStrictEqual([
      {
        paymentMethodId: '1CECF3C5-C439-4137-BA96-38BCB5214292',
        type: 'creditCard',
        creditCard: {
          brand: 'mastercard',
        },
      },
      {
        paymentMethodId: '48A21CD9-F3E9-417A-9044-D338E3E755D7',
        type: 'adyen',
        adyen: {
          type: 'googlePay',
          config:
            '{"paymentMethods":[{"type":"paywithgoogle","name":"Google Pay","configuration":{"gatewayMerchantId":"ATGE_TicketsUSEast","merchantId":"BCR2DN4T6C2YDCT4"},"brands":["amex","discover","mc","visa"]}]}',
        },
      },
      {
        paymentMethodId: '5D4567A1-25AF-41D4-AA70-91FE9B7F781D',
        type: 'creditCard',
        creditCard: {
          brand: 'discover',
        },
      },
      {
        paymentMethodId: '03F4BF70-E01B-4334-BFAA-E7940F82ACBA',
        type: 'adyen',
        adyen: {
          brand: 'discover',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["discover"]}]}',
        },
      },
      {
        paymentMethodId: '385F7C14-FD81-46A6-980D-2050756693DC',
        type: 'adyen',
        adyen: {
          brand: 'mastercard',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["mc"]}]}',
        },
      },
      {
        paymentMethodId: '0DAF748B-759B-4749-A102-06B2AA1E4E9A',
        type: 'adyen',
        adyen: {
          brand: 'americanExpress',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["amex"]}]}',
        },
      },
      {
        paymentMethodId: 'EAAB2AE1-E67A-4965-BB3D-8CF8246C8E94',
        type: 'adyen',
        adyen: {
          brand: 'visa',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["visa"]}]}',
        },
      },
      {
        paymentMethodId: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
        type: 'paypal',
      },
      {
        paymentMethodId: '030ED457-2A17-4D43-85FC-63F981AE846A',
        type: 'creditCard',
        creditCard: {
          brand: 'visa',
        },
      },
      {
        paymentMethodId: 'C92785F1-70E4-4DF0-9DA9-1D7ACD4CE433',
        type: 'adyen',
        adyen: {
          type: 'applePay',
          config:
            '{"paymentMethods":[{"type":"applepay","name":"Apple Pay","configuration":{"merchantName":"ATGE_TicketsUSEast"},"brands":["amex","discover","mc","visa"]}]}',
        },
      },
      {
        paymentMethodId: 'AA608C2C-75F9-4FD0-9C0A-47F85FFF2200',
        type: 'creditCard',
        creditCard: {
          brand: 'americanExpress',
        },
      },
    ])
  })

  it('returns available payment methods when mixed Adyen and Worldpay', () => {
    const paymentMethods = parsePaymentMethods(mockedPaymentMethodsMixed)

    expect(paymentMethods).toStrictEqual([
      {
        paymentMethodId: '1CECF3C5-C439-4137-BA96-38BCB5214292',
        type: 'creditCard',
        creditCard: { brand: 'mastercard' },
      },
      {
        paymentMethodId: '5D4567A1-25AF-41D4-AA70-91FE9B7F781D',
        type: 'creditCard',
        creditCard: { brand: 'americanExpress' },
      },
      {
        paymentMethodId: '03F4BF70-E01B-4334-BFAA-E7940F82ACBA',
        type: 'adyen',
        adyen: {
          brand: 'discover',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["discover"]}]}',
        },
      },
      {
        paymentMethodId: '385F7C14-FD81-46A6-980D-2050756693DC',
        type: 'adyen',
        adyen: {
          brand: 'mastercard',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["mc"]}]}',
        },
      },
      {
        paymentMethodId: 'DEA7B183-8A39-4708-A8E9-38E518C5A4D7',
        type: 'giftCertificate',
      },
      {
        paymentMethodId: '0DAF748B-759B-4749-A102-06B2AA1E4E9A',
        type: 'adyen',
        adyen: {
          brand: 'americanExpress',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"holderNameRequired":true,"brands":["amex"]}]}',
        },
      },
      {
        paymentMethodId: 'EAAB2AE1-E67A-4965-BB3D-8CF8246C8E94',
        type: 'adyen',
        adyen: {
          brand: 'visa',
          type: 'creditCard',
          config:
            '{"paymentMethods":[{"type":"scheme","name":"Card","hasHolderName":true,"brands":["visa"]}]}',
        },
      },
      {
        paymentMethodId: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
        type: 'paypal',
      },
      {
        paymentMethodId: '030ED457-2A17-4D43-85FC-63F981AE846A',
        type: 'creditCard',
        creditCard: { brand: 'visa' },
      },
      {
        paymentMethodId: '17D94207-5E51-4DD0-8BA2-9EA6370A7449',
        type: 'creditCard',
        creditCard: { brand: 'discover' },
      },
    ])
  })
})

describe('parseUniqueAvailablePaymentMethods', () => {
  it('parse available payment methods', () => {
    const paymentMethods =
      parseUniqueAvailablePaymentMethods(mockedPaymentMethods)

    expect(paymentMethods).toStrictEqual([
      'CARD',
      'GIFT_VOUCHER',
      'PAYPAL',
      'APPLEPAY',
      'GOOGLEPAY',
    ])
  })

  it('parse empty payment methods', () => {
    const paymentMethods = parseUniqueAvailablePaymentMethods([])

    expect(paymentMethods).toStrictEqual([])
  })

  it('parse missing payment methods', () => {
    const paymentMethods = parseUniqueAvailablePaymentMethods()

    expect(paymentMethods).toStrictEqual([])
  })
})

describe('parseUniqueCardTypes', () => {
  it('parse available payment methods', () => {
    const cardTypes = parseUniqueCardTypes(mockedPaymentMethods)

    expect(cardTypes).toStrictEqual([
      'mastercard',
      'american-express',
      'maestro',
      'visa',
      'discover',
    ])
  })

  it('parse empty payment methods', () => {
    const cardTypes = parseUniqueCardTypes([])

    expect(cardTypes).toStrictEqual([])
  })

  it('parse missing payment methods', () => {
    const cardTypes = parseUniqueCardTypes()

    expect(cardTypes).toStrictEqual([])
  })
})

describe('buildAvailableAllocationBody', () => {
  const dbPerformanceId = 'A07268A2-679C-47FF-A87C-74E1B0B91E2D'
  const orderId = 'N5Zt3bjgTvGM7-h-_pKg9w-0000000000'
  const avId = '9F481F43-4C18-4D77-8A90-BAAB471779D7'

  it('to return expected result with empty priceZoneId', async () => {
    const priceZoneId = ''

    const expectedResult = {
      actions: [
        {
          method: 'getBestAvailable',
          params: {
            'perfVector': 'A07268A2-679C-47FF-A87C-74E1B0B91E2D',
            'optNum': '1',
            'priceValue': '',
            'reqNum::9F481F43-4C18-4D77-8A90-BAAB471779D7': '1',
          },
        },
      ],
      objectName: 'N5Zt3bjgTvGM7-h-_pKg9w-0000000000',
      get: ['Order', 'Admissions', 'ServiceChargeDetails', 'Questions'],
    }

    const buildBodyResult = buildAvailableAllocationBody(
      dbPerformanceId,
      avId,
      priceZoneId,
      orderId
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('to return expected result with all params', async () => {
    const priceZoneId = 'testId'

    const expectedResult = {
      actions: [
        {
          method: 'getBestAvailable',
          params: {
            'perfVector': 'A07268A2-679C-47FF-A87C-74E1B0B91E2D',
            'optNum': '1',
            'priceValue': 'testId',
            'reqNum::9F481F43-4C18-4D77-8A90-BAAB471779D7': '1',
          },
        },
      ],
      objectName: 'N5Zt3bjgTvGM7-h-_pKg9w-0000000000',
      get: ['Order', 'Admissions', 'ServiceChargeDetails', 'Questions'],
    }

    const buildBodyResult = buildAvailableAllocationBody(
      dbPerformanceId,
      avId,
      priceZoneId,
      orderId
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })
})

describe('v2allocateBestAvailableSeat', () => {
  const orderId = 'Y5cUILZNSra_PhZxVaCvnw-0000000000'
  const loungeItem = {
    id: '75D0D0BC-80ED-449C-A78C-9FEA1030A31A',
    type: 'lounge',
    quantity: 1,
    details: {
      creditCard: { tags: [] },
      id: '2um8JENwF2t316SDnahsl',
      name: 'Blue Elephant Package at Piccadilly Theatre',
      title: 'Blue Elephant Package',
      type: 'lounge',
      avId: '75D0D0BC-80ED-449C-A78C-9FEA1030A31A',
      dbPerformanceId: '9F481F43-4C18-4D77-8A90-BAAB471779D7',
      price: 95,
      info: 'Ambassador Lounge entry, drinks reception with Champagne, premium alcoholic and soft options, cheese board, ice cream, house programme.',
      imageUrl:
        'https://res.cloudinary.com/dwzhqvxaz/image/upload/v1615560183/Upsells/BlueElephantLounge_Upsell_1920x1280.png',
      unitType: 'per package',
    },
  }

  it('to return success', async () => {
    const Admissions = {
      'state': '24',
      '68096937-B8A0-4562-97A0-145A388C89CE': {
        state: '24',
        order_id: {
          standard: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
          input: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
          display: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
        },
        ticket_id: { standard: '', input: '', display: '' },
        digital_wallet_token: { standard: '', input: '', display: '' },
        orderadmission_id: {
          standard: 'BF3FC202-D8C9-47DA-93DE-2D04734D1C7D',
          input: 'BF3FC202-D8C9-47DA-93DE-2D04734D1C7D',
          display: 'BF3FC202-D8C9-47DA-93DE-2D04734D1C7D',
        },
        admission_id: {
          standard: '68096937-B8A0-4562-97A0-145A388C89CE',
          input: '68096937-B8A0-4562-97A0-145A388C89CE',
          display: '68096937-B8A0-4562-97A0-145A388C89CE',
        },
        hold_value_id: { standard: '', input: '', display: '' },
        customer_offer_id: { standard: '', input: '', display: '' },
        price_value_id: {
          standard: '9CE6693F-C2AA-4965-AD99-B996CB47C36D',
          input: '9CE6693F-C2AA-4965-AD99-B996CB47C36D',
          display: '9CE6693F-C2AA-4965-AD99-B996CB47C36D',
        },
        performance_id: {
          standard: '555E16AA-15BC-48A1-A736-7004B4924766',
          input: '555E16AA-15BC-48A1-A736-7004B4924766',
          display: '555E16AA-15BC-48A1-A736-7004B4924766',
        },
        seat_id: {
          standard: '639D0D3C-2227-4050-98DD-5B8795AEEBDD',
          input: '639D0D3C-2227-4050-98DD-5B8795AEEBDD',
          display: '639D0D3C-2227-4050-98DD-5B8795AEEBDD',
        },
        row: { standard: '', input: '', display: '' },
        seat: { standard: '', input: '', display: '' },
        aisle: { standard: '', input: '', display: '' },
        sys_section: { standard: '1', input: '1', display: '1' },
        sys_row: { standard: '1', input: '1', display: '1' },
        sys_seat: { standard: '39', input: '39', display: '39' },
        message: { standard: '', input: '', display: '' },
        image: { standard: '', input: '', display: '' },
        section_id: {
          standard: 'DBB6992A-F261-45E9-8890-D7437AB3C959',
          input: 'DBB6992A-F261-45E9-8890-D7437AB3C959',
          display: 'DBB6992A-F261-45E9-8890-D7437AB3C959',
        },
        section: {
          standard: 'Ambassador Lounge',
          input: 'Ambassador Lounge',
          display: 'Ambassador Lounge',
        },
        section_entrance: { standard: '', input: '', display: '' },
        section_description: {
          standard: 'Ambassador Lounge',
          input: 'Ambassador Lounge',
          display: 'Ambassador Lounge',
        },
        price_type_id: {
          standard: '75D0D0BC-80ED-449C-A78C-9FEA1030A31A',
          input: '75D0D0BC-80ED-449C-A78C-9FEA1030A31A',
          display: '75D0D0BC-80ED-449C-A78C-9FEA1030A31A',
        },
        order_bundle_id: { standard: '', input: '', display: '' },
        orderbundle_bundle_version_id: { standard: '', input: '', display: '' },
        bundle_element_id: { standard: '', input: '', display: '' },
        net: { standard: '79.17', input: '79.17', display: '£79.17' },
        incl_charge1: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge2: { standard: '15.83', input: '15.83', display: '£15.83' },
        incl_charge3: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge4: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge5: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge1: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge2: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge3: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge4: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge5: { standard: '0.00', input: '0.00', display: '£0.00' },
        net_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge1_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge2_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge3_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge4_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge5_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        add_charge1_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge2_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge3_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge4_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge5_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        amount: { standard: '95.00', input: '95.00', display: '£95.00' },
        total: { standard: '95.00', input: '95.00', display: '£95.00' },
        default_amount: { standard: '0.00', input: '0.00', display: '£0.00' },
        total_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        amount_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        comp_count: { standard: '', input: '', display: '' },
        comp_count_delta: { standard: '', input: '', display: '' },
        location: {
          standard: 'Ambassador Lounge',
          input: 'Ambassador Lounge',
          display: 'Ambassador Lounge',
        },
        on_offer_timeout: {
          standard: [Array],
          input: [Array],
          display: [Array],
        },
        on_offer_id: {
          standard: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
          input: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
          display: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
        },
        override_barcode: { standard: '', input: '', display: '' },
        override_ticket_number: {
          standard: '251639956372',
          input: '251639956372',
          display: '251639956372',
        },
        card_number: { standard: '', input: '', display: '' },
        customerpass_id: { standard: '', input: '', display: '' },
        customer_id: { standard: '', input: '', display: '' },
        agent_id: { standard: '', input: '', display: '' },
        default_price_type_id: { standard: '', input: '', display: '' },
        performance_start_date: {
          standard: [Array],
          input: [Array],
          display: [Array],
        },
        release_reason: { standard: '', input: '', display: '' },
        print_count: { standard: '', input: '', display: '' },
        print_status: { standard: '4', input: '4', display: '4' },
        promo_code_id: { standard: '', input: '', display: '' },
        access_code: { standard: '', input: '', display: '' },
        data1: { standard: '', input: '', display: '' },
        price_message: { standard: '', input: '', display: '' },
        hold_message: { standard: '', input: '', display: '' },
        pricing_method: { standard: '', input: '', display: '' },
        order_exchange_id: { standard: '', input: '', display: '' },
        exchange_orderadmission_id: { standard: '', input: '', display: '' },
        exchange_performance_id: { standard: '', input: '', display: '' },
        exchange_price_type_id: { standard: '', input: '', display: '' },
        exchange_amount_delta: { standard: '', input: '', display: '' },
        exchange_net_delta: { standard: '', input: '', display: '' },
        exchange_total_delta: { standard: '', input: '', display: '' },
        exchange_eligibility: { standard: '', input: '', display: '' },
        audit_id: { standard: '', input: '', display: '' },
        create_audit_id: { standard: '', input: '', display: '' },
        association: { standard: '', input: '', display: '' },
        audit_time: { standard: '', input: '', display: '' },
        audit_user_id: { standard: '', input: '', display: '' },
        audit_user_role_id: { standard: '', input: '', display: '' },
        AdmissionQuestions: { state: '0' },
        Charges: {
          'state': '24',
          '734BA82B-5427-4196-B7A6-3A1C24CFB67D': [Object],
        },
      },
      '0ADCD614-CD64-44CA-B917-308738476281': {
        state: '24',
        order_id: {
          standard: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
          input: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
          display: 'F4D7FB69-2086-4BD2-8A21-9BE2D379DB69',
        },
        ticket_id: { standard: '', input: '', display: '' },
        digital_wallet_token: { standard: '', input: '', display: '' },
        orderadmission_id: {
          standard: '2122F648-9BB0-4EFB-B5CB-BE7DE8849CE8',
          input: '2122F648-9BB0-4EFB-B5CB-BE7DE8849CE8',
          display: '2122F648-9BB0-4EFB-B5CB-BE7DE8849CE8',
        },
        admission_id: {
          standard: '0ADCD614-CD64-44CA-B917-308738476281',
          input: '0ADCD614-CD64-44CA-B917-308738476281',
          display: '0ADCD614-CD64-44CA-B917-308738476281',
        },
        hold_value_id: {
          standard: 'D908AFB9-AF8B-426D-A3BF-A1D2DA07F4BA',
          input: 'D908AFB9-AF8B-426D-A3BF-A1D2DA07F4BA',
          display: 'D908AFB9-AF8B-426D-A3BF-A1D2DA07F4BA',
        },
        customer_offer_id: { standard: '', input: '', display: '' },
        price_value_id: {
          standard: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          input: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          display: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        },
        performance_id: {
          standard: '36A1C947-4D52-4220-BF26-D36CBE29616A',
          input: '36A1C947-4D52-4220-BF26-D36CBE29616A',
          display: '36A1C947-4D52-4220-BF26-D36CBE29616A',
        },
        seat_id: {
          standard: '3568E4E0-91D3-4227-936E-C37D7229C79B',
          input: '3568E4E0-91D3-4227-936E-C37D7229C79B',
          display: '3568E4E0-91D3-4227-936E-C37D7229C79B',
        },
        row: { standard: 'J', input: 'J', display: 'J' },
        seat: { standard: '15', input: '15', display: '15' },
        aisle: { standard: '', input: '', display: '' },
        sys_section: { standard: '4', input: '4', display: '4' },
        sys_row: { standard: '9', input: '9', display: '9' },
        sys_seat: { standard: '15', input: '15', display: '15' },
        message: { standard: '', input: '', display: '' },
        image: { standard: '', input: '', display: '' },
        section_id: {
          standard: '39E827A9-87EF-44B2-AB94-BF4529706517',
          input: '39E827A9-87EF-44B2-AB94-BF4529706517',
          display: '39E827A9-87EF-44B2-AB94-BF4529706517',
        },
        section: {
          standard: 'Royal Circle',
          input: 'Royal Circle',
          display: 'Royal Circle',
        },
        section_entrance: {
          standard: 'Sherwood St.',
          input: 'Sherwood St.',
          display: 'Sherwood St.',
        },
        section_description: { standard: '', input: '', display: '' },
        price_type_id: {
          standard: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
          input: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
          display: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        order_bundle_id: { standard: '', input: '', display: '' },
        orderbundle_bundle_version_id: { standard: '', input: '', display: '' },
        bundle_element_id: { standard: '', input: '', display: '' },
        net: { standard: '78.23', input: '78.23', display: '£78.23' },
        incl_charge1: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge2: { standard: '10.36', input: '10.36', display: '£10.36' },
        incl_charge3: { standard: '4.66', input: '4.66', display: '£4.66' },
        incl_charge4: { standard: '1.75', input: '1.75', display: '£1.75' },
        incl_charge5: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge1: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge2: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge3: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge4: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge5: { standard: '0.00', input: '0.00', display: '£0.00' },
        net_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        incl_charge1_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge2_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge3_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge4_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        incl_charge5_paid: {
          standard: '0.00',
          input: '0.00',
          display: '£0.00',
        },
        add_charge1_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge2_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge3_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge4_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        add_charge5_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        amount: { standard: '95.00', input: '95.00', display: '£95.00' },
        total: { standard: '95.00', input: '95.00', display: '£95.00' },
        default_amount: {
          standard: '95.00',
          input: '95.00',
          display: '£95.00',
        },
        total_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        amount_paid: { standard: '0.00', input: '0.00', display: '£0.00' },
        comp_count: { standard: '', input: '', display: '' },
        comp_count_delta: { standard: '', input: '', display: '' },
        location: {
          standard: 'Royal Circle/J/15',
          input: 'Royal Circle/J/15',
          display: 'Royal Circle/J/15',
        },
        on_offer_timeout: {
          standard: [Array],
          input: [Array],
          display: [Array],
        },
        on_offer_id: {
          standard: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
          input: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
          display: 'A32DB286-7612-4C60-80B5-8FD74E0F4CBA',
        },
        override_barcode: { standard: '', input: '', display: '' },
        override_ticket_number: {
          standard: '251409102549',
          input: '251409102549',
          display: '251409102549',
        },
        card_number: { standard: '', input: '', display: '' },
        customerpass_id: { standard: '', input: '', display: '' },
        customer_id: { standard: '', input: '', display: '' },
        agent_id: { standard: '', input: '', display: '' },
        default_price_type_id: { standard: '', input: '', display: '' },
        performance_start_date: {
          standard: [Array],
          input: [Array],
          display: [Array],
        },
        release_reason: { standard: '', input: '', display: '' },
        print_count: { standard: '', input: '', display: '' },
        print_status: { standard: '4', input: '4', display: '4' },
        promo_code_id: { standard: '', input: '', display: '' },
        access_code: { standard: '', input: '', display: '' },
        data1: { standard: '', input: '', display: '' },
        price_message: { standard: '', input: '', display: '' },
        hold_message: { standard: '', input: '', display: '' },
        pricing_method: { standard: '', input: '', display: '' },
        order_exchange_id: { standard: '', input: '', display: '' },
        exchange_orderadmission_id: { standard: '', input: '', display: '' },
        exchange_performance_id: { standard: '', input: '', display: '' },
        exchange_price_type_id: { standard: '', input: '', display: '' },
        exchange_amount_delta: { standard: '', input: '', display: '' },
        exchange_net_delta: { standard: '', input: '', display: '' },
        exchange_total_delta: { standard: '', input: '', display: '' },
        exchange_eligibility: { standard: '', input: '', display: '' },
        audit_id: { standard: '', input: '', display: '' },
        create_audit_id: { standard: '', input: '', display: '' },
        association: { standard: '', input: '', display: '' },
        audit_time: { standard: '', input: '', display: '' },
        audit_user_id: { standard: '', input: '', display: '' },
        audit_user_role_id: { standard: '', input: '', display: '' },
        AdmissionQuestions: { state: '0' },
        Charges: {
          'state': '24',
          '55D3F4AA-C53A-4A96-AA44-9C54F1EAD4CE': [Object],
          '9D478E88-65BD-4218-8F5A-65537D85907A': [Object],
          'F3A532AD-E2A1-48F1-BBD9-A87899E04325': [Object],
          'CB104D3F-8B55-4A53-8CAC-14332240298F': [Object],
          'AC00B7DF-D658-4DFA-BAD9-A22C19673A20': [Object],
          '614C408F-A36D-4576-846F-76F0F8E97E11': [Object],
          'B2D616CF-6A16-4361-B1B4-114812BA569D': [Object],
          'E9D2D762-3190-4BEE-AFE2-44A455029A6D': [Object],
          'A045B681-E2EB-4D30-A797-1F85ECF4EF6B': [Object],
          '81AAB0B3-19B0-43CF-ABC2-BA61B30D2FA3': [Object],
          'D67B4377-91BD-4E7A-9752-8DA2CE687BAF': [Object],
          '4132BFF4-B5B2-4ED0-8DD9-D36D5F2D3C9E': [Object],
          '93F9C8DB-2FC8-4DBE-85E8-4565D31302CE': [Object],
          '24F19670-C47F-4758-B85E-93360EECEC53': [Object],
          'E64C1DDE-54FA-4B91-8FD2-27B39A0AB178': [Object],
          'D90C5E23-1535-4BB1-8134-378356BCE1DB': [Object],
        },
      },
    }
    const ServiceChargeDetails = {}

    const expectedBody = {
      req: {
        actions: [
          {
            method: 'getBestAvailable',
            params: {
              'perfVector': '9F481F43-4C18-4D77-8A90-BAAB471779D7',
              'optNum': '1',
              'priceValue': '',
              'reqNum::75D0D0BC-80ED-449C-A78C-9FEA1030A31A': '1',
            },
          },
        ],
        objectName: 'Y5cUILZNSra_PhZxVaCvnw-0000000000',
        get: ['Order', 'Admissions', 'ServiceChargeDetails', 'Questions'],
      },
    }

    const expectedResponse = {
      seats: {
        '68096937-B8A0-4562-97A0-145A388C89CE': {
          section: 'Ambassador Lounge',
          seat: '',
          row: '',
          total: '95.00',
          message: '',
        },
        '0ADCD614-CD64-44CA-B917-308738476281': {
          section: '',
          seat: '15',
          row: 'J',
          total: '95.00',
          message: '',
        },
      },
      orderId,
    }
    const apiRequestStub = sinon.stub().resolves({
      data: { Admissions, ServiceChargeDetails },
    })

    const response = await v2allocateBestAvailableSeat(
      apiRequestStub,
      orderId,
      loungeItem
    )

    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args[0]).toBe('order')
    expect(apiCall.args[1]).toStrictEqual(expectedBody)
    expect(apiCall.args[2]).toStrictEqual({ isV2Explicit: true })

    expect(response).toStrictEqual(expectedResponse)
  })
})

const avFailureResponse = {
  version: '7.48.0',
  session: '972CF698-21A9-4881-B245-61AEACA11D8F',
  errorCode: '6',
  message: 'There was an error processing an action',
  exception: {
    method: 'getBestAvailable',
    type: 'Validation',
    severity: 'Info',
    number: 5515,
    message:
      'The number of seats requested is too large. Try your search again.',
    context: '',
  },
}

describe('reserveGeneralAdmissions', () => {
  const avSuccessResponse = {
    version: '7.48.0',
    session: '6C0A9E2F-EA25-4C39-A9FF-C39D223EE629',
    data: {
      Admissions: {
        'state': '24',
        '91043E4E-3944-41B9-9C6C-5EFF4E6560B1': {
          state: '24',
          order_id: {
            standard: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
            input: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
            display: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
          },
          orderadmission_id: {
            standard: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
            input: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
            display: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
          },
          admission_id: {
            standard: '91043E4E-3944-41B9-9C6C-5EFF4E6560B1',
            input: '91043E4E-3944-41B9-9C6C-5EFF4E6560B1',
            display: '91043E4E-3944-41B9-9C6C-5EFF4E6560B1',
          },
          price_value_id: {
            standard: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            input: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            display: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          },
          performance_id: {
            standard: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
            input: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
            display: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
          },
          seat_id: {
            standard: 'F91C8FCE-9C95-40B3-9671-0F9BFAF24981',
            input: 'F91C8FCE-9C95-40B3-9671-0F9BFAF24981',
            display: 'F91C8FCE-9C95-40B3-9671-0F9BFAF24981',
          },
          section_id: {
            standard: '91A3F978-96EF-4C3C-B0A7-1A1DC75BD991',
            input: '91A3F978-96EF-4C3C-B0A7-1A1DC75BD991',
            display: '91A3F978-96EF-4C3C-B0A7-1A1DC75BD991',
          },
          section: {
            standard: 'Studio',
            input: 'Studio',
            display: 'Studio',
          },
          section_description: {
            standard: 'Studio',
            input: 'Studio',
            display: 'Studio',
          },
          price_type_id: {
            standard: 'EC290A80-54B4-4635-932C-1769C20123FE',
            input: 'EC290A80-54B4-4635-932C-1769C20123FE',
            display: 'EC290A80-54B4-4635-932C-1769C20123FE',
          },
          net: {
            standard: '16.67',
            input: '16.67',
            display: '£16.67',
          },
          location: {
            standard: 'GA',
            input: 'GA',
            display: 'GA',
          },
          on_offer_timeout: {
            standard: ['2022-06-09T11:14:56.000'],
            input: ['09/06/2022, 11:14:56'],
            display: ['2022-06-09 11:14 am'],
          },
          on_offer_id: {
            standard: '04D3B468-0091-4088-995C-87619A9EBDE2',
            input: '04D3B468-0091-4088-995C-87619A9EBDE2',
            display: '04D3B468-0091-4088-995C-87619A9EBDE2',
          },
          override_ticket_number: {
            standard: '288613082037',
            input: '288613082037',
            display: '288613082037',
          },
          performance_start_date: {
            standard: ['2023-05-25T19:30:00.000'],
            input: ['25/05/2023, 19:30'],
            display: ['Thu 25 May 2023 19:30'],
          },
          Charges: {
            'state': '24',
            '8C7DDF71-89E4-410F-AA4C-0DF373B9C06D': {
              state: '24',
              order_id: {
                standard: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
                input: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
                display: 'B182C9CF-1995-4A3C-97C3-6255E4DA86DF',
              },
              charge_id: {
                standard: '8C7DDF71-89E4-410F-AA4C-0DF373B9C06D',
                input: '8C7DDF71-89E4-410F-AA4C-0DF373B9C06D',
                display: '8C7DDF71-89E4-410F-AA4C-0DF373B9C06D',
              },
              rate_id: {
                standard: 'CAD953BB-0CE0-4DA2-ACE2-E760679FD036',
                input: 'CAD953BB-0CE0-4DA2-ACE2-E760679FD036',
                display: 'CAD953BB-0CE0-4DA2-ACE2-E760679FD036',
              },
              item_id: {
                standard: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
                input: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
                display: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
              },
              owner_id: {
                standard: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
                input: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
                display: '44DAB087-9BAD-4C71-B8EE-D3043F5E403F',
              },
              performance_id: {
                standard: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
                input: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
                display: 'FD78C1E6-0238-446D-B176-ABAC170807E0',
              },
              service_charge_id: {
                standard: '40A9CB63-E494-4E5F-B3AE-719110A88377',
                input: '40A9CB63-E494-4E5F-B3AE-719110A88377',
                display: '40A9CB63-E494-4E5F-B3AE-719110A88377',
              },
            },
          },
        },
      },
      ServiceChargeDetails: {
        'state': '16',
        '40A9CB63-E494-4E5F-B3AE-719110A88377': {
          state: '16',
          association: {
            standard: '',
            input: '',
            display: '',
          },
          service_charge_id: {
            standard: '40A9CB63-E494-4E5F-B3AE-719110A88377',
            input: '',
            display: '40A9CB63-E494-4E5F-B3AE-719110A88377',
          },
          name: {
            standard: 'ATG Per Ticket Fee (WEB): 10%',
            input: '',
            display: 'ATG Per Ticket Fee (WEB): 10%',
          },
          description: {
            standard: 'Per Ticket Fee',
            input: '',
            display: 'Per Ticket Fee',
          },
          short_description: {
            standard: 'Per Ticket Fee',
            input: '',
            display: 'Per Ticket Fee',
          },
          data1: {
            standard: 'non_agent_commission',
            input: '',
            display: 'non_agent_commission',
          },
          data2: {
            standard: 'Service Charge - Per TIcket Fee',
            input: '',
            display: 'Service Charge - Per TIcket Fee',
          },
          data3: {
            standard: 'Outside Fees',
            input: '',
            display: 'Outside Fees',
          },
          data4: {
            standard: 'Per Ticket',
            input: '',
            display: 'Per Ticket',
          },
          data5: {
            standard: 'Percentage',
            input: '',
            display: 'Percentage',
          },
          data6: {
            standard: 'ATG Ticketing',
            input: '',
            display: 'ATG Ticketing',
          },
          data8: {
            standard: 'Refundable',
            input: '',
            display: 'Refundable',
          },
          additional_amount: {
            standard: '2.00',
            input: '',
            display: '£2.00',
          },
          optional_charge_acceptance: {
            standard: '1',
            input: '1',
            display: '1',
          },
        },
      },
      Questions: {
        state: '0',
      },
      DeliveryMethodDetails: {
        'state': '16',
        '30F85C7A-D114-4F86-97D8-35898B292C29': {
          state: '16',
          association: {
            standard: '',
            input: '',
            display: '',
          },
          deliverymethod_id: {
            standard: '30F85C7A-D114-4F86-97D8-35898B292C29',
            input: '',
            display: '30F85C7A-D114-4F86-97D8-35898B292C29',
          },
          name: {
            standard: 'eTicket (Deferred)',
            input: '',
            display: 'eTicket (Deferred)',
          },
          description: {
            standard:
              'Emailed and available to you within two weeks of the event for contactless entry. Display on your phone or print.',
            input: '',
            display:
              'Emailed and available to you within two weeks of the event for contactless entry. Display on your phone or print.',
          },
          detailed_description: {
            standard: '',
            input: '',
            display: '',
          },
          type: {
            standard: 'None',
            input: '',
            display: 'None',
          },
          ticket_template_id: {
            standard: '',
            input: '',
            display: '',
          },
          delivery_time: {
            standard: ['2D'],
            input: ['2D'],
            display: ['2D'],
          },
        },
        'AvailablePaymentMethods': {
          'state': '16',
          'E0384EDB-72C7-412E-B32D-338B88D996FF': {
            state: '16',
            payment_method_id: {
              standard: 'E0384EDB-72C7-412E-B32D-338B88D996FF',
              input: '',
              display: 'E0384EDB-72C7-412E-B32D-338B88D996FF',
            },
            customer_payment_id: {
              standard: '',
              input: '',
              display: '',
            },
            name: {
              standard: 'Worldpay Websales - Mastercard',
              input: '',
              display: 'Worldpay Websales - Mastercard',
            },
            description: {
              standard: 'Card - Mastercard',
              input: '',
              display: 'Card - Mastercard',
            },
            detailed_description: {
              standard: '',
              input: '',
              display: '',
            },
            type: {
              standard: 'CreditCard',
              input: '',
              display: 'CreditCard',
            },
            icon: {
              standard: '/ArticleMedia/Images/Mastercardnew4.png',
              input: '',
              display: '/ArticleMedia/Images/Mastercardnew4.png',
            },
            payer_auth_enabled: {
              standard: '1',
              input: '',
              display: '1',
            },
          },
        },
      },
    },
    return: [
      {
        method: 'getBestAvailable',
        message: '',
      },
    ],
  }

  const generalAdmission = [
    {
      sectionName: 'Standing',
      priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
      number: 2,
      priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
      ticketPrice: '75.00',
      benefitId: '',
      promoCodeId: '',
    },
    {
      sectionName: 'Studio',
      priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
      number: 2,
      priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
      ticketPrice: '75.00',
      benefitId: '',
      promoCodeId: '',
    },
  ]

  it('should call api with correct body params', async () => {
    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const orderId = 'PyHpbxtfTBy6Re_Z1TwNjw-0000000000'

    const body = buildReserveAdmissionBody(
      generalAdmission,
      orderId,
      performanceId,
      USER_AGENT
    )

    expect(body).toStrictEqual({
      actions: [
        {
          method: 'getBestAvailable',
          params: {
            'optNum': '1',
            'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
            'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': '2',
            'reqRows': '1',
            'section_name': 'Standing',
          },
        },
        {
          method: 'getBestAvailable',
          params: {
            'optNum': '1',
            'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
            'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': '2',
            'reqRows': '1',
            'section_name': 'Studio',
          },
        },
      ],
      objectName: orderId,
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: 'userAgent',
        },
      },
    })
  })

  it('should throw an error if av reservation fails', async () => {
    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const apiRequestStub = sinon.stub().resolves(avFailureResponse)

    await expect(
      reserveGeneralAdmissions(apiRequestStub, generalAdmission, performanceId)
    ).rejects.toThrow(
      new AvAtgError('AV failure, general admission reservation failed')
    )
  })

  it('show reserve admissions with promocodes and hiddenPromoCodes', async () => {
    const generalAdmissionWithPromoCodesAndBenefits = [
      {
        sectionName: 'Standing',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        number: 2,
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        ticketPrice: '75.00',
        benefitId: '',
        promoCodeId: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
      },
      {
        sectionName: 'Studio',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        number: 2,
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        ticketPrice: '75.00',
        hiddenPromoCodeId: '93E2E8F2-98BA-43FA-9073-1D02C1593F2C',
        promoCodeId: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
      },
    ]

    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const apiRequestStub = sinon.stub().resolves(avSuccessResponse)

    await reserveGeneralAdmissions(
      apiRequestStub,
      generalAdmissionWithPromoCodesAndBenefits,
      performanceId,
      USER_AGENT
    )

    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args[0]).toBe('order')
    expect(apiCall.args[1]).toStrictEqual({
      req: {
        actions: [
          {
            method: 'getBestAvailable',
            params: {
              'optNum': '1',
              'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
              'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
              'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D::8798C793-7FAB-44C1-B4F6-2AB7CE197A77':
                '2',
              'reqRows': '1',
              'section_name': 'Standing',
            },
          },
          {
            method: 'getBestAvailable',
            params: {
              'optNum': '1',
              'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
              'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
              'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D::93E2E8F2-98BA-43FA-9073-1D02C1593F2C':
                '2',
              'reqRows': '1',
              'section_name': 'Studio',
            },
          },
        ],
        objectName: expect.any(String),
        get: [
          'Admissions',
          'ServiceChargeDetails',
          'Questions',
          'DeliveryMethodDetails',
          'AvailablePaymentMethods',
          'Order',
        ],
        session: {
          set: {
            userAgent: 'userAgent',
          },
        },
      },
    })
  })
})

describe('buildReserveBodyv3', () => {
  it('Reserve request body with one price type', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id',
        promoCodeId: '',
        hiddenPromoCodeId: '',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id',
        promoCodeId: '',
        hiddenPromoCodeId: '',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id': ['seat-guid-1', 'seat-guid-2'],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('Reserve request body with two price types', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: '',
        hiddenPromoCodeId: '',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id-2',
        promoCodeId: '',
        hiddenPromoCodeId: '',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id-1': ['seat-guid-1'],
            'addSeatID::price-Type-id-2': ['seat-guid-2'],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('Reserve request body with promoCode', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: 'promo-code-id-1',
        hiddenPromoCodeId: '',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: 'promo-code-id-1',
        hiddenPromoCodeId: '',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id-1::promo-code-id-1': [
              'seat-guid-1',
              'seat-guid-2',
            ],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('Reserve request body with hiddenPromoCode', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: '',
        hiddenPromoCodeId: 'hidden-promo-code-id-1',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: '',
        hiddenPromoCodeId: 'hidden-promo-code-id-1',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id-1::hidden-promo-code-id-1': [
              'seat-guid-1',
              'seat-guid-2',
            ],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('Reserve request body with hiddenPromoCode and promoCode', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: 'promo-code-id-1',
        hiddenPromoCodeId: '',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: '',
        hiddenPromoCodeId: 'hidden-promo-code-id-1',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id-1::promo-code-id-1': ['seat-guid-1'],
            'addSeatID::price-Type-id-1::hidden-promo-code-id-1': [
              'seat-guid-2',
            ],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })

  it('Reserve request body with ignored hiddenPromoCode in favour of a promoCode', async () => {
    const performance = 'performance-id'
    const seats = {
      'seat-guid-1': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: 'promo-code-id-1',
        hiddenPromoCodeId: '',
      },
      'seat-guid-2': {
        priceTypeId: 'price-Type-id-1',
        promoCodeId: 'promo-code-id-1',
        hiddenPromoCodeId: 'hidden-promo-code-id-1',
      },
    }
    const orderId = 'orderId'

    const expectedResult = {
      actions: [
        {
          method: 'manageAdmissions',
          params: {
            'performanceID': 'performance-id',
            'addSeatID::price-Type-id-1::promo-code-id-1': ['seat-guid-1'],
            'addSeatID::price-Type-id-1::hidden-promo-code-id-1': [
              'seat-guid-2',
            ],
          },
        },
      ],
      objectName: 'orderId',
      get: [
        'Admissions',
        'ServiceChargeDetails',
        'Questions',
        'DeliveryMethodDetails',
        'AvailablePaymentMethods',
        'Order',
      ],
      session: {
        set: {
          userAgent: USER_AGENT,
        },
      },
    }

    const buildBodyResult = buildReserveBodyv3(
      performance,
      seats,
      orderId,
      USER_AGENT
    )

    expect(buildBodyResult).toStrictEqual(expectedResult)
  })
})

describe('v2PromoCodeSearch', () => {
  it('with VALID AV result', async () => {
    const apiRequestStub = jest.fn()
    apiRequestStub.mockReturnValue({
      data: {
        Result: {
          state: 'rubbish',
          1: {
            promocode_id: { standard: 'my-promo-code-id' },
            promocode_start_date: { standard: '2012-05-17T00:00:00.000' },
            promocode_end_date: { standard: '' },
            promocode_description: { standard: 'This is a promoCode' },
            promocode_short_description: { standard: 'This promoCode' },
            promocode_access_code: { standard: 'my-promo-access-code' },
          },
        },
      },
    })

    const accessCodeMock = 'someAccessCode'
    const reqMock = {
      req: {
        actions: [{ acceptWarnings: '4276', method: 'search' }],
        get: ['Result'],
        set: {
          'Query': {
            Filter: {
              '+1': {
                name: 'promocode_access_code',
                oper: '=',
                operator_type: 'AND',
                type: 'matchCondition',
                value: accessCodeMock,
              },
              '+2': {
                name: 'OneTimeAccessCode.accesscode_access_code',
                oper: '=',
                operator_type: 'OR',
                type: 'matchCondition',
                value: accessCodeMock,
              },
            },
            ResultMember: {
              '+1': { name: 'promocode_id', order: 1 },
              '+2': { name: 'promocode_description', order: 2 },
              '+3': { name: 'promocode_short_description', order: 3 },
              '+4': { name: 'promocode_start_date', order: 4 },
              '+5': { name: 'promocode_end_date', order: 5 },
              '+6': { name: 'promocode_access_code', order: 6 },
              '+7': {
                name: 'OneTimeAccessCode.accesscode_access_code',
                order: 7,
              },
            },
          },
          'Query::current_page': 1,
          'Search::object': 'ts_promotion',
          'Search::page_size': 1,
        },
      },
    }
    const promoCode = await v2PromoCodeSearch(apiRequestStub, accessCodeMock)

    expect(promoCode).toStrictEqual({
      id: 'my-promo-code-id',
      startDate: '2012-05-17T00:00:00.000',
      endDate: '',
      description: 'This is a promoCode',
      shortDescription: 'This promoCode',
      accessCode: 'my-promo-access-code',
    })

    expect(apiRequestStub).toHaveBeenCalledWith('search', reqMock)
  })

  it('with INVALID AV result', async () => {
    const apiRequestStub = sinon.stub().resolves({
      data: {
        Result: {
          state: 'rubbish',
        },
      },
    })
    const promoCodePromise = v2PromoCodeSearch(apiRequestStub, 'someAccessCode')

    await expect(promoCodePromise).rejects.toThrow(
      new AvAtgError('Invalid Promocode')
    )
  })
})

describe('v2loadSeatmapScreen', () => {
  it('with legends data', async () => {
    const params = {
      performanceId: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
      screenId: 'EA34C73C-CA65-4052-A63E-2AFCD692C657',
      promoCodeId: undefined,
    }

    const apiRequestStub = sinon.stub().resolves({
      data: {
        'pricetypes': {
          'state': '16',
          '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
        },
        'pricing': {
          'state': '16',
          '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
        },
        'admissions': {
          'state': '16',
          '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
        },
        'legends': {
          'state': '16',
          '47DABA72-D5C2-4AA6-B652-615533600992': {
            value_legend_id: {
              standard: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
              input: '',
              display: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_display',
            },
            description: {
              standard: 'Super Premium Plus standard',
              input: '',
              display: 'Super Premium Plus display',
            },
            group: {
              standard: 'Super Premium Plus group standard',
              input: '',
              display: 'Super Premium Plus group display',
            },
          },
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date': {
          standard: ['start_date_standard'],
          input: ['start_date_input'],
          display: ['start_date_display'],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2': {
          standard: '',
          input: '',
          display: '',
        },
      },
    })

    const expectedReqBody = {
      req: {
        actions: [
          {
            method: 'loadMap',
            params: {
              performance_ids: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              screen_id: 'EA34C73C-CA65-4052-A63E-2AFCD692C657',
              entered_promo_code_id: undefined,
            },
          },
        ],
        get: [
          'pricetypes',
          'pricing',
          'admissions',
          'legends',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2',
        ],
      },
    }

    const expectedResult = {
      admissions: {
        '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
      },
      legends: [
        {
          description: 'Super Premium Plus standard',
          group: 'Super Premium Plus group standard',
          id: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
        },
      ],
      performanceDate: {
        dateString: 'start_date_display',
        dateTimeFull: 'start_date_standard',
      },
      popupMessage: '',
      priceTypes: {
        '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
      },
      pricing: {
        '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
      },
      uplift: {
        bubblePricing: '',
        dynamicPricing: false,
        serviceFeeThreshold: '',
      },
    }

    const result = await v2loadSeatmapScreen(apiRequestStub, params)

    expect(apiRequestStub.calledWith('map', expectedReqBody)).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })

  it('with uplift data', async () => {
    const params = {
      performanceId: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
      screenId: 'EA34C73C-CA65-4052-A63E-2AFCD692C657',
      promoCodeId: undefined,
    }

    const apiRequestStub = sinon.stub().resolves({
      data: {
        'pricetypes': {
          'state': '16',
          '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
        },
        'pricing': {
          'state': '16',
          '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
        },
        'admissions': {
          'state': '16',
          '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
        },
        'legends': {
          'state': '16',
          '47DABA72-D5C2-4AA6-B652-615533600992': {
            value_legend_id: {
              standard: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
              input: '',
              display: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_display',
            },
            description: {
              standard: 'Super Premium Plus standard',
              input: '',
              display: 'Super Premium Plus display',
            },
            group: {
              standard: 'Super Premium Plus group standard',
              input: '',
              display: 'Super Premium Plus group display',
            },
          },
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date': {
          standard: ['start_date_standard'],
          input: ['start_date_input'],
          display: ['start_date_display'],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7': {
          standard: 'on',
          input: '',
          display: 'on',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2': {
          standard: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
          input: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
          display: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2': {
          standard:
            '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
          input: '',
          display:
            '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
        },
      },
    })

    const expectedReqBody = {
      req: {
        actions: [
          {
            method: 'loadMap',
            params: {
              performance_ids: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              screen_id: 'EA34C73C-CA65-4052-A63E-2AFCD692C657',
              entered_promo_code_id: undefined,
            },
          },
        ],
        get: [
          'pricetypes',
          'pricing',
          'admissions',
          'legends',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2',
        ],
      },
    }

    const expectedResult = {
      admissions: {
        '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
      },
      legends: [
        {
          description: 'Super Premium Plus standard',
          group: 'Super Premium Plus group standard',
          id: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
        },
      ],
      performanceDate: {
        dateString: 'start_date_display',
        dateTimeFull: 'start_date_standard',
      },
      popupMessage: '',
      priceTypes: {
        '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
      },
      pricing: {
        '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
      },
      uplift: {
        dynamicPricing: true,
        bubblePricing: [
          '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
          '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
          '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
          '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
          '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
          '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
          'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
          'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
          'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
          'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
          'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
          'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
        ],
        serviceFeeThreshold:
          '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
      },
    }

    const result = await v2loadSeatmapScreen(apiRequestStub, params)

    expect(apiRequestStub.calledWith('map', expectedReqBody)).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })
})

describe('v2loadSeatmapExtract', () => {
  it('with legends data', async () => {
    const params = {
      performanceId: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
      promoCodeId: undefined,
    }

    const apiRequestStub = sinon.stub().resolves({
      data: {
        'pricetypes': {
          'state': '16',
          '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
        },
        'pricing': {
          'state': '16',
          '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
        },
        'legends': {
          'state': '16',
          '47DABA72-D5C2-4AA6-B652-615533600992': {
            value_legend_id: {
              standard: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
              input: '',
              display: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_display',
            },
            description: {
              standard: 'Super Premium Plus standard',
              input: '',
              display: 'Super Premium Plus display',
            },
            group: {
              standard: 'Super Premium Plus group standard',
              input: '',
              display: 'Super Premium Plus group display',
            },
          },
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date': {
          standard: ['start_date_standard'],
          input: ['start_date_input'],
          display: ['start_date_display'],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2': {
          standard: '',
          input: '',
          display: '',
        },
      },
      return: [
        {
          method: 'extract',
          message: '',
          values: [
            {
              name: 'result',
              value: {
                '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
              },
            },
          ],
        },
      ],
    })

    const expectedReqBody = {
      req: {
        actions: [
          {
            method: 'extract',
            params: {
              performance_ids: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              entered_promo_code_id: undefined,
              templateName: 'Bolt Map Extract',
              contentType: 'application/json',
            },
          },
        ],
        get: [
          'pricetypes',
          'pricing',
          'admissions',
          'legends',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2',
        ],
      },
    }

    const expectedResult = {
      admissions: {
        '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
      },
      legends: [
        {
          description: 'Super Premium Plus standard',
          group: 'Super Premium Plus group standard',
          id: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
        },
      ],
      performanceDate: {
        dateString: 'start_date_display',
        dateTimeFull: 'start_date_standard',
      },
      popupMessage: '',
      priceTypes: {
        '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
      },
      pricing: {
        '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
      },
      uplift: {
        bubblePricing: '',
        dynamicPricing: false,
        serviceFeeThreshold: '',
      },
    }

    const result = await v2loadSeatmapExtract(apiRequestStub, params)

    expect(apiRequestStub.calledWith('map', expectedReqBody)).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })

  it('with promocode data', async () => {
    const params = {
      performanceId: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
      promoCodeId: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
    }

    const apiRequestStub = sinon.stub().resolves({
      data: {
        'pricetypes': {
          'state': '16',
          '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
        },
        'pricing': {
          'state': '16',
          '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
        },
        'legends': {
          'state': '16',
          '47DABA72-D5C2-4AA6-B652-615533600992': {
            value_legend_id: {
              standard: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
              input: '',
              display: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_display',
            },
            description: {
              standard: 'Super Premium Plus standard',
              input: '',
              display: 'Super Premium Plus display',
            },
            group: {
              standard: 'Super Premium Plus group standard',
              input: '',
              display: 'Super Premium Plus group display',
            },
          },
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date': {
          standard: ['start_date_standard'],
          input: ['start_date_input'],
          display: ['start_date_display'],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2': {
          standard: '',
          input: '',
          display: '',
        },
      },
      return: [
        {
          method: 'extract',
          message: '',
          values: [
            {
              name: 'result',
              value: {
                '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
              },
            },
          ],
        },
      ],
    })

    const expectedReqBody = {
      req: {
        actions: [
          {
            method: 'extract',
            params: {
              performance_ids: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              entered_promo_code_id: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
              templateName: 'Bolt Map Extract',
              contentType: 'application/json',
            },
          },
        ],
        get: [
          'pricetypes',
          'pricing',
          'admissions',
          'legends',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2',
        ],
      },
    }

    const expectedResult = {
      admissions: {
        '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
      },
      legends: [
        {
          description: 'Super Premium Plus standard',
          group: 'Super Premium Plus group standard',
          id: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
        },
      ],
      performanceDate: {
        dateString: 'start_date_display',
        dateTimeFull: 'start_date_standard',
      },
      popupMessage: '',
      priceTypes: {
        '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
      },
      pricing: {
        '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
      },
      uplift: {
        bubblePricing: '',
        dynamicPricing: false,
        serviceFeeThreshold: '',
      },
    }

    const result = await v2loadSeatmapExtract(apiRequestStub, params)

    expect(apiRequestStub.calledWith('map', expectedReqBody)).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })

  it('with uplift data', async () => {
    const params = {
      performanceId: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
      promoCodeId: undefined,
    }

    const apiRequestStub = sinon.stub().resolves({
      data: {
        'pricetypes': {
          'state': '16',
          '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
        },
        'pricing': {
          'state': '16',
          '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
        },
        'admissions': {
          'state': '16',
          '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
        },
        'legends': {
          'state': '16',
          '47DABA72-D5C2-4AA6-B652-615533600992': {
            value_legend_id: {
              standard: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
              input: '',
              display: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_display',
            },
            description: {
              standard: 'Super Premium Plus standard',
              input: '',
              display: 'Super Premium Plus display',
            },
            group: {
              standard: 'Super Premium Plus group standard',
              input: '',
              display: 'Super Premium Plus group display',
            },
          },
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date': {
          standard: ['start_date_standard'],
          input: ['start_date_input'],
          display: ['start_date_display'],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage': {
          standard: '',
          input: '',
          display: '',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7': {
          standard: 'on',
          input: '',
          display: 'on',
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2': {
          standard: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
          input: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
          display: [
            '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
            '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
            '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
            '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
            '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
            '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
            'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
            'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
            'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
            'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
            'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
            'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
          ],
        },
        'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2': {
          standard:
            '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
          input: '',
          display:
            '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
        },
      },
      return: [
        {
          method: 'extract',
          message: '',
          values: [
            {
              name: 'result',
              value: {
                '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
              },
            },
          ],
        },
      ],
    })

    const expectedReqBody = {
      req: {
        actions: [
          {
            method: 'extract',
            params: {
              performance_ids: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              entered_promo_code_id: undefined,
              templateName: 'Bolt Map Extract',
              contentType: 'application/json',
            },
          },
        ],
        get: [
          'pricetypes',
          'pricing',
          'admissions',
          'legends',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::start_date',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::popupmessage',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::data7',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::multidata2',
          'performances::C8F77B7C-EC06-4301-861F-C4998E1C7A8D::comment2',
        ],
      },
    }

    const expectedResult = {
      admissions: {
        '8A448F7D-FCA7-43A9-A7DF-24EBE440A572': {},
      },
      legends: [
        {
          description: 'Super Premium Plus standard',
          group: 'Super Premium Plus group standard',
          id: '8D42EF61-054C-4DCA-86ED-9320F9D9C800_standard',
        },
      ],
      performanceDate: {
        dateString: 'start_date_display',
        dateTimeFull: 'start_date_standard',
      },
      popupMessage: '',
      priceTypes: {
        '767DF1A1-F9D2-4058-8D8B-1F0170C96399': {},
      },
      pricing: {
        '41FB940E-E7E7-431A-A7D7-4FDFD470AD68': {},
      },
      uplift: {
        dynamicPricing: true,
        bubblePricing: [
          '00501AC4-E7E7-45BE-9432-891BB891335E,235.00',
          '15E9414A-051D-4793-9FBC-2B64DCE28000,165.00',
          '1CD2F3F5-21EC-4D5D-B215-BA0972760F3A,185.00',
          '4D78379A-A719-4B81-8254-C7A8C7C3743B,285.00',
          '558C30DF-9114-4C89-97E3-8C605B2FF3EF,185.00',
          '8DD12575-9800-4513-A6D6-EA3C5F2506F3,155.00',
          'A6BAB700-7066-44F2-841C-681E06C2B7A2,235.00',
          'B2CD858E-7303-4AC2-9F7C-B8B5E2A69C82,295.00',
          'D1016DF8-AF08-4075-8301-30AE958144E2,395.00',
          'E07E4608-4508-4A68-9D26-569741644FE2,295.00',
          'F896E12F-2F01-418D-8C19-E9C6FC0148A4,395.00',
          'FF6D8B01-E26B-43D8-8DB9-370032C14C0F,285.00',
        ],
        serviceFeeThreshold:
          '0.00-58.00=9.00;58.01-118.00=11.00;118.01-158.00=13.00;158.01-198.00=14.00;198.01-249.00=16.00;249.01-999.99=18.00',
      },
    }

    const result = await v2loadSeatmapExtract(apiRequestStub, params)

    expect(apiRequestStub.calledWith('map', expectedReqBody)).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })
})

describe('reserveMixedAdmissions', () => {
  const avSuccessResponse = {
    version: '7.48.0',
    session: 'AB0143CD-0814-4AA1-B93E-15320AD2953B',
    data: {
      Admissions: {
        'state': '24',
        'D23BCCF4-0F5E-40F5-AA21-D8D615D9DBD4': {
          state: '24',
          price_value_id: {
            standard: '5DB52E79-6D47-42C9-9FE9-DD5E10D011C2',
            input: '5DB52E79-6D47-42C9-9FE9-DD5E10D011C2',
            display: '5DB52E79-6D47-42C9-9FE9-DD5E10D011C2',
          },
          seat_id: {
            standard: 'F0DF0159-496D-4AE5-BEFC-0F0CFBC33564',
            input: 'F0DF0159-496D-4AE5-BEFC-0F0CFBC33564',
            display: 'F0DF0159-496D-4AE5-BEFC-0F0CFBC33564',
          },
          section_id: {
            standard: 'F77F48DE-5863-4B3B-83BA-E5B23E5F9DDA',
            input: 'F77F48DE-5863-4B3B-83BA-E5B23E5F9DDA',
            display: 'F77F48DE-5863-4B3B-83BA-E5B23E5F9DDA',
          },
          section: {
            standard: 'Standing',
            input: 'Standing',
            display: 'Standing',
          },
          Charges: {
            'state': '24',
            'E5F9CF3D-BB2A-4D62-9F92-9DBB81D6680D': {
              state: '24',
            },
            '6469323A-27BB-4373-9B67-28D7429B1C44': {
              state: '24',
            },
            '8DAA472D-525F-4EED-89EF-C3417CD72FAD': {
              state: '24',
            },
            '05C78C64-4546-4555-A13F-1C4B9DA10CEE': {
              state: '24',
            },
            '6CCA0DEB-9F96-4E6A-BD15-1CA55997DF6B': {
              state: '24',
            },
          },
        },
        'C3D5EEDA-2882-4AF2-B4CE-4B5D779C8433': {
          price_value_id: {
            standard: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            input: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
            display: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          },
          performance_id: {
            standard: '52672EB3-7F79-4B4C-A0BE-C959550B6C85',
            input: '52672EB3-7F79-4B4C-A0BE-C959550B6C85',
            display: '52672EB3-7F79-4B4C-A0BE-C959550B6C85',
          },
          seat_id: {
            standard: 'B0F2B78C-6C76-4436-AE78-FA8F4AA80026',
            input: 'B0F2B78C-6C76-4436-AE78-FA8F4AA80026',
            display: 'B0F2B78C-6C76-4436-AE78-FA8F4AA80026',
          },
          section_id: {
            standard: 'F87B9F70-4A33-44ED-B79C-5F879E4B73CC',
            input: 'F87B9F70-4A33-44ED-B79C-5F879E4B73CC',
            display: 'F87B9F70-4A33-44ED-B79C-5F879E4B73CC',
          },
          section: {
            standard: 'East Balcony',
            input: 'East Balcony',
            display: 'East Balcony',
          },
          price_type_id: {
            standard: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
            input: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
            display: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
          },
          Charges: {
            'state': '24',
            '76A3D092-18A9-4A78-BCE5-C9859319474D': {
              amount: {
                standard: '3.56',
                input: '3.56',
                display: '£3.56',
              },
              net: {
                standard: '3.56',
                input: '3.56',
                display: '£3.56',
              },
              total: {
                standard: '3.56',
                input: '3.56',
                display: '£3.56',
              },
              type: {
                standard: '11',
                input: '11',
                display: '11',
              },
            },
          },
        },
      },
      ServiceChargeDetails: {
        'state': '16',
        'F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E': {},
        '77D4EF89-EB1D-498D-A52F-34E32BEB8968': {
          state: '16',
          service_charge_id: {
            standard: '77D4EF89-EB1D-498D-A52F-34E32BEB8968',
            input: '',
            display: '77D4EF89-EB1D-498D-A52F-34E32BEB8968',
          },
          name: {
            standard: 'One Nighters (2.90 - 3.90): Per Ticket Fee',
            input: '',
            display: 'One Nighters (2.90 - 3.90): Per Ticket Fee',
          },
        },
        '298AA45E-B1A0-460B-9218-CA35A86A3F4C': {
          service_charge_id: {
            standard: '298AA45E-B1A0-460B-9218-CA35A86A3F4C',
            input: '',
            display: '298AA45E-B1A0-460B-9218-CA35A86A3F4C',
          },
          name: {
            standard: 'Ticket Protection',
            input: '',
            display: 'Ticket Protection',
          },
        },
        'B4ACE8F4-0A9A-4C6F-A098-4135C2E82527': {
          state: '16',
          name: {
            standard: 'VAT 12.5%',
            input: '',
            display: 'VAT 12.5%',
          },
        },
        '3A6212D6-9A30-45EC-B3E2-16940F765D05': {
          name: {
            standard: 'VAT 20%',
            input: '',
            display: 'VAT 20%',
          },
        },
        '713DD4BA-01E2-419C-8226-ADA8805F7B2F': {
          name: {
            standard: 'ATG Restoration Levy: £1.50',
            input: '',
            display: 'ATG Restoration Levy: £1.50',
          },
        },
      },
      Questions: {
        state: '0',
      },
      DeliveryMethodDetails: {
        'state': '16',
        '30F85C7A-D114-4F86-97D8-35898B292C29': {
          deliverymethod_id: {
            standard: '30F85C7A-D114-4F86-97D8-35898B292C29',
            input: '',
            display: '30F85C7A-D114-4F86-97D8-35898B292C29',
          },
          name: {
            standard: 'eTicket (Deferred)',
            input: '',
            display: 'eTicket (Deferred)',
          },
        },
        'AvailablePaymentMethods': {
          'E0384EDB-72C7-412E-B32D-338B88D996FF': {
            name: {
              standard: 'Worldpay Websales - Mastercard',
              input: '',
              display: 'Worldpay Websales - Mastercard',
            },
            type: {
              standard: 'CreditCard',
              input: '',
              display: 'CreditCard',
            },
          },
          'E9151D89-8686-4881-8C47-555B91E067DC': {
            name: {
              standard: 'Worldpay Websales - American Express',
              input: '',
              display: 'Worldpay Websales - American Express',
            },
            type: {
              standard: 'CreditCard',
              input: '',
              display: 'CreditCard',
            },
          },
          'CE929B3B-805D-4B16-A599-5120C4C67EB4': {
            name: {
              standard: 'PayPal',
              input: '',
              display: 'PayPal',
            },
            type: {
              standard: 'PayPal',
              input: '',
              display: 'PayPal',
            },
          },
        },
      },
      return: [
        {
          method: 'getBestAvailable',
          message: '',
        },
        {
          method: 'manageAdmissions',
          message: '',
        },
      ],
    },
  }

  const generalAdmissions = [
    {
      priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
      sectionName: 'Standing',
      priceZone: '5DB52E79-6D47-42C9-9FE9-DD5E10D011C2',
      ticketPrice: '55.00',
      number: 1,
      benefitId: '',
      promoCodeId: '',
    },
  ]

  const seats = {
    'B0F2B78C-6C76-4436-AE78-FA8F4AA80026': {
      benefitId: '',
      priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
      promoCodeId: '',
      ticketPrice: '29.40',
    },
  }

  it('should call api with correct params', async () => {
    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const apiRequestStub = sinon.stub().resolves(avSuccessResponse)

    await reserveMixedAdmissions(
      apiRequestStub,
      generalAdmissions,
      seats,
      performanceId,
      USER_AGENT
    )

    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args[0]).toBe('order')
    expect(apiCall.args[1]).toStrictEqual({
      req: {
        actions: [
          {
            method: 'getBestAvailable',
            params: {
              'optNum': '1',
              'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
              'priceValue': '5DB52E79-6D47-42C9-9FE9-DD5E10D011C2',
              'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': '1',
              'reqRows': '1',
              'section_name': 'Standing',
            },
          },
          {
            method: 'manageAdmissions',
            params: {
              'addSeatID::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D': [
                'B0F2B78C-6C76-4436-AE78-FA8F4AA80026',
              ],
              'performanceID': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
            },
          },
        ],
        objectName: expect.any(String),
        get: [
          'Admissions',
          'ServiceChargeDetails',
          'Questions',
          'DeliveryMethodDetails',
          'AvailablePaymentMethods',
          'Order',
        ],
        session: {
          set: {
            userAgent: 'userAgent',
          },
        },
      },
    })
  })

  it('should throw an error if av reservation fails', async () => {
    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const apiRequestStub = sinon.stub().resolves(avFailureResponse)

    await expect(
      reserveMixedAdmissions(
        apiRequestStub,
        generalAdmissions,
        seats,
        performanceId,
        USER_AGENT
      )
    ).rejects.toThrow(
      new AvAtgError('AV failure, mixed admission reservation failed')
    )
  })

  it('show reserve mixed admissions with promocodes and hiddenPromoCodes', async () => {
    const generalAdmissions = [
      {
        sectionName: 'Standing',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        number: 2,
        priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
        ticketPrice: '75.00',
        benefitId: '',
        promoCodeId: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
      },
    ]

    const seats = {
      'B0F2B78C-6C76-4436-AE78-FA8F4AA80026': {
        benefitId: '',
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '8798C793-7FAB-44C1-B4F6-2AB7CE197A77',
        ticketPrice: '29.40',
      },
    }

    const performanceId = 'D48CD5A8-719E-427B-A696-B9F2CD3793EF'
    const apiRequestStub = sinon.stub().resolves(avSuccessResponse)

    await reserveMixedAdmissions(
      apiRequestStub,
      generalAdmissions,
      seats,
      performanceId,
      USER_AGENT
    )

    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args[0]).toBe('order')
    expect(apiCall.args[1]).toStrictEqual({
      req: {
        actions: [
          {
            method: 'getBestAvailable',
            params: {
              'optNum': '1',
              'perfVector': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
              'priceValue': 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
              'reqNum::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D::8798C793-7FAB-44C1-B4F6-2AB7CE197A77':
                '2',
              'reqRows': '1',
              'section_name': 'Standing',
            },
          },
          {
            method: 'manageAdmissions',
            params: {
              'addSeatID::B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D::8798C793-7FAB-44C1-B4F6-2AB7CE197A77':
                ['B0F2B78C-6C76-4436-AE78-FA8F4AA80026'],
              'performanceID': 'D48CD5A8-719E-427B-A696-B9F2CD3793EF',
            },
          },
        ],
        objectName: expect.any(String),
        get: [
          'Admissions',
          'ServiceChargeDetails',
          'Questions',
          'DeliveryMethodDetails',
          'AvailablePaymentMethods',
          'Order',
        ],
        session: {
          set: {
            userAgent: 'userAgent',
          },
        },
      },
    })
  })
})

describe('v2BubbleAvailabilitySearch', () => {
  it('should be called with uplift data', async () => {
    const performanceId = 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D'
    const bubbleHoldIds = [
      '9F2D709A-945D-428F-806F-B8DB7B157931',
      '12465F8F-FC75-495E-914B-BB851365661E',
    ]

    const apiRequestStub = sinon.stub().resolves({
      data: {
        Result: {
          1: {
            'state': '32',
            'admission_hold_value_id': {
              standard: '9F2D709A-945D-428F-806F-B8DB7B157931',
              input: '',
              display: '9F2D709A-945D-428F-806F-B8DB7B157931',
            },
            'admission_price_value_id': {
              standard: '674D512E-9B45-441F-AE4C-53017FFB33F9',
              input: '',
              display: '674D512E-9B45-441F-AE4C-53017FFB33F9',
            },
            'COUNT_admission_id': {
              standard: '20',
              input: '',
              display: '20',
            },
            'COUNT_OrderAdmission.orderadmission_admission_id': {
              standard: '0',
              input: '',
              display: '0',
            },
            'Seat.Section.section_stand': {
              standard: 'Loge',
              input: '',
              display: 'Loge',
            },
          },
          2: {
            'state': '32',
            'admission_hold_value_id': {
              standard: '12465F8F-FC75-495E-914B-BB851365661E',
              input: '',
              display: '12465F8F-FC75-495E-914B-BB851365661E',
            },
            'admission_price_value_id': {
              standard: '674D512E-9B45-441F-AE4C-53017FFB33F9',
              input: '',
              display: '674D512E-9B45-441F-AE4C-53017FFB33F9',
            },
            'COUNT_admission_id': {
              standard: '11',
              input: '',
              display: '11',
            },
            'COUNT_OrderAdmission.orderadmission_admission_id': {
              standard: '0',
              input: '',
              display: '0',
            },
            'Seat.Section.section_stand': {
              standard: 'Loge',
              input: '',
              display: 'Loge',
            },
          },
        },
      },
      return: [
        {
          method: 'search',
          message: '',
        },
      ],
    })

    const expectedReqBody = {
      req: {
        set: {
          'Search::object': 'ts_admission',
          'Search::page_size': 100,
          'Query': {
            ResultMember: {
              '+1': {
                name: 'admission_hold_value_id',
                order: 1,
                option: 5,
              },
              '+2': {
                name: 'admission_price_value_id',
                order: 2,
                option: 5,
              },
              '+3': {
                name: 'admission_id',
                order: 3,
                function: 101,
              },
              '+4': {
                name: 'OrderAdmission.orderadmission_admission_id',
                order: 4,
                function: 101,
              },
              '+5': {
                name: 'Seat.Section.section_stand',
                order: 5,
              },
            },
            Clause: {
              '+1': {
                name: 'admission_performance_id',
                type: 'matchCondition',
                oper: '=',
                value: 'C8F77B7C-EC06-4301-861F-C4998E1C7A8D',
              },
              '+2': {
                name: 'admission_hold_value_id',
                type: 'matchCondition',
                oper: '=',
                value: [
                  '9F2D709A-945D-428F-806F-B8DB7B157931',
                  '12465F8F-FC75-495E-914B-BB851365661E',
                ],
              },
            },
          },
        },
        actions: [
          {
            method: 'search',
            acceptWarnings: ['4276'],
          },
        ],
        get: ['Result'],
      },
    }

    const expectedResult = {
      '9F2D709A-945D-428F-806F-B8DB7B157931': {
        holdId: '9F2D709A-945D-428F-806F-B8DB7B157931',
        total: 20,
        available: 20,
        zones: ['674D512E-9B45-441F-AE4C-53017FFB33F9'],
        standAvailability: [
          {
            available: 20,
            stand: 'Loge',
            total: 20,
          },
        ],
      },
      '12465F8F-FC75-495E-914B-BB851365661E': {
        holdId: '12465F8F-FC75-495E-914B-BB851365661E',
        total: 11,
        available: 11,
        zones: ['674D512E-9B45-441F-AE4C-53017FFB33F9'],
        standAvailability: [
          {
            available: 11,
            stand: 'Loge',
            total: 11,
          },
        ],
      },
    }

    const result = await v2BubbleAvailabilitySearch(
      apiRequestStub,
      performanceId,
      bubbleHoldIds
    )

    expect(
      apiRequestStub.calledWithExactly('search', expectedReqBody)
    ).toBeTruthy()
    expect(result).toStrictEqual(expectedResult)
  })
})

const availabilityData = {
  version: '7.49.3',
  session: 'DF2E1035-B2B7-4C42-9CAB-24959A763AE3',
  data: {
    Result: {
      1: {
        'state': '32',
        'admission_hold_value_id': {
          standard: '8D718470-1777-4865-8040-11EFAF2C649A',
          input: '',
          display: '8D718470-1777-4865-8040-11EFAF2C649A',
        },
        'admission_price_value_id': {
          standard: '84B55244-7519-4C08-9604-CD3BC487F20F',
          input: '',
          display: '84B55244-7519-4C08-9604-CD3BC487F20F',
        },
        'COUNT_admission_id': {
          standard: '18',
          input: '',
          display: '18',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '1',
          input: '',
          display: '1',
        },
        'Seat.Section.section_stand': {
          standard: 'Section 2',
          input: '',
          display: 'Section 2',
        },
      },
      2: {
        'state': '32',
        'admission_hold_value_id': {
          standard: '2830A04D-9593-4094-919B-293731890B45',
          input: '',
          display: '2830A04D-9593-4094-919B-293731890B45',
        },
        'admission_price_value_id': {
          standard: '293548B9-54CE-48C0-8C22-27526D987CBD',
          input: '',
          display: '293548B9-54CE-48C0-8C22-27526D987CBD',
        },
        'COUNT_admission_id': {
          standard: '17',
          input: '',
          display: '17',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '17',
          input: '',
          display: '17',
        },
        'Seat.Section.section_stand': {
          standard: 'Pit',
          input: '',
          display: 'Pit',
        },
      },
      3: {
        'state': '32',
        'admission_hold_value_id': {
          standard: 'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3',
          input: '',
          display: 'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3',
        },
        'admission_price_value_id': {
          standard: '293548B9-54CE-48C0-8C22-27526D987CBD',
          input: '',
          display: '293548B9-54CE-48C0-8C22-27526D987CBD',
        },
        'COUNT_admission_id': {
          standard: '12',
          input: '',
          display: '12',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '10',
          input: '',
          display: '10',
        },
        'Seat.Section.section_stand': {
          standard: 'Pit',
          input: '',
          display: 'Pit',
        },
      },
      4: {
        'state': '32',
        'admission_hold_value_id': {
          standard: 'D0DDD0C1-BE1D-4EDA-A385-50DDC978FAB8',
          input: '',
          display: 'D0DDD0C1-BE1D-4EDA-A385-50DDC978FAB8',
        },
        'admission_price_value_id': {
          standard: '58EEC848-11A0-4D29-AFD6-6F0186224B0C',
          input: '',
          display: '58EEC848-11A0-4D29-AFD6-6F0186224B0C',
        },
        'COUNT_admission_id': {
          standard: '27',
          input: '',
          display: '27',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '0',
          input: '',
          display: '0',
        },
        'Seat.Section.section_stand': {
          standard: 'Section 2',
          input: '',
          display: 'Section 2',
        },
      },
      5: {
        'state': '32',
        'admission_hold_value_id': {
          standard: '93DF3BDA-E6F0-40F1-9F48-698977B57D76',
          input: '',
          display: '93DF3BDA-E6F0-40F1-9F48-698977B57D76',
        },
        'admission_price_value_id': {
          standard: '84B55244-7519-4C08-9604-CD3BC487F20F',
          input: '',
          display: '84B55244-7519-4C08-9604-CD3BC487F20F',
        },
        'COUNT_admission_id': {
          standard: '17',
          input: '',
          display: '17',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '6',
          input: '',
          display: '6',
        },
        'Seat.Section.section_stand': {
          standard: 'Section 2',
          input: '',
          display: 'Section 2',
        },
      },
      6: {
        'state': '32',
        'admission_hold_value_id': {
          standard: 'F1325E51-6F21-4F10-95F9-D9B2515AEF0D',
          input: '',
          display: 'F1325E51-6F21-4F10-95F9-D9B2515AEF0D',
        },
        'admission_price_value_id': {
          standard: '84B55244-7519-4C08-9604-CD3BC487F20F',
          input: '',
          display: '84B55244-7519-4C08-9604-CD3BC487F20F',
        },
        'COUNT_admission_id': {
          standard: '25',
          input: '',
          display: '25',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '0',
          input: '',
          display: '0',
        },
        'Seat.Section.section_stand': {
          standard: 'Section 2',
          input: '',
          display: 'Section 2',
        },
      },
      // Same hold and price zone, but different section
      7: {
        'state': '32',
        'admission_hold_value_id': {
          standard: 'F1325E51-6F21-4F10-95F9-D9B2515AEF0D',
          input: '',
          display: 'F1325E51-6F21-4F10-95F9-D9B2515AEF0D',
        },
        'admission_price_value_id': {
          standard: '58EEC848-11A0-4D29-AFD6-6F0186224B0C',
          input: '',
          display: '58EEC848-11A0-4D29-AFD6-6F0186224B0C',
        },
        'COUNT_admission_id': {
          standard: '25',
          input: '',
          display: '25',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '20',
          input: '',
          display: '20',
        },
        'Seat.Section.section_stand': {
          standard: 'Pit',
          input: '',
          display: 'Pit',
        },
      },
      // Same hold and price zone, but different section
      8: {
        'state': '32',
        'admission_hold_value_id': {
          standard: 'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3',
          input: '',
          display: 'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3',
        },
        'admission_price_value_id': {
          standard: '293548B9-54CE-48C0-8C22-27526D987CBD',
          input: '',
          display: '293548B9-54CE-48C0-8C22-27526D987CBD',
        },
        'COUNT_admission_id': {
          standard: '12',
          input: '',
          display: '12',
        },
        'COUNT_OrderAdmission.orderadmission_admission_id': {
          standard: '8',
          input: '',
          display: '8',
        },
        'Seat.Section.section_stand': {
          standard: 'Section 1',
          input: '',
          display: 'Section 1',
        },
      },
      state: '0',
    },
  },
  return: [
    {
      method: 'search',
      message: '',
    },
  ],
}

describe('extractBubbleAvailabilityData', () => {
  it('should return the availability grouped by zone, even for duplicate zones and stands', () => {
    const result = extractBubbleAvailabilityData(availabilityData)

    expect(result).toStrictEqual({
      '8D718470-1777-4865-8040-11EFAF2C649A': {
        holdId: '8D718470-1777-4865-8040-11EFAF2C649A',
        total: 18,
        available: 17,
        zones: ['84B55244-7519-4C08-9604-CD3BC487F20F'],
        standAvailability: [
          {
            stand: 'Section 2',
            total: 18,
            available: 17,
          },
        ],
      },
      '2830A04D-9593-4094-919B-293731890B45': {
        holdId: '2830A04D-9593-4094-919B-293731890B45',
        total: 17,
        available: 0,
        zones: ['293548B9-54CE-48C0-8C22-27526D987CBD'],
        standAvailability: [
          {
            stand: 'Pit',
            total: 17,
            available: 0,
          },
        ],
      },
      'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3': {
        holdId: 'AF8EC1CD-8F42-46C5-89D9-4D652CA985F3',
        total: 24,
        available: 6, // sum of the stands and zones
        zones: ['293548B9-54CE-48C0-8C22-27526D987CBD'],
        standAvailability: [
          {
            stand: 'Pit',
            total: 12,
            available: 2,
          },
          {
            stand: 'Section 1',
            total: 12,
            available: 4,
          },
        ],
      },
      'D0DDD0C1-BE1D-4EDA-A385-50DDC978FAB8': {
        holdId: 'D0DDD0C1-BE1D-4EDA-A385-50DDC978FAB8',
        total: 27,
        available: 27,
        zones: ['58EEC848-11A0-4D29-AFD6-6F0186224B0C'],
        standAvailability: [
          {
            stand: 'Section 2',
            total: 27,
            available: 27,
          },
        ],
      },
      '93DF3BDA-E6F0-40F1-9F48-698977B57D76': {
        holdId: '93DF3BDA-E6F0-40F1-9F48-698977B57D76',
        total: 17,
        available: 11,
        zones: ['84B55244-7519-4C08-9604-CD3BC487F20F'],
        standAvailability: [
          {
            stand: 'Section 2',
            total: 17,
            available: 11,
          },
        ],
      },
      'F1325E51-6F21-4F10-95F9-D9B2515AEF0D': {
        holdId: 'F1325E51-6F21-4F10-95F9-D9B2515AEF0D',
        total: 50,
        available: 30,
        zones: [
          '84B55244-7519-4C08-9604-CD3BC487F20F',
          '58EEC848-11A0-4D29-AFD6-6F0186224B0C',
        ],
        standAvailability: [
          {
            stand: 'Section 2',
            total: 25,
            available: 25,
          },
          {
            stand: 'Pit',
            total: 25,
            available: 5,
          },
        ],
      },
    })
  })
})

describe('getFraudPreventionParams', () => {
  it('should return fraud prevention params from payment client config', async () => {
    const paymentMethodsIds = [
      '1CECF3C5-C439-4137-BA96-38BCB5214292',
      '5D4567A1-25AF-41D4-AA70-91FE9B7F781D',
      'CE929B3B-805D-4B16-A599-5120C4C67EB4',
      '030ED457-2A17-4D43-85FC-63F981AE846A',
      '17D94207-5E51-4DD0-8BA2-9EA6370A7449',
    ]

    const avSuccessResponse = {
      version: '7.71.1',
      session: '8663ACCB-B3E6-459C-9A3F-BC7A43DE8944',
      return: [
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config":{"hosted_field_ups":false,"hosted_page_ups":false,"phone_service_ups":false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config":{"hosted_field_ups":false,"hosted_page_ups":true,"phone_service_ups":false,"device_fingerprint":{"organization_id":"82t8dihc","domain":"h.online-metrix.net"}}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config":{"pay_type":"PayPal","hosted_field_ups":false,"hosted_page_ups":false,"phone_service_ups":false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config":{"hosted_field_ups":false,"hosted_page_ups":true,"phone_service_ups":false,"device_fingerprint":{"organization_id":"82t8dihc","domain":"h.online-metrix.net"}}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config":{"hosted_field_ups":false,"hosted_page_ups":true,"phone_service_ups":false,"device_fingerprint":{"organization_id":"82t8dihc","domain":"h.online-metrix.net"}}}',
              mimetype: 'text/plain',
            },
          ],
        },
      ],
    }

    const apiRequestStub = sinon.stub().resolves(avSuccessResponse)

    const response = await getFraudPreventionParams(
      apiRequestStub,
      paymentMethodsIds
    )

    const apiCall = apiRequestStub.getCall(0)

    expect(apiCall.args[0]).toBe('paymentMethod')
    expect(apiCall.args[1]).toStrictEqual({
      req: {
        objectName: 'myPaymentMethod',
        actions: [
          {
            method: 'getPaymentClientConfig',
            params: {
              payment_method_id: '1CECF3C5-C439-4137-BA96-38BCB5214292',
            },
          },
          {
            method: 'getPaymentClientConfig',
            params: {
              payment_method_id: '5D4567A1-25AF-41D4-AA70-91FE9B7F781D',
            },
          },
          {
            method: 'getPaymentClientConfig',
            params: {
              payment_method_id: 'CE929B3B-805D-4B16-A599-5120C4C67EB4',
            },
          },
          {
            method: 'getPaymentClientConfig',
            params: {
              payment_method_id: '030ED457-2A17-4D43-85FC-63F981AE846A',
            },
          },
          {
            method: 'getPaymentClientConfig',
            params: {
              payment_method_id: '17D94207-5E51-4DD0-8BA2-9EA6370A7449',
            },
          },
        ],
      },
    })

    expect(response).toMatchObject({
      organisationId: '82t8dihc',
      profileDomain: 'h.online-metrix.net',
    })
    expect(response).toHaveProperty('profilingId')
  })

  it('should return undefined if fraud prevention not configured', async () => {
    const paymentMethodsIds = [
      '1CECF3C5-C439-4137-BA96-38BCB5214292',
      '5D4567A1-25AF-41D4-AA70-91FE9B7F781D',
      'CE929B3B-805D-4B16-A599-5120C4C67EB4',
      '030ED457-2A17-4D43-85FC-63F981AE846A',
      '17D94207-5E51-4DD0-8BA2-9EA6370A7449',
    ]

    const avSuccessResponse = {
      version: '7.71.1',
      session: '8663ACCB-B3E6-459C-9A3F-BC7A43DE8944',
      return: [
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config": {"hosted_field_ups": false, "hosted_page_ups": false, "phone_service_ups": false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config": {"hosted_field_ups": false, "hosted_page_ups": false, "phone_service_ups": false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config": {"hosted_field_ups": false, "hosted_page_ups": false, "phone_service_ups": false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config": {"hosted_field_ups": false, "hosted_page_ups": false, "phone_service_ups": false}}',
              mimetype: 'text/plain',
            },
          ],
        },
        {
          method: 'getPaymentClientConfig',
          message: '',
          values: [
            {
              name: 'result',
              value:
                '{"config": {"hosted_field_ups": false, "hosted_page_ups": false, "phone_service_ups": false}}',
              mimetype: 'text/plain',
            },
          ],
        },
      ],
    }

    const apiRequestStub = sinon.stub().resolves(avSuccessResponse)

    const response = await getFraudPreventionParams(
      apiRequestStub,
      paymentMethodsIds
    )

    expect(response).toBeUndefined()
  })
})
