const {
  transactionFeeServiceCharge,
  serviceChargeDetails,
} = require('../test/fixtures/avServiceChargeDetails')

const {
  parseQuestions,
  parseQuestionsRequired,
  getQuestionsWithAnswers,
  getTransactionFeeServiceCharge,
  redactFieldValues,
} = require('./utils')

describe('av-client/utils', () => {
  it('parseQuestions', () => {
    const input = {
      'state': '16',
      '2C6D3350-2390-43FD-893C-FDA9F077C113': {
        state: '16',
        question_id: {
          standard: '112237FD-24CD-4911-B326-B7AB1DF53E49',
          input: '112237FD-24CD-4911-B326-B7AB1DF53E49',
          display: '112237FD-24CD-4911-B326-B7AB1DF53E49',
        },
        questioninstance_id: {
          standard: '2C6D3350-2390-43FD-893C-FDA9F077C113',
          input: '2C6D3350-2390-43FD-893C-FDA9F077C113',
          display: '2C6D3350-2390-43FD-893C-FDA9F077C113',
        },
        order_id: {
          standard: '7181678C-92C4-4729-884A-51C3F205EA0F',
          input: '7181678C-92C4-4729-884A-51C3F205EA0F',
          display: '7181678C-92C4-4729-884A-51C3F205EA0F',
        },
        performance_id: {
          standard: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
          input: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
          display: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
        },
        miscellaneous_item_id: { standard: '', input: '', display: '' },
        answers: { standard: '', input: '', display: '' },
        category: {
          standard: 'Marketing Opt In',
          input: 'Marketing Opt In',
          display: 'Marketing Opt In',
        },
        rank: { standard: '1', input: '1', display: '1' },
        body: {
          standard:
            'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
          input:
            'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
          display:
            'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
        },
        order_admission_id: { standard: '', input: '', display: '' },
        order_item_id: { standard: '', input: '', display: '' },
        audit_id: { standard: '', input: '', display: '' },
        create_audit_id: { standard: '', input: '', display: '' },
        association: { standard: '', input: '', display: '' },
        audit_time: { standard: '', input: '', display: '' },
        audit_user_id: { standard: '', input: '', display: '' },
        audit_user_role_id: { standard: '', input: '', display: '' },
      },
      '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D': {
        state: '16',
        question_id: {
          standard: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
          input: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
          display: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
        },
        questioninstance_id: {
          standard: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
          input: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
          display: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
        },
        order_id: {
          standard: '7181678C-92C4-4729-884A-51C3F205EA0F',
          input: '7181678C-92C4-4729-884A-51C3F205EA0F',
          display: '7181678C-92C4-4729-884A-51C3F205EA0F',
        },
        performance_id: {
          standard: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
          input: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
          display: '5CFAC5AA-5BE1-407B-9079-EE084EA12DB9',
        },
        miscellaneous_item_id: { standard: '', input: '', display: '' },
        answers: { standard: '', input: '', display: '' },
        category: {
          standard: 'Babes in arms',
          input: 'Babes in arms',
          display: 'Babes in arms',
        },
        rank: { standard: '', input: '', display: '' },
        body: {
          standard:
            'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
          input:
            'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
          display:
            'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
        },
        order_admission_id: { standard: '', input: '', display: '' },
        order_item_id: { standard: '', input: '', display: '' },
        audit_id: { standard: '', input: '', display: '' },
        create_audit_id: { standard: '', input: '', display: '' },
        association: { standard: '', input: '', display: '' },
        audit_time: { standard: '', input: '', display: '' },
        audit_user_id: { standard: '', input: '', display: '' },
        audit_user_role_id: { standard: '', input: '', display: '' },
      },
    }
    const expected = [
      {
        id: '112237FD-24CD-4911-B326-B7AB1DF53E49',
        instanceId: '2C6D3350-2390-43FD-893C-FDA9F077C113',
        body: 'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
      },
      {
        id: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
        instanceId: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
        body: 'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
      },
    ]

    const result = parseQuestions(input)

    expect(result).toStrictEqual(expected)
  })

  it('parseQuestionsRequired', () => {
    const input = {
      1: {
        state: '32',
        performancequestion_question_id: {
          standard: '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E',
          input: '',
          display: '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E',
        },
        performancequestion_required: {
          standard: '0',
          input: '',
          display: '0',
        },
      },
      2: {
        state: '46',
        performancequestion_question_id: {
          standard: '00AA051E-CB3D-4BAA-AA08-D3D737Z76543',
          input: '',
          display: '00AA051E-CB3D-4BAA-AA08-D3D737Z76543',
        },
        performancequestion_required: {
          standard: '1',
          input: '',
          display: '1',
        },
      },
      state: '0',
    }
    const expected = {
      '9BB9951E-CB3D-4BAA-AA08-D3D73751A34E': false,
      '00AA051E-CB3D-4BAA-AA08-D3D737Z76543': true,
    }

    const result = parseQuestionsRequired(input)

    expect(result).toStrictEqual(expected)
  })

  it('getQuestionsWithAnswers', () => {
    const questions = [
      {
        id: '112237FD-24CD-4911-B326-B7AB1DF53E49',
        instanceId: '2C6D3350-2390-43FD-893C-FDA9F077C113',
        body: 'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
      },
      {
        id: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
        instanceId: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
        body: 'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
      },
    ]
    const answers = [
      {
        question_id: '112237FD-24CD-4911-B326-B7AB1DF53E49',
        title: 'AEG Presents',
        answers: [
          { key: 'Yes', label: 'Yes' },
          { key: 'No', label: 'No' },
        ],
      },
      {
        question_id: 'B835EFC3-E490-4C84-8576-DE3DED179AD0',
        title: 'Babes in arms',
        answers: [
          { key: '00', label: '0' },
          { key: '01', label: '1' },
          { key: '02', label: '2' },
          { key: '03', label: '3' },
        ],
      },
    ]
    const required = {
      '112237FD-24CD-4911-B326-B7AB1DF53E49': true,
      'B835EFC3-E490-4C84-8576-DE3DED179AD0': false,
    }
    const expected = [
      {
        id: '2C6D3350-2390-43FD-893C-FDA9F077C113',
        body: 'I would like the promoter of this show, AEG Presents, to send me information by email about its latest gigs, tours &amp; festivals, access to presale tickets, competitions and exclusive offers.',
        title: 'AEG Presents',
        answers: [
          { key: 'Yes', label: 'Yes' },
          { key: 'No', label: 'No' },
        ],
        required: true,
      },
      {
        id: '31D87B16-D8C8-458E-8AA8-D99B4CC9D35D',
        body: 'Children under 18 months old go for free. How many children of this age will be attending as part of your booking?',
        title: 'Babes in arms',
        answers: [
          { key: '00', label: '0' },
          { key: '01', label: '1' },
          { key: '02', label: '2' },
          { key: '03', label: '3' },
        ],
        required: false,
      },
    ]

    const result = getQuestionsWithAnswers(questions, answers, required)

    expect(result).toStrictEqual(expected)
  })

  describe('getTransactionFeeServiceCharge', () => {
    it('returns transaction fee when description equals `Transaction Fee`', () => {
      const expected = transactionFeeServiceCharge

      const result = getTransactionFeeServiceCharge(serviceChargeDetails)

      expect(result).toStrictEqual(expected)
    })

    it('returns transaction fee when description equals `Order Fee`', () => {
      const expected = transactionFeeServiceCharge
      const orderFeeDescription = {
        standard: 'Order Fee',
        input: '',
        display: 'Order Fee',
      }

      const result = getTransactionFeeServiceCharge({
        ...serviceChargeDetails,
        'F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E': {
          ...serviceChargeDetails['F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E'],
          description: orderFeeDescription,
        },
      })

      expect(result).toStrictEqual({
        ...expected,
        description: orderFeeDescription,
      })
    })

    it('returns transaction fee when description contains `Order Processing Fee $`', () => {
      const expected = transactionFeeServiceCharge
      const orderProcessingFeeDescription = {
        standard: 'Order Processing Fee $2.00',
        input: '',
        display: 'Order Processing Fee $2.00',
      }

      const result = getTransactionFeeServiceCharge({
        ...serviceChargeDetails,
        'F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E': {
          ...serviceChargeDetails['F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E'],
          description: orderProcessingFeeDescription,
        },
      })

      expect(result).toStrictEqual({
        ...expected,
        description: orderProcessingFeeDescription,
      })
    })
  })

  describe('redactFieldValues', () => {
    const rawData = {
      userid: '123',
      password: 'abc',
      first_name: 'John',
      last_name: 'Doe',
      foo: 'bar',
    }

    const expectedObject = {
      userid: '-redacted-',
      password: '-redacted-',
      first_name: '-redacted-',
      last_name: '-redacted-',
      foo: 'bar',
    }

    it('redacts objects', () => {
      const result = redactFieldValues(rawData)

      expect(result).toStrictEqual(expectedObject)
    })

    it('redacts arrays', () => {
      const result = redactFieldValues([rawData, rawData])

      expect(result).toStrictEqual([expectedObject, expectedObject])
    })

    it('redacts nested objects and arrays', () => {
      const result = redactFieldValues({
        ...rawData,
        nested: {
          ...rawData,
        },
        nestedArray: [rawData, rawData],
      })

      expect(result).toStrictEqual({
        ...expectedObject,
        nested: {
          ...expectedObject,
        },
        nestedArray: [expectedObject, expectedObject],
      })
    })
  })
})
