'use strict'

const { resolve } = require('path')
const cp = require('child_process')
const http = require('http')

const pino = require('pino')({
  prettyPrint: true,
})

const {
  PERFORMANCE = '6A175C1D-7D36-4397-9E96-354675DDD28B',
  TICKET = 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
  SEAT = 'CA331D42-A9C0-430E-B808-F33E73366191',
  PORT = 9999,
} = process.env

const sp = cp.spawn('npm', ['start'], {
  cwd: resolve(__dirname, '..'),
  env: { ...process.env, PORT },
})
sp.stdout.pipe(process.stdout)
sp.stdout.once('data', () => {
  setTimeout(reserve, 1000)
})

function reserve() {
  const url = `http://localhost:${PORT}/reserve`
  const payload = {
    performance: PERFORMANCE,
    seats: { [SEAT]: TICKET },
  }
  pino.info({ payload }, `Posting to ${url} with:`)
  const req = http.request(
    url,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    },
    (res) => {
      res.once('data', (body) => {
        const { token } = JSON.parse(body)
        pino.info(
          { response: JSON.parse(body) },
          'received response from /reserve'
        )
        payment({ token })
      })
    }
  )
  req.end(JSON.stringify(payload))
}

function payment({ token }) {
  const url = `http://localhost:${PORT}/payment`
  const payload = {
    fname: 'Jane',
    lname: 'Doe',
    email: '<EMAIL>',
    phone: '0123456789',
    street: '123 Test Street',
    city: 'London',
    country: 'United Kingdom',
    zip: 'AB1 2CD',
    preferences: [1, 2],
    delivery: 'email',
    cc: {
      name: 'Bruce Wayne',
      number: '4242424242424242',
      expires: '0120',
      cvv: '100',
    },
  }
  const auth = `Bearer ${token}`
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': auth,
  }
  pino.info({ headers, payload }, `Posting to ${url} with:`)
  const req = http.request(
    url,
    {
      method: 'POST',
      headers,
    },
    (res) => {
      res.once('data', (body) => {
        pino.info(
          { response: JSON.parse(body) },
          'received response from /payment – flow complete'
        )
      })
    }
  )
  req.end(JSON.stringify(payload))
}

/*
Mon: `C43BE380-AD08-4DC7-BCD4-F0391ADEE16D`
Tue: `6A175C1D-7D36-4397-9E96-354675DDD28B`
Wed: `D810F953-35CE-4C58-B03A-A573AC85F9ED`
Thur: `844577A4-50EC-4512-BD4B-86BDDBA461CE`
Fri mat: `E18E7A8B-C003-4C2B-AE91-3315DB0CB781`
Fri eve: `CF6D1F07-E52B-49B1-A34A-B055BE0E4C84`
Sat mat: `37B5AE15-09A0-49F2-8EA3-D90E330E6E30`
Sat eve: `9647A55B-5869-4E2F-802F-A27CF06888C8`

For any of those performance you will be able to see Balcony J1-16 available, for variety, J1-8 is one price, and J9-16 is another:

J1 - `CA331D42-A9C0-430E-B808-F33E73366191`
J2 - `B1001FF5-DEC9-4B5F-B17A-76921E6D9C6B`
J3 - `E9D89334-A667-4A5C-B073-568E8D02A4D8`
J4 - `9801B484-9876-43BD-B124-4DA7C1E20C1B`
J5 - `D92D43B8-7F5B-4627-90DD-ED14F81FA92D`
J6 - `A41DD6B0-1B29-4214-B8A8-E493883AFF0B`
J7 - `11835CA0-4AAA-4EE9-AC11-246992D1D051`
J8 - `E5E3BC7B-39DF-4E66-A380-BE55CFB9498C`

J9 - `DD649C0E-5DBE-41CB-93A1-08AD536A0ED0`
J10 - `F0AD0598-2C47-48A9-BCA1-E99D7D7DFD4A`
J11 - `FC65822F-5CAA-43AD-AFD7-B1DA91CB8973`
J12 - `B5C27BC5-B5D7-4558-AC47-440F1E0E214F`
J13 - `40E4E924-9E7B-4B12-9999-22381FE3EF2B`
J14 - `3282CC6D-D211-4DA6-88C2-8A07172CEF58`
J15 - `5563A1FC-E0F3-4689-AEDC-3FF66BAFE406`
J16 - `4C5F2350-6F50-40EE-BD97-F6B69CE76C39`
*/
