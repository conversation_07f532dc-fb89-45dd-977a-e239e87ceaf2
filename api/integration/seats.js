'use strict'

const { resolve } = require('path')
const cp = require('child_process')
const http = require('http')

const pino = require('pino')({
  prettyPrint: true,
})

const {
  PERFORMANCE = '2ED7A551-FBC7-4A71-B214-127B071720F6',
  VENUE = 'F2A3CC81-E693-4F5B-AA18-6ADC878F8E49',
  PORT = 9999,
} = process.env

const sp = cp.spawn('npm', ['start', '--', '-l', 'info'], {
  cwd: resolve(__dirname, '..'),
  env: { ...process.env, PORT },
})

sp.stdout.once('data', () => {
  setTimeout(seats, 1000)
})

function seats() {
  const url = `http://localhost:${PORT}/tickets/${VENUE}/${PERFORMANCE}`
  pino.info(`Getting ${url}`)
  http.get(
    url,
    {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    },
    (res) => {
      const chunks = []
      res.on('data', (chunk) => chunks.push(chunk))
      res.once('end', () => {
        const body = JSON.parse(Buffer.concat(chunks))
        pino.info(
          { response: body },
          'received response from /seats – flow complete'
        )
        sp.kill()
      })
    }
  )
}
