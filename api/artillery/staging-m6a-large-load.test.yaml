# This test will check that the BOLT API infrastructure can handle 35 requests
# per second (2100 per minute), which is the current normal load for the API.
#
# Each virtual user will pick one scenario > flow at random and execute it
# (one request per virtual user).
#
# Note: We don't care for error in this current test state. We're only interested
# in the infrastructures stability under load.
config:
  target: 'https://boltapi-fargate.stage-atgtickets.com'
  http:
    # Responses have to be sent within 30 seconds
    timeout: 30
  plugins:
    # ensure:
    #   p95: 500
    #   maxErrorRate: 1
    expect: {}
  phases:
    - name: Warm up phase
      # 3 virtual users in 30 seconds (one every 10 seconds)
      duration: 30
      arrivalCount: 3
    - name: Ramp up load
      # 1 virtual users per second > 35 virtual users per second (2100 virtual
      # users per minute) over 10 minutes with no more than 35 concurrent virtual
      # users at any given time
      duration: 10m
      arrivalRate: 1
      rampTo: 35
      maxVusers: 35
    - name: Sustained load
      # 35 virtual users per second (2100 virtual users per minute) for 10 minutes
      # with no more than 35 concurrent virtual users at any given time
      duration: 5m
      arrivalRate: 35
      maxVusers: 35
scenarios:
  - flow:
      - get:
          url: '/health-check'
          capture:
            - json: '$.status'
              as: status
            - json: '$.env'
              as: env
          expect:
            - statusCode: 200
            - contentType: json
            - hasProperty: status
            - hasProperty: env
            - equals:
                - '{{ status }}'
                - 'OK'
            - equals:
                - '{{ env }}'
                - 'staging'
  - flow:
      - get:
          url: '/venues'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/venues/ambassadors-theatre'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/venues/bristol-hippodrome'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/shows'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/shows'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/shows/a-christmas-carol'
          expect:
            - statusCode: 200
  - flow:
      - get:
          url: '/shows/a-christmas-carol/studio-at-new-wimbledon-theatre'
          expect:
            - statusCode: 200
