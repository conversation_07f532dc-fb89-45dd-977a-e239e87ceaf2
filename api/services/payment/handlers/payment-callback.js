const axios = require('axios')
const jwt = require('jsonwebtoken')
const {
  generateTraceContextHeaders,
} = require('@atg-digital/server-logger-library')

const {
  AvAtgError,
  AvPaymentTokenRequiredError,
} = require('../../../lib/av-client/exceptions')
const sessionService = require('../../../lib/session-service')
const {
  processPaymentRedirectData,
} = require('../../../lib/av-client/v2-av-client/payment')
const {
  sendOrderSucceeded,
  sendOrderFailed,
} = require('../../../lib/event-bridge-client/utils')
const { getPerformanceDataById } = require('../utils/get-performance-data')
const benefitConfigService = require('../../../lib/benefits-config-service/benefits')

const { SINGLE_USE_SERVICE_ENDPOINT } = process.env

async function paymentCallbackHandler(request, reply) {
  const { body, user, avClient, avUtils, log, cookies, headers, dbClient } =
    request
  const boltDeviceId = cookies['bolt-device']
  const boltSessionId = cookies['bolt-session']
  const brand = headers['bolt-brand']
  const ip = headers['cf-connecting-ip']
  const userDataCookie = cookies['user-data']
  const userSigCookie = cookies['user-sig']
  const { session, cookie, orderId, performance } = user
  const {
    paymentResponseParams,
    referrerSource,
    singleUsePromoCode,
    showConfig,
  } = body
  const { apiRequest, closeSession } = avClient.restoreSession({
    session,
    cookie,
    log,
    isV2: true,
  })

  const {
    orderTotal,
    deliveryId,
    customer,
    availablePaymentMethods,
    payments,
  } = await avClient.fetchOrderDetails(apiRequest, orderId)

  const customerId = Object.entries(customer).filter(
    ([key]) => key !== 'state'
  )[0][1]['customer_id']['standard']

  const query = new URLSearchParams(paymentResponseParams)
  const queryObj = {}
  for (const [k, v] of query) {
    queryObj[k] = v
  }
  const avStr = avUtils.encodeAVStr(queryObj)

  const paymentId = await avClient.setPaymentTokenInfo(
    apiRequest,
    orderId,
    avStr
  )

  let reservation
  try {
    reservation = await avClient.insertOrder(
      apiRequest,
      orderId,
      deliveryId,
      undefined,
      referrerSource,
      singleUsePromoCode,
      showConfig,
      undefined,
      ip
    )

    log.info(
      {
        orderTotal,
        orderId,
        orderNumber: reservation.orderNumber,
        paymentTypes: ['callback'],
        performanceId: performance,
        singleUsePromoCode,
      },
      'Payment completed'
    )

    const basket = await sessionService.getEntireBasket(boltSessionId, brand)

    const datapumpPerformanceData = await getPerformanceDataById(
      performance,
      dbClient
    )

    let benefitData = {}
    if (userDataCookie && userSigCookie) {
      const decodedData = jwt.decode(`${userSigCookie}.${userDataCookie}`)
      if (decodedData && decodedData.benefits) {
        benefitData = await benefitConfigService.getBenefitConfigs(
          decodedData.benefits,
          benefitConfigService.fetchBenefitInfo
        )
      }
    }

    sendOrderSucceeded({
      orderTotal,
      orderNumber: reservation.orderNumber,
      performanceId: performance,
      sessionId: session,
      deviceId: boltDeviceId,
      accountId: customerId,
      basket,
      orderId,
      creationDate: new Date().toISOString(),
      payments: [
        {
          paymentId,
          type: payments[paymentId]['paymentmethod_type']['standard'],
        },
      ],
      availablePaymentMethods,
      deliveryId,
      deliveryMethodDetails: reservation.deliveryMethodDetails,
      admissions: reservation.admissions,
      performanceVenueType: datapumpPerformanceData.venueType,
      performanceDetails: reservation.performanceDetails,
      orderPayments: reservation.payments,
      benefitConfigs: benefitData.benefitConfigs,
      tickets: reservation.tickets,
      priceTypes: reservation.priceTypes,
      miscItemDetails: reservation.miscItemDetails,
      serviceChargeDetails: reservation.serviceChargeDetails,
      orderItems: reservation.orderItems,
    }).catch((err) => log.error(err))

    const setCookie = await sessionService.setOrderAsBooked(
      cookies['bolt-session'],
      cookies['user-data'] && cookies['user-sig'] ? undefined : orderId,
      brand
    )
    if (setCookie) {
      reply.header('set-cookie', setCookie)
    }

    // Burn supc on payment-callback completed
    if (singleUsePromoCode) {
      await axios
        .get(`${SINGLE_USE_SERVICE_ENDPOINT}burn/${singleUsePromoCode}`, {
          headers: generateTraceContextHeaders(),
        })
        .catch(() => {
          log.error(
            `${singleUsePromoCode} failed on burn request for order: ${orderId}`
          )

          throw new AvAtgError('Invalid singleUsePromoCode', 400)
        })
      log.info(
        `${singleUsePromoCode} has been successfully burned for order: ${orderId}`
      )
    }

    closeSession().catch(() => {})
    return reservation
  } catch (err) {
    if (err instanceof AvPaymentTokenRequiredError) {
      const { pa_request_URL, pa_request_information } =
        await avClient.fetchOrderPaymentPaRequestData(
          orderId,
          paymentId,
          apiRequest
        )

      const response = await processPaymentRedirectData(
        pa_request_URL,
        pa_request_information,
        orderId,
        closeSession
      )

      return response
    } else {
      await closeSession().catch(() => {})
      sendOrderFailed(
        {
          failureReason: err.name,
          orderTotal,
          performanceId: performance,
          sessionId: session,
          deviceId: boltDeviceId,
          accountId: cookies['bolt-account'],
        },
        log
      )
      throw err
    }
  }
}

module.exports = paymentCallbackHandler
