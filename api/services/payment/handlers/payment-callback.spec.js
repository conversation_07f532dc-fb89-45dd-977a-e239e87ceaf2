const sinon = require('sinon')
const axios = require('axios')
const jwt = require('jsonwebtoken')

const {
  AvError,
  AvPaymentCancelledError,
  AvPaymentTokenRequiredError,
} = require('../../../lib/av-client/exceptions')
const sessionService = require('../../../lib/session-service')

const mockSendOrderSucceeded = jest.fn()
const mockSendOrderFailed = jest.fn()
jest.mock('../../../lib/event-bridge-client/utils', () => ({
  sendOrderSucceeded: mockSendOrderSucceeded,
  sendOrderFailed: mockSendOrderFailed,
}))

const handler = require('./payment-callback')

jest.mock('../../../lib/benefits-config-service/benefits', () => ({
  fetchBenefitInfo: sinon.stub().resolves({}),
  getBenefitConfigs: sinon.stub().resolves({}),
}))

const sandbox = sinon.createSandbox()

const axiosStub = {
  get: sandbox.stub(axios, 'get'),
}

const userPayload = {
  exp: 1630672139084,
  iat: 1630668539084,
  email: '<EMAIL>',
  firstName: '"Basic"',
  lastName: 'User',
  avId: 'CD720E07-88B4-4B27-9D99-938455E76A20',
  benefits: [
    {
      id: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      valid_until_date: '2024-05-24',
    },
  ],
}
sinon.stub(jwt, 'decode').returns(userPayload)

const sessionStub = {
  setOrderAsBooked: sandbox.stub(sessionService, 'setOrderAsBooked').resolves(),
  getEntireBasket: sandbox.stub(sessionService, 'getEntireBasket').resolves(),
}

const supc = {
  single_use_code: 'single-use-code-parameter',
  promo_code: '1234',
}
const supc_burn = {}

const pa_request_URL = 'pa_request_URL'
const pa_request_information_Worldpay_3DS_Challenge = {
  body: { JWT: 'jwt' },
}

axiosStub.get.resolves(supc_burn)

const logStub = {
  info: () => {},
  error: () => {},
  warn: () => {},
}

const userStub = {
  session: 'session-id',
  cookie: 'cooookie',
  orderId: 'order-id',
  performance: 'performance-id',
}

const bodyStub = {
  paymentResponseParams: 'P1=V1&P2=V2',
}

const insertOrderError = new AvPaymentTokenRequiredError({
  message: '3DS Required',
})

const dbClientStub = {
  fetchPerformanceById: sinon.stub().resolves({
    venue_slug: 'venue-slug',
  }),
}

describe('payments.js', () => {
  beforeEach(() => {
    mockSendOrderSucceeded.mockResolvedValue()
  })

  afterEach(async () => {
    sandbox.reset()
  })

  it('payment callback handler re-inserts the order for 3DS response', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {},
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()
    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('payment callback handler re-inserts the order for 3DS response with provided referrerSource', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: {
        ...bodyStub,
        referrerSource: 'referrer-source',
      },
      log: logStub,
      cookies: {},
      headers: {
        'bolt-brand': 'atgtickets.com',
        'bolt-session': 'mock-bolt-session',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()

    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()

    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        'referrer-source',
        undefined,
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()

    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('payment callback handler re-inserts the order for 3DS response with provided singleUsePromoCode', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: {
        ...bodyStub,
        singleUsePromoCode: 'single-use-code-parameter',
      },
      log: logStub,
      cookies: {},
      headers: {},
    }

    axiosStub.get.resolves(supc)

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(axiosStub.get.call).toBeTruthy()

    expect(axiosStub.get.firstCall.args[0]).toContain(
      'burn/single-use-code-parameter'
    )

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        'single-use-code-parameter',
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()
    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('payment callback handler calls Session Service with bolt-session cookie on success', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()
    sessionStub.setOrderAsBooked.resetHistory()
    sessionStub.getEntireBasket.resetHistory()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-session': 'mock-bolt-session',
      },
      headers: {
        'bolt-brand': 'atgtickets',
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(sessionStub.setOrderAsBooked.calledOnce).toBeTruthy()
    expect(
      sessionStub.setOrderAsBooked.calledWithExactly(
        'mock-bolt-session',
        'order-id',
        'atgtickets'
      )
    ).toBeTruthy()
    expect(sessionStub.getEntireBasket.calledOnce).toBeTruthy()
    expect(
      sessionStub.getEntireBasket.calledWithExactly(
        'mock-bolt-session',
        'atgtickets'
      )
    ).toBeTruthy()
  })

  it('payment callback handler re-inserts the order for 3DS response with provided showConfig', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: {
        ...bodyStub,
        showConfig: 'show-config',
      },
      log: logStub,
      cookies: {},
      headers: {},
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        undefined,
        'show-config',
        undefined,
        undefined
      )
    ).toBeTruthy()
    expect(closeSessionStub.calledOnce).toBeTruthy()
  })

  it('payment callback handler returns 402 if user cancels the payment', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
    })
    const setPaymentTokenInfoStub = sinon.stub()
    const insertOrderStub = sinon.stub().rejects(AvPaymentCancelledError)
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
        'bolt-session': 'mock-bolt-session',
      },
      headers: {},
    }

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(AvPaymentCancelledError)

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()
    expect(closeSessionStub.calledOnce).toBeTruthy()

    expect(mockSendOrderFailed).toHaveBeenCalledWith(
      {
        failureReason: 'AvPaymentCancelledError',
        orderTotal: '100',
        performanceId: 'performance-id',
        sessionId: 'session-id',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('payment callback handler returns 500 if order insertion fails', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
    })
    const setPaymentTokenInfoStub = sinon.stub()
    const insertOrderStub = sinon.stub().rejects(AvError)
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
        'bolt-session': 'mock-bolt-session',
      },
      headers: {},
    }

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(AvError)

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()
    expect(closeSessionStub.calledOnce).toBeTruthy()

    expect(mockSendOrderFailed).toHaveBeenCalledWith(
      {
        failureReason: 'AvError',
        orderTotal: '100',
        performanceId: 'performance-id',
        sessionId: 'session-id',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('payment callback handler logs "payment completed" event on success', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
      payments: {
        'test-payment-id': {
          paymentmethod_type: { standard: 'test-payment-method' },
        },
      },
    })
    const setPaymentTokenInfoStub = sinon.stub().resolves('test-payment-id')
    const insertOrderStub = sinon
      .stub()
      .resolves({ orderNumber: 'my-order-number' })
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
    }

    const logInfoStub = sinon.stub()
    const requestStub = {
      avClient: avClientStub,
      dbClient: dbClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {
        'user-data': 'user-data',
        'user-sig': 'user-sig',
      },
      headers: {
        'bolt-brand': 'atgtickets.com',
        'bolt-session': 'mock-bolt-session',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toBeTruthy()

    expect(
      logInfoStub.calledOnceWithExactly(
        {
          orderTotal: '100',
          orderId: 'order-id',
          orderNumber: 'my-order-number',
          paymentTypes: ['callback'],
          performanceId: 'performance-id',
          singleUsePromoCode: undefined,
        },
        'Payment completed'
      )
    ).toBeTruthy()
  })

  it('payment callback handles Worldpay 3DS Challenge redirect', async () => {
    const closeSessionStub = sinon.stub().resolves()
    const apiRequestStub = sinon.stub()

    const fetchOrderDetailsStub = sinon.stub().resolves({
      orderTotal: '100',
      deliveryId: 'delivery-id',
      customer: {
        'test-guid': { customer_id: { standard: 'test-guid' } },
        'state': '0',
      },
      availablePaymentMethods: [],
    })

    const paymentId = 'paymentId'
    const setPaymentTokenInfoStub = sinon.stub().resolves(paymentId)
    const insertOrderStub = sinon.stub().rejects(insertOrderError)
    const restoreSessionStub = sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    })
    const fetchOrderPaymentPaRequestDataStub = sinon.stub().resolves({
      pa_request_URL,
      pa_request_information: JSON.stringify(
        pa_request_information_Worldpay_3DS_Challenge
      ),
    })

    const avClientStub = {
      restoreSession: restoreSessionStub,
      fetchOrderDetails: fetchOrderDetailsStub,
      fetchOrderPaymentPaRequestData: fetchOrderPaymentPaRequestDataStub,
      setPaymentTokenInfo: setPaymentTokenInfoStub,
      insertOrder: insertOrderStub,
    }

    const encodeAVStrStub = sinon.stub().returns('blah-encoded-string')
    const decodeAVStrStub = sinon.stub().returns('bleh-decoded-object')

    const avUtilsStub = {
      encodeAVStr: encodeAVStrStub,
      decodeAVStr: decodeAVStrStub,
    }

    const requestStub = {
      avClient: avClientStub,
      avUtils: avUtilsStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {},
    }

    const result = await handler(requestStub)

    expect(result).toStrictEqual({
      redirectTo: pa_request_URL,
      orderId: userStub.orderId,
      paymentRequestParams: {
        ...pa_request_information_Worldpay_3DS_Challenge.body,
      },
    })

    expect(
      encodeAVStrStub.calledWithExactly({ P1: 'V1', P2: 'V2' })
    ).toBeTruthy()

    expect(
      fetchOrderDetailsStub.calledWithExactly(apiRequestStub, 'order-id')
    ).toBeTruthy()
    expect(
      setPaymentTokenInfoStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'blah-encoded-string'
      )
    ).toBeTruthy()
    expect(
      insertOrderStub.calledWithExactly(
        apiRequestStub,
        'order-id',
        'delivery-id',
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      )
    ).toBeTruthy()

    expect(
      fetchOrderPaymentPaRequestDataStub.calledWithExactly(
        userStub.orderId,
        paymentId,
        apiRequestStub
      )
    ).toBeTruthy()
  })
})
