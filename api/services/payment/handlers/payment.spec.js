'use strict'

const sinon = require('sinon')
const axios = require('axios')
const jwt = require('jsonwebtoken')

process.env.CUSTOMER_ACCOUNTS_JWT_SECRET_KEY_atgtk = 'jwt-secret'
process.env.CUSTOMER_ACCOUNTS_SERVICE_SUBDOMAIN =
  'CUSTOMER_ACCOUNTS_SERVICE_SUBDOMAIN'
process.env.EVENTBRIDGE_AWS_ACCOUNT_ID = ********
process.env.APP_ENV = 'test'
process.env.SOURCE_ID = 'TEST_SOURCE_ID'

const mockInsertOrderWithDeferredDeliveryRetry = jest
  .fn()
  .mockResolvedValue({ orderNumber: 'my-order-number' })
const mockGetProfilingIdFromToken = jest.fn().mockResolvedValue('profilingId')
const mockInsertOrder = jest.fn()

jest.mock('../utils', () => ({
  ...jest.requireActual('../utils'),
  insertOrderWithDeferredDeliveryRetry:
    mockInsertOrderWithDeferredDeliveryRetry,
  getProfilingIdFromToken: mockGetProfilingIdFromToken,
}))
jest.mock('../../../lib/av-client/v2-av-client/payment', () => ({
  ...jest.requireActual('../../../lib/av-client/v2-av-client/payment'),
  insertOrder: mockInsertOrder,
}))
jest.mock('../../../lib/optimizely')

const mockSendOrderRequested = jest.fn()
const mockSendOrderSucceeded = jest.fn()
const mockSendOrderFailed = jest.fn()
jest.mock('../../../lib/event-bridge-client/utils', () => ({
  sendOrderRequested: mockSendOrderRequested,
  sendOrderSucceeded: mockSendOrderSucceeded,
  sendOrderFailed: mockSendOrderFailed,
}))

const { CUSTOMER_ACCOUNTS_SERVICE_SUBDOMAIN } = process.env

const {
  AvError,
  AvAtgError,
  AvPaymentTokenRequiredError,
  AvPaymentAmountError,
  AvBadSessionError,
  AvExceptionError,
} = require('../../../lib/av-client/exceptions')
const { eventBridgeClient } = require('../../../lib/event-bridge-client')
const sessionService = require('../../../lib/session-service')
const benefitConfigService = require('../../../lib/benefits-config-service/benefits')
const upliftUtils = require('../utils/uplift')
const {
  ticketProtectionServiceCharge,
  serviceChargeDetails,
} = require('../../../lib/test/fixtures/avServiceChargeDetails')
const mockedAvAddCustomerToOrderResponse = require('../../../lib/test/fixtures/avAddCustomerResponse.json')
const utils = require('../../../lib/av-client/utils')
const {
  parseAvAvailablePaymentMethods,
} = require('../../../lib/av-client/v2-av-client')
const { TICKET_PROTECTION_AMOUNT } = require('../../../config')
const lifecycle = require('../../../lib/loader/lifecycle')
const {
  WORLDPAY_GATEWAY_NAME,
  WORLDPAY_DDC_OPERATION_NAME,
} = require('../../../lib/utils')
const {
  AV_DELIVERY_ERROR_SUBSTRING,
} = require('../../../lib/av-delivery-error')

const handler = require('./payment')

const customerDataResponseStub = {
  headers: {
    'atg-user-token': 'test-data.test-sig',
  },
}

const axiosStub = {
  get: sinon.stub(axios, 'get'),
  post: sinon.stub(axios, 'post'),
}

const clientEventBridgeStub = {
  send: sinon.stub(eventBridgeClient, 'send'),
}

const createEventBridgeRequestStub = (logErrorStub) => {
  const avStub = createAvClientStub()
  clientEventBridgeStub.send.resetHistory()

  const userStub = createUserStub()
  userStub.seats = {
    'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
      'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
  }

  const bodyStub = createBodyStub()
  bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
  bodyStub.paymentMethod = 'creditCard'
  bodyStub.singleUsePromoCode = undefined
  bodyStub.email = '<EMAIL>'

  return {
    avClient: avStub,
    dbClient: dbClientStub,
    user: userStub,
    body: bodyStub,
    log: { ...logStub, error: logErrorStub },
    cookies: {
      'bolt-device': 'mock-bolt-device',
      'bolt-session': 'mock-bolt-session',
      'bolt-account': 'mock-bolt-account',
    },
    headers: requestHeaders,
  }
}

const sessionStub = {
  setOrderAsBooked: sinon.stub(sessionService, 'setOrderAsBooked').resolves(),
  setOrderInfoToSession: sinon
    .stub(sessionService, 'setOrderInfoToSession')
    .resolves(),
  getEntireBasket: sinon.stub(sessionService, 'getEntireBasket').resolves(),
}

const logStub = {
  info: () => {},
  error: () => {},
  warn: () => {},
}

const createUserStub = () => ({
  session: 'mock-bolt-session',
  cookie: 'cooookie',
  orderId: 'order-id',
  performance: 'performance-id',
})

const paymentDataStub = [
  {
    paymentId: 'payment-Id',
    type: 'paypal',
  },
]

const pa_request_URL = 'pa_request_URL'
const pa_request_information = '00002p100002v100002p200002v2'
const pa_request_information_Worldpay_DDC = {
  configuration: {
    gateway: WORLDPAY_GATEWAY_NAME,
    operation: WORLDPAY_DDC_OPERATION_NAME,
  },
  body: {
    Bin: 123,
    JWT: 'jwt',
  },
}

const availablePaymentMethods = parseAvAvailablePaymentMethods(
  mockedAvAddCustomerToOrderResponse
)

const createAvClientStub = () => {
  const closeSessionStub = sinon.stub().resolves()
  const apiRequestStub = sinon.stub()

  return {
    createCustomer: sinon.stub().resolves('customer-id'),
    v2InsertCustomer: sinon.stub().resolves({
      customerNumber: 'customer-number',
      customerId: 'customer-id',
    }),
    addCustomerNumberToOrder: sinon
      .stub()
      .resolves({ availablePaymentMethods, serviceChargeDetails }),
    addCustomerIdToOrder: sinon
      .stub()
      .resolves({ availablePaymentMethods, serviceChargeDetails }),
    addAdditionalItemsToOrder: sinon.stub().resolves(),
    addDynamicTicketProtection: sinon.stub().resolves(),
    addPaymentsToOrder: sinon.stub().resolves(paymentDataStub),
    insertOrder: sinon.stub().resolves({ orderNumber: 'my-order-number' }),
    fetchOrderPaymentPaRequestData: sinon.stub().resolves({
      pa_request_URL,
      pa_request_information,
    }),
    removeServiceCharge: sinon.stub().resolves(),
    restoreSession: sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    }),
    refs: {
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    },
  }
}
const createBodyStub = () => ({
  cc: 'cc-obj',
  delivery: 'post',
  deliveryType: 'eTicket',
  summary: 'summary-obj',
  additionalItems: 'additionalItems-array',
  paymentMethod: 'creditCard',
  callbackURL: 'http://www.example.com/callback',
  orderTotal: '100.30',
  answers: 'answers-obj',
  referrerSource: 'referrer-source-parameter',
  singleUsePromoCode: 'single-use-code-parameter',
  showConfig: 'show-config-parameter',
  optOut: false,
  ip: '127.0.0.1',
})

const createCookiesStub = () => ({
  'user-sig': 'test-sig',
  'user-data': 'test-data',
  'bolt-session': 'mock-bolt-session',
})

const userPayload = {
  exp: 1630672139084,
  iat: 1630668539084,
  email: '<EMAIL>',
  firstName: '"Basic"',
  lastName: 'User',
  avId: 'CD720E07-88B4-4B27-9D99-938455E76A20',
  benefits: [
    {
      id: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      valid_until_date: '2024-05-24',
    },
  ],
}

const brand = 'atgtickets.com'

const requestHeaders = {
  'cf-connecting-ip': '127.0.0.1',
  'bolt-brand': brand,
}

const jwtVerifyStub = sinon.stub(jwt, 'verify').returns(userPayload)
const jwtDecodeStub = sinon.stub(jwt, 'decode').returns(userPayload)

const dbClientStub = {
  fetchPerformanceById: sinon.stub().resolves({
    title_slug: 'title-slug',
    venue_slug: 'venue-slug',
    on_sale_date: 'on-sale-date',
    tc_on_sale_date: 'tc-on-sale-date',
    venue_data7: 'Non-ATG (Online Ticketed)',
  }),
}

describe('payments.js', () => {
  let getBenefitConfigsStub
  let massagePerformanceSpy
  let addSeatUpliftChargesToOrderStub
  let addBestAvailableUpliftedChargeToOrderStub

  beforeEach(() => {
    mockSendOrderSucceeded.mockResolvedValue()
  })

  afterEach(() => {
    if (getBenefitConfigsStub) {
      getBenefitConfigsStub.restore()
    }

    if (massagePerformanceSpy) {
      massagePerformanceSpy.restore()
    }

    if (addSeatUpliftChargesToOrderStub) {
      addSeatUpliftChargesToOrderStub.restore()
    }

    if (addBestAvailableUpliftedChargeToOrderStub) {
      addBestAvailableUpliftedChargeToOrderStub.restore()
    }
  })

  it('payment handler supports delivery method by id', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({})

    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(axiosStub.get.calledThrice).toBeTruthy()

    expect(axiosStub.get.firstCall.args[0]).toContain(
      'check/single-use-code-parameter'
    )

    expect(axiosStub.get.secondCall.args[0]).toContain('customer-data')

    expect(axiosStub.get.thirdCall.args[0]).toContain(
      'burn/single-use-code-parameter'
    )

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )
  })

  it('payment handler supports delivery method by id with "is not in list for Selected Delivery Method" error', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({})

    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.singleUsePromoCode = undefined

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    const expectedSuccessResponse = {
      orderNumber: 'order-number',
      admissions: { seat_id: 'seat-id' },
      deliveryMethodDetails: {
        delivery_time: 'delivety-time',
        name: { standard: 'eTicket' },
      },
    }

    avStub.refs.apiRequest.resolves({
      data: {
        DeliveryMethodDetails: {
          state: '16',
          deliveryId: {
            delivery_time: 'delivery-time',
            name: {
              standard: 'eTicket',
            },
          },
          deliveryIdV2: {
            delivery_time: 'delivery-time',
            name: {
              standard: 'name',
            },
          },
        },
      },
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockImplementationOnce(
      jest.requireActual('../utils').insertOrderWithDeferredDeliveryRetry
    )

    mockInsertOrder.mockRejectedValueOnce({
      message: AV_DELIVERY_ERROR_SUBSTRING.IS_NOT_LIST_FOR_SELECTED,
    })
    mockInsertOrder.mockResolvedValueOnce(expectedSuccessResponse)

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'order-number',
    })

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(mockInsertOrder).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'deliveryId',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )
  })

  it('payment handler calls Session Service with bolt-session cookie, user, email, firstName and orderId on payment success for not logged in users', async () => {
    sessionStub.setOrderAsBooked.resetHistory()
    sessionStub.setOrderInfoToSession.resetHistory()
    sessionStub.getEntireBasket.resetHistory()

    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.singleUsePromoCode = undefined
    bodyStub.email = '<EMAIL>'
    bodyStub.fname = 'TestName'

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-session': 'mock-bolt-session',
      },
      headers: requestHeaders,
    }

    const userSessionStub = {
      ids: [
        {
          id: 'customer-id',
          source: 'TEST_SOURCE_ID',
        },
      ],
    }
    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(sessionStub.setOrderInfoToSession.calledOnce).toBeTruthy()

    expect(
      sessionStub.setOrderInfoToSession.calledWithExactly(
        'mock-bolt-session',
        userSessionStub,
        '<EMAIL>',
        'TestName',
        brand
      )
    ).toBeTruthy()

    expect(sessionStub.setOrderAsBooked.calledOnce).toBeTruthy()

    expect(
      sessionStub.setOrderAsBooked.calledWithExactly(
        'mock-bolt-session',
        'order-id',
        brand
      )
    ).toBeTruthy()

    expect(sessionStub.getEntireBasket.calledOnce).toBeTruthy()
    expect(
      sessionStub.getEntireBasket.calledWithExactly('mock-bolt-session', brand)
    ).toBeTruthy()
  })

  it('payment handler calls Session Service with bolt-session header, user, email, firstName and orderId on payment success for not logged in users', async () => {
    sessionStub.setOrderAsBooked.resetHistory()
    sessionStub.setOrderInfoToSession.resetHistory()
    sessionStub.getEntireBasket.resetHistory()

    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.singleUsePromoCode = undefined
    bodyStub.email = '<EMAIL>'
    bodyStub.fname = 'TestName'

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-session': 'mock-bolt-session',
      },
      headers: requestHeaders,
    }

    const userSessionStub = {
      ids: [
        {
          id: 'customer-id',
          source: 'TEST_SOURCE_ID',
        },
      ],
    }
    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(sessionStub.setOrderInfoToSession.calledOnce).toBeTruthy()

    expect(
      sessionStub.setOrderInfoToSession.calledWithExactly(
        'mock-bolt-session',
        userSessionStub,
        '<EMAIL>',
        'TestName',
        brand
      )
    ).toBeTruthy()

    expect(sessionStub.setOrderAsBooked.calledOnce).toBeTruthy()

    expect(
      sessionStub.setOrderAsBooked.calledWithExactly(
        'mock-bolt-session',
        'order-id',
        brand
      )
    ).toBeTruthy()

    expect(sessionStub.getEntireBasket.calledOnce).toBeTruthy()
    expect(
      sessionStub.getEntireBasket.calledWithExactly('mock-bolt-session', brand)
    ).toBeTruthy()
  })

  it('payment handler logs "payment completed" event on success and setsOrderAsBooked to true for logged in user', async () => {
    sessionStub.setOrderAsBooked.resetHistory()
    sessionStub.getEntireBasket.resetHistory()

    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.email = '<EMAIL>'

    const requestCookies = createCookiesStub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        warn: () => {},
        error: () => {},
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { preSaleEnabled: true },
          },
        ],
      })

    customerDataResponseStub.email = '<EMAIL>'
    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(sessionStub.setOrderAsBooked.calledOnce).toBeTruthy()

    expect(getBenefitConfigsStub.calledOnce).toBeTruthy()

    expect(sessionStub.getEntireBasket.calledOnce).toBeTruthy()
  })

  it('payment handler successfully validates the customer details before payment for a valid customer with membership benefits', async () => {
    const avStub = createAvClientStub()

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({})
    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        warn: () => {},
        error: () => {},
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()
    jwtVerifyStub.resetHistory()

    await handler(requestStub)

    expect(
      axiosStub.get.calledWithExactly(
        `https://${CUSTOMER_ACCOUNTS_SERVICE_SUBDOMAIN}.${brand}/customer-data`,
        {
          headers: {
            cookie: `user-data=${requestCookies['user-data']}; user-sig=${requestCookies['user-sig']}; bolt-session=${requestCookies['bolt-session']};`,
          },
        }
      )
    ).toBeTruthy()
    expect(jwtVerifyStub.calledOnce).toBeTruthy()

    expect(
      jwtVerifyStub.calledWithExactly('test-data.test-sig', 'jwt-secret')
    ).toBeTruthy()

    expect(
      logInfoStub.calledWithExactly(
        'Customer-data has been successfully checked'
      )
    ).toBeTruthy()
  })

  it("payment handler logs and throws the correct error on failing account-service's user validation", async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: {
        ...requestCookies,
        'bolt-session': 'mock-bolt-session',
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    bodyStub.email = '<EMAIL>'

    const errorObject = {
      response: { status: 401, data: { code: 'INVALID' } },
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().rejects(errorObject)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvAtgError('Invalid user', errorObject.response.status)
    )

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )

    expect(
      logErrorStub.calledWithExactly(
        `Error validating user data. The user with email: ${userPayload.email} is ${errorObject.response.data.code}`
      )
    ).toBeTruthy()
  })

  it('payment handler throws an error if SUPC equals test_expireslater', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const userStub = createUserStub()

    const bodyStub = createBodyStub()
    bodyStub.singleUsePromoCode = 'test_expireslater'

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-session': 'mock-bolt-session',
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvAtgError('Invalid singleUsePromoCode', 410)
    )

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('payment handler supports delivery method by name', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()

    const bodyStub = createBodyStub()
    bodyStub.delivery = 'post'
    bodyStub.paymentMethod = 'creditCard'

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )
  })

  it('payment handler processes the order for payment via creditCard - not signed in user', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [],
        bestAvailable: [],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()

    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via creditCard - for mixed admissions tickets', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = [
      {
        number: 1,
      },
    ]
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [
          {
            number: 1,
          },
        ],
        bestAvailable: [],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via creditCard for general admissions ticket', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {}
    userStub.generalAdmissions = [
      {
        number: 1,
      },
    ]
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {},
        generalAdmissions: [
          {
            number: 1,
          },
        ],
        bestAvailable: [],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment with non existing profilingId', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {}
    userStub.generalAdmissions = [
      {
        number: 1,
      },
    ]
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    mockGetProfilingIdFromToken.mockReturnValueOnce(null)

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {},
        generalAdmissions: [
          {
            number: 1,
          },
        ],
        bestAvailable: [],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        null
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via creditCard for best available ticket', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {}
    userStub.generalAdmissions = []
    userStub.bestAvailable = [
      {
        number: 1,
      },
    ]
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {},
        generalAdmissions: [],
        bestAvailable: [
          {
            number: 1,
          },
        ],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via creditCard - signed in user', async () => {
    jwtVerifyStub.resetHistory()

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({})

    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const requestCookies = createCookiesStub()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(jwtVerifyStub.calledOnce).toBeTruthy()

    expect(
      jwtVerifyStub.calledWithExactly('test-data.test-sig', 'jwt-secret')
    ).toBeTruthy()

    expect(
      avStub.addCustomerIdToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'CD720E07-88B4-4B27-9D99-938455E76A20'
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [],
        bestAvailable: [],
        ticketProtectionFee: 2.75,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()

    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via paypal', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()

    const insertOrderError = new AvPaymentTokenRequiredError({
      message: '3DS Required',
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      insertOrderError
    )
    const bodyStub = createBodyStub()

    bodyStub.paymentMethod = 'paypal'
    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-session': 'mock-bolt-session',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    avStub.refs.apiRequest.resolves({
      response: {
        data: {
          pa_request_URL: { standard: pa_request_URL },
          pa_request_information: {
            standard: pa_request_information,
          },
        },
      },
    })

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      redirectTo: pa_request_URL,
      orderId: userStub.orderId,
      paymentRequestParams: { p1: 'v1', p2: 'v2' },
    })

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        undefined,
        'paypal',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(
      avStub.fetchOrderPaymentPaRequestData.calledWithExactly(
        userStub.orderId,
        paymentDataStub[0].paymentId,
        avStub.refs.apiRequest
      )
    ).toBeTruthy()

    expect(avStub.refs.closeSession.notCalled).toBeTruthy()
  })

  it('payment handler handles Worldpay DDC redirect', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()

    const insertOrderError = new AvPaymentTokenRequiredError({
      message: '3DS Required',
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      insertOrderError
    )

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-session': 'mock-bolt-session',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    avStub.fetchOrderPaymentPaRequestData.resolves({
      pa_request_URL,
      pa_request_information: JSON.stringify(
        pa_request_information_Worldpay_DDC
      ),
    })

    const result = await handler(requestStub)

    expect(result).toStrictEqual({
      redirectTo: pa_request_URL,
      orderId: userStub.orderId,
      paymentRequestParams: { ...pa_request_information_Worldpay_DDC.body },
      ddcRequired: true,
    })

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'creditCard',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(
      avStub.fetchOrderPaymentPaRequestData.calledWithExactly(
        userStub.orderId,
        paymentDataStub[0].paymentId,
        avStub.refs.apiRequest
      )
    ).toBeTruthy()

    expect(avStub.refs.closeSession.notCalled).toBeTruthy()
  })

  it('payment handler returns bad gateway when order insertion throws', async () => {
    const avStub = createAvClientStub()

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      new AvError('something bad happend')
    )

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'paypal'
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-session': 'mock-bolt-session',
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvError('something bad happend')
    )

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [],
        ticketProtectionFee: 2.75,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        'paypal',
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    /*
     * expect(replyStub.badGateway.calledWithExactly()).toBeTruthy()
     * the HTTP status will depend on AV's exception number and will be 400
     * by default (bad request).
     * Most likely will be a  402 (payment required).
     */
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the zero amount order for payment without payment methods', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.ticketProtectionFee = 0

    const bodyStub = createBodyStub()

    bodyStub.cc = undefined
    bodyStub.orderTotal = '0'
    bodyStub.paymentMethod = []

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(avStub.addCustomerIdToOrder.notCalled).toBeTruthy()

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: 'performance-id',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: undefined,
        ticketProtectionFee: 0,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '0',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        undefined,
        '0',
        logStub,
        undefined,
        [],
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()

    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler logs "payment completed" event on success', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(mockSendOrderRequested).toHaveBeenCalled()
    expect(mockSendOrderSucceeded).toHaveBeenCalled()

    expect(
      logInfoStub.calledWithExactly(
        {
          orderTotal: '100.30',
          orderId: 'order-id',
          orderNumber: 'my-order-number',
          paymentTypes: ['paypal'],
          performanceId: 'performance-id',
          singleUsePromoCode: 'single-use-code-parameter',
        },
        'Payment completed'
      )
    ).toBeTruthy()
  })

  it('payment handler log performance and seats on AvPaymentAmountError error', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const userStub = createUserStub()

    const originalError = new AvPaymentAmountError({
      grandTotal: 10,
      orderTotal: 15,
      ticketProtection: 'ticketProtection',
      additionalItems: 'additionalItems',
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      originalError
    )

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.orderTotal = '25.65'
    userStub.performance = '********-AA7A-4D89-A476-75CAECFE8F96'
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {
        'bolt-device': 'mock-bolt-device',
        'bolt-session': 'mock-bolt-session',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
      ip: '127.0.0.1',
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvPaymentAmountError({
        grandTotal: originalError.expectedAmount,
        orderTotal: originalError.submittedAmount,
        ticketProtection: originalError.ticketProtection,
        additionalItems: originalError.additionalItems,
        performance: userStub.performance,
        seats: userStub.seats,
      })
    )

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '25.65',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: '********-AA7A-4D89-A476-75CAECFE8F96',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()

    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler logs "Session Expired" event when user session has expired', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const originalError = new AvBadSessionError(
      { message: 'Session Expired', stack: '' },
      'test-url'
    )

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({})

    avStub.addCustomerIdToOrder = sinon.stub().rejects(originalError)

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }

    const requestCookies = createCookiesStub()

    const bodyStub = createBodyStub()

    const logErrorStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      cookies: {
        ...requestCookies,
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
        'bolt-session': 'mock-bolt-session',
      },
      log: {
        info: () => {},
        error: logErrorStub,
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvBadSessionError(
        { message: 'Session Expired', stack: '' },
        'test-url'
      )
    )

    expect(logErrorStub.calledWithExactly(originalError)).toBeTruthy()

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('payment handler logs "invalid signature" on JsonWebTokenError', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    jwtVerifyStub.restore()
    const errorObject = new jwt.JsonWebTokenError({
      message: 'invalid signature',
      stack: '',
    })

    sinon.stub(jwt, 'verify').throws(errorObject)

    const userStub = createUserStub()

    const requestCookies = createCookiesStub()

    const bodyStub = createBodyStub()

    const logErrorStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      cookies: {
        ...requestCookies,
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
        'bolt-session': 'mock-bolt-session',
      },
      log: {
        info: () => {},
        error: logErrorStub,
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(errorObject)

    expect(logErrorStub.calledWithExactly(errorObject)).toBeTruthy()

    expect(avStub.addCustomerNumberToOrder.notCalled).toBeTruthy()

    expect(avStub.addPaymentsToOrder.notCalled).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).not.toHaveBeenCalled()

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('payment handler throws an error on mismatch of applied vs available user membership benefits', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: 'nonexistentAppliedMembershipBenefitId',
      },
    }

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { transactionFeeOverride: 0 },
          },
        ],
      })

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: {
        ...requestCookies,
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
        'bolt-session': 'mock-bolt-session',
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    jwtVerifyStub.restore()
    sinon.stub(jwt, 'verify').returns(userPayload)

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvAtgError('Invalid membership benefits', 400)
    )

    expect(logErrorStub.calledOnce).toBeTruthy()

    expect(
      logErrorStub.calledWithExactly(
        `Membership benefits mismatch. Benefit with id: nonexistentAppliedMembershipBenefitId which is applied is not part of the user's membership benefits.`
      )
    ).toBeTruthy()

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('Logged in plus member checks benefit amenities and removes unwanted transactionfee', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.performance = 'performance-id'

    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.ticketProtection = true

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()
    const logWarnStub = sinon.stub()

    massagePerformanceSpy = sinon.spy(lifecycle, 'massagePerformance')

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { transactionFeeOverride: 0 },
          },
        ],
      })

    const requestStub = {
      avClient: avStub,
      dbClient: {
        fetchPerformanceById: sinon.stub().resolves({
          venue_data7: 'venue',
        }),
      },
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: logWarnStub,
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    jwtVerifyStub.restore()
    sinon.stub(jwt, 'verify').returns(userPayload)

    await handler(requestStub)

    expect(getBenefitConfigsStub.calledOnce).toBeTruthy()

    expect(
      massagePerformanceSpy.returnValues[0].benefitsAvailable
    ).toStrictEqual({ access: true, membership: true })

    expect(
      logWarnStub.calledWithExactly(
        {
          orderId: 'order-id',
          serviceChargeId: 'F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E',
          benefit: {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { transactionFeeOverride: 0 },
          },
          performance: 'performance-id',
          seats: {
            'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
              priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
              promoCodeId: '',
              benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
            },
          },
          upsells: 'additionalItems-array',
          ticketProtection: true,
          orderTotal: '100.30',
        },
        'Transaction fee removed from basket in av'
      )
    ).toBeTruthy()

    expect(
      avStub.removeServiceCharge.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'F0FA6D70-5278-4308-87E9-3DB5FF1A3C8E'
      )
    ).toBeTruthy()
  })

  it('Logged in member checks benefit amenities, Transaction Fees do not exist so none are removed', async () => {
    const avStub = createAvClientStub()
    avStub.addCustomerIdToOrder = sinon
      .stub()
      .resolves(ticketProtectionServiceCharge)

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { transactionFeeOverride: 0 },
          },
        ],
      })

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    jwtVerifyStub.restore()
    sinon.stub(jwt, 'verify').returns(userPayload)

    await handler(requestStub)

    expect(getBenefitConfigsStub.calledOnce).toBeTruthy()
    expect(avStub.removeServiceCharge.notCalled).toBeTruthy()
  })

  it('Logged in member checks benefit amenities, amenities does not include transaction fee override', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: { preSaleEnabled: true },
          },
        ],
      })

    sinon.stub(lifecycle, 'massagePerformance').returns({
      benefitsAvailable: {
        access: false,
        membership: false,
      },
    })

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    jwtVerifyStub.restore()
    sinon.stub(jwt, 'verify').returns(userPayload)

    await handler(requestStub)

    expect(getBenefitConfigsStub.calledOnce).toBeTruthy()
    expect(avStub.removeServiceCharge.notCalled).toBeTruthy()
  })

  it('Logged in member checks benefit amenities, amenities should include transaction fee (3rd party venue)', async () => {
    const avStub = createAvClientStub()

    const userStub = {
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: { venue_data7: 'Non-ATG (Online Ticketed)' },
    }

    userStub.seats = {}

    const bodyStub = createBodyStub()

    const requestCookies = createCookiesStub()

    const tfServiceChargeStub = sinon.stub(
      utils,
      'getTransactionFeeServiceCharge'
    )

    getBenefitConfigsStub = sinon
      .stub(benefitConfigService, 'getBenefitConfigs')
      .resolves({
        hiddenPromoCodeIds: ['hiddenPromoCode'],
        benefitConfigs: [
          {
            benefitId: 'benefitId',
            hiddenPromoCodeIds: ['hiddenPromoCode'],
            benefitAmenities: {
              preSaleEnabled: true,
              transactionFeeOverride: 0,
            },
          },
        ],
      })

    massagePerformanceSpy = sinon.spy(lifecycle, 'massagePerformance')

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: () => {},
        warn: () => {},
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves(customerDataResponseStub)
    axiosStub.get.onThirdCall().resolves()

    await handler(requestStub)

    expect(
      massagePerformanceSpy.returnValues[0].benefitsAvailable
    ).toStrictEqual({ access: false, membership: false })
    expect(tfServiceChargeStub.notCalled).toBeTruthy()
  })

  it('should not add uplift charge to order if order has not been uplifted for reserved', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {
        'seat-id-1': {
          hasHoldOverride: false,
          holdId: '',
        },
      },
    })
    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addSeatUpliftChargesToOrderStub.callCount).toBe(0)
  })

  it('should not add uplift charge to order if order has not been uplifted for mixed admission', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {
        'seat-id-1': {
          hasHoldOverride: false,
          holdId: '',
        },
      },
      generalAdmissions: [
        {
          sectionName: 'Studio',
          number: 2,
          ticketPrice: '48.50',
          promoCodeId: '',
          benefitId: '',
          priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          priceTypeId: 'EC290A80-54B4-4635-932C-1769C20123FE',
        },
      ],
    })
    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addSeatUpliftChargesToOrderStub.callCount).toBe(0)
  })

  it('add uplift charge to order if order has been uplifted for reserved', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {
        'seat-id-1': {
          hasHoldOverride: true,
          holdId: 'hold-1',
        },
      },
    })
    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.holdCalculationId = 'Calculation::1234'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addSeatUpliftChargesToOrderStub.callCount).toBe(1)
  })

  it('add uplift charge to order if order has been uplifted for mixed admission', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {
        'seat-id-1': {
          hasHoldOverride: true,
          holdId: 'hold-1',
        },
      },
      generalAdmissions: [
        {
          sectionName: 'Studio',
          number: 2,
          ticketPrice: '48.50',
          promoCodeId: '',
          benefitId: '',
          priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          priceTypeId: 'EC290A80-54B4-4635-932C-1769C20123FE',
          holdOverrideId: 'hold-2',
        },
      ],
    })
    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.holdCalculationId = 'Calculation::1234'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addSeatUpliftChargesToOrderStub.callCount).toBe(1)
  })

  it('add uplift charge to order if order has been uplifted for general admission', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {},
      generalAdmissions: [
        {
          sectionName: 'Studio',
          number: 2,
          ticketPrice: '48.50',
          promoCodeId: '',
          benefitId: '',
          priceZone: 'ADBE7964-E8B9-422B-9C03-8AABEBE67194',
          priceTypeId: 'EC290A80-54B4-4635-932C-1769C20123FE',
          holdOverrideId: 'hold-1',
        },
      ],
    })
    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.holdCalculationId = 'Calculation::1234'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addSeatUpliftChargesToOrderStub.callCount).toBe(1)
  })

  it('should not add uplift charge to order if order has not been uplifted for best available', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      bestAvailable: [
        {
          priceZone: 'mockPriceValueId',
          priceTypeId: 'mockPriceTypeId',
        },
      ],
    })

    addBestAvailableUpliftedChargeToOrderStub = sinon
      .stub(upliftUtils, 'addBestAvailableUpliftedChargeToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addBestAvailableUpliftedChargeToOrderStub.callCount).toBe(0)
  })

  it('add uplift charge to order if order has been uplifted for best available', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      bestAvailable: [
        {
          priceZone: 'mockPriceValueId',
          priceTypeId: 'mockPriceTypeId',
          holdOverrideId: 'mockHoldId',
        },
      ],
    })

    addBestAvailableUpliftedChargeToOrderStub = sinon
      .stub(upliftUtils, 'addBestAvailableUpliftedChargeToOrder')
      .resolves(undefined)

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'
    bodyStub.holdCalculationId = 'Calculation::1234'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'my-order-number',
    })

    expect(addBestAvailableUpliftedChargeToOrderStub.callCount).toBe(1)
  })

  it('payment handler returns a 400 if unable to add charges to order', async () => {
    const createUserStub = () => ({
      session: 'mock-bolt-session',
      cookie: 'cooookie',
      orderId: 'order-id',
      performance: 'performance-id',
      seats: {
        'seat-id-1': {
          hasHoldOverride: true,
          holdId: 'hold-1',
        },
      },
    })

    addSeatUpliftChargesToOrderStub = sinon
      .stub(upliftUtils, 'addSeatUpliftChargesToOrder')
      .rejects('Error')

    const avStub = createAvClientStub()

    const userStub = createUserStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.paymentMethod = 'creditCard'

    const logInfoStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: logInfoStub,
        error: () => {},
        warn: () => {},
      },
      cookies: {},
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get').resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(
      new AvAtgError(
        '[Uplift payment] Error adding charges to order Error',
        400
      )
    )
  })

  it('payment handler proceeds in case of a 500 error with no benefits', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }

    const bodyStub = createBodyStub()
    bodyStub.delivery = '0D395F3A-A74A-4AFA-95CF-EAB75DAD35FB'
    bodyStub.paymentMethod = 'creditCard'

    const requestCookies = createCookiesStub()

    const logErrorStub = sinon.stub()
    const logWarnStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        warn: logWarnStub,
        error: logErrorStub,
      },
      cookies: requestCookies,
      headers: requestHeaders,
    }

    const errorObject = {
      response: { status: 500, data: { code: 'Internal Server Error' } },
    }
    jwtDecodeStub.resetHistory()

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')
    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().rejects(errorObject)
    axiosStub.get.onThirdCall().resolves()

    await handler(requestStub)

    expect(jwtDecodeStub.calledOnce).toBeTruthy()
    expect(jwtDecodeStub.calledWithExactly('test-data.test-sig')).toBeTruthy()

    expect(jwtVerifyStub.calledTwice).toBeTruthy()
    expect(
      jwtVerifyStub.calledWithExactly('test-data.test-sig', 'jwt-secret')
    ).toBeTruthy()

    expect(avStub.addCustomerIdToOrder.calledOnce).toBeTruthy()
    expect(logErrorStub.called).toBeFalsy()
  })

  it('payment handler send OrderRequested event', async () => {
    const requestStub = createEventBridgeRequestStub()

    await handler(requestStub)

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '100.30',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('Log avNoDeliveryMethods Error', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.orderTotal = '25.65'
    bodyStub.delivery = 'post'
    bodyStub.ticketProtection = true
    bodyStub.additionalItems = 'additionalItems-array'

    const userStub = createUserStub()

    userStub.performance = 'performance-id'
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }

    const avError = new AvExceptionError({
      message:
        '[30F85C7A-D114-4F86-97D8-35898B292C29 is not in list for Selected Delivery Method.] : Please check again.',
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(avError)

    const logErrorStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: {
        'bolt-session': 'mock-bolt-session',
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(avError)

    expect(
      logErrorStub.calledWithExactly({
        name: 'AvNoDeliveryMethodError',
        status: 422,
        message: 'No available delivery methods',
        deliveryMethod: 'post',
        performanceId: 'performance-id',
        upsells: 'additionalItems-array',
        ticketProtection: true,
        customerNumber: 'customer-number',
        orderTotal: '25.65',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
            priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
            promoCodeId: '',
            benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
          },
        },
      })
    ).toBeTruthy()

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '25.65',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('Log AvPaymentAvailableMethodsError Error', async () => {
    const avStub = createAvClientStub()

    clientEventBridgeStub.send.resetHistory()

    const bodyStub = createBodyStub()
    bodyStub.orderTotal = '25.65'
    bodyStub.ticketProtection = true
    bodyStub.additionalItems = 'additionalItems-array'

    const userStub = createUserStub()

    userStub.performance = 'performance-id'
    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
        priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        promoCodeId: '',
        benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
      },
    }

    const avError = new AvExceptionError({
      message:
        '[E0384EDB-72C7-412E-B32D-338B88D996FF is not in list for Payment Method.] : Please check again.',
    })

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(avError)

    const logErrorStub = sinon.stub()

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: {
        info: () => {},
        error: logErrorStub,
        warn: () => {},
      },
      cookies: {
        'bolt-session': 'mock-bolt-session',
        'bolt-device': 'mock-bolt-device',
        'bolt-account': 'mock-bolt-account',
      },
      headers: requestHeaders,
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()

    const promise = handler(requestStub)

    await expect(promise).rejects.toStrictEqual(avError)

    expect(
      logErrorStub.calledWithExactly({
        name: 'AvPaymentAvailableMethodsError',
        statusCode: 500,
        message: `No available payment methods`,
        performanceId: 'performance-id',
        upsells: 'additionalItems-array',
        ticketProtection: true,
        customerNumber: 'customer-number',
        orderTotal: '25.65',
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2': {
            priceTypeId: 'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
            promoCodeId: '',
            benefitId: '3F54EF6A-96D0-448B-BEEF-653F0FDB3D15',
          },
        },
      })
    ).toBeTruthy()

    expect(mockSendOrderRequested).toHaveBeenCalledWith(
      {
        orderTotal: '25.65',
        referrerSource: 'referrer-source-parameter',
        paymentMethods: undefined,
        performanceId: 'performance-id',
        sessionId: 'mock-bolt-session',
        deviceId: 'mock-bolt-device',
        accountId: 'mock-bolt-account',
      },
      requestStub.log
    )
  })

  it('sets the set-cookie header when returned from customerData call', async () => {
    const avStub = createAvClientStub()

    const userStub = createUserStub()

    const bodyStub = createBodyStub()

    const requestCookies = createCookiesStub()

    const userStubWithSeats = {
      ...userStub,
      seats: {
        'seat-id-1': {
          priceTypeId: 'price-type-id',
          promoCodeId: 'promo-code-id',
        },
      },
    }

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStubWithSeats,
      body: bodyStub,
      log: logStub,
      cookies: requestCookies,
      headers: requestHeaders,
    }

    const replyStub = {
      header: sinon.stub(),
    }

    axiosStub.get.restore()
    axiosStub.get = sinon.stub(axios, 'get')

    axiosStub.get.onFirstCall().resolves()
    axiosStub.get.onSecondCall().resolves({
      headers: {
        'set-cookie': ['cookie1=cookie1; cookie2=cookie2'],
      },
    })
    axiosStub.get.onThirdCall().resolves()

    axiosStub.post.resolves({
      data: {
        data: {
          benefitConfig: false,
        },
      },
    })

    await handler(requestStub, replyStub)

    expect(
      replyStub.header.calledWith('set-cookie', [
        'cookie1=cookie1; cookie2=cookie2',
      ])
    ).toBeTruthy()
  })
})
