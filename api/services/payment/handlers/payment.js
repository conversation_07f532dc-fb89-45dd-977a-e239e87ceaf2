const axios = require('axios')
const jwt = require('jsonwebtoken')
const {
  generateTraceContextHeaders,
} = require('@atg-digital/server-logger-library')

const {
  AvPaymentTokenRequiredError,
  AvAtgError,
  AvPaymentAmountError,
  AvBadSessionError,
  AvExceptionError,
} = require('../../../lib/av-client/exceptions')
const {
  getTransactionFeeServiceCharge,
} = require('../../../lib/av-client/utils')
const {
  sendOrderRequested,
  sendOrderSucceeded,
  sendOrderFailed,
} = require('../../../lib/event-bridge-client/utils')
const {
  processPaymentRedirectData,
} = require('../../../lib/av-client/v2-av-client/payment')
const benefitConfigService = require('../../../lib/benefits-config-service/benefits')
const sessionService = require('../../../lib/session-service')
const {
  v2GetCustomerNumber,
} = require('../../../lib/av-client/v2-av-client/customer')
const upliftUtils = require('../utils/uplift')
const {
  hasPerformanceBeenUplifted: hasPerformanceSeatsBeenUplifted,
} = require('../../reserve/utils/uplift')
const { getPerformanceDataById } = require('../utils/get-performance-data')
const {
  insertOrderWithDeferredDeliveryRetry,
  getProfilingIdFromToken,
} = require('../utils')
const {
  getCustomerAccountsServiceEndpoint,
  getBrandSpecificSecret,
} = require('../../../lib/brand-utils')

const { SINGLE_USE_SERVICE_ENDPOINT, SOURCE_ID } = process.env

async function paymentHandler(request, reply) {
  const { avClient, log, user, cookies, dbClient, headers, body } = request
  const {
    session,
    cookie,
    orderId,
    performance,
    seats,
    generalAdmissions,
    bestAvailable,
    ticketProtectionFee,
  } = user
  const boltDeviceId = cookies['bolt-device']
  const boltSessionId = cookies['bolt-session']
  const userData = cookies['user-data']
  const userSignature = cookies['user-sig']
  const brand = headers['bolt-brand']
  const ip = headers['cf-connecting-ip']

  const {
    cc,
    delivery: deliveryMethod,
    deliveryType,
    paymentMethod,
    callbackURL,
    orderTotal,
    ticketProtection,
    answers,
    optOut,
    additionalItems,
    paymentMethods,
    referrerSource,
    singleUsePromoCode,
    showConfig,
    email,
    fname,
    holdCalculationId,
  } = body

  sendOrderRequested(
    {
      orderTotal,
      referrerSource,
      paymentMethods,
      performanceId: performance,
      sessionId: boltSessionId,
      deviceId: boltDeviceId,
      accountId: cookies['bolt-account'],
    },
    log
  )

  const { apiRequest, closeSession } = avClient.restoreSession({
    session,
    cookie,
    log,
    isV2: true,
  })

  let payments,
    serviceChargeDetails,
    availablePaymentMethods,
    response,
    avIdStr,
    customerNumber

  try {
    if (singleUsePromoCode) {
      if (singleUsePromoCode === 'test_expireslater') {
        sendOrderFailed(
          {
            failureReason: 'SingleUsePromoCodeError',
            orderTotal,
            performanceId: performance,
            sessionId: boltSessionId,
            deviceId: boltDeviceId,
            accountId: cookies['bolt-account'],
          },
          log
        )
        throw new AvAtgError('Invalid singleUsePromoCode', 410)
      } else {
        await axios
          .get(`${SINGLE_USE_SERVICE_ENDPOINT}check/${singleUsePromoCode}`, {
            headers: generateTraceContextHeaders(),
          })
          .catch((err) => {
            const { response } = err
            throw new AvAtgError('Invalid singleUsePromoCode', response.status)
          })
        log.info(`${singleUsePromoCode} has been successfully checked`)
      }
    }

    const datapumpPerformanceData = await getPerformanceDataById(
      performance,
      dbClient
    )
    let hasTransactionFeeOverride = false
    let benefitData = {}

    if (userData && userSignature) {
      const CUSTOMER_ACCOUNTS_SERVICE_URL =
        getCustomerAccountsServiceEndpoint(brand)

      const customerDataEndpoint = `${CUSTOMER_ACCOUNTS_SERVICE_URL}/customer-data`

      const headers = {
        ...generateTraceContextHeaders(),
        cookie: `user-data=${userData}; user-sig=${userSignature}; bolt-session=${boltSessionId};`,
      }

      log.info({ headers, customerDataEndpoint }, 'Customer data request')

      const customerDataResponse = await axios
        .get(customerDataEndpoint, {
          headers,
        })
        .catch((err) => {
          const { response } = err
          const statusCode = String(response.status)
          const userToken = `${userData}.${userSignature}`

          try {
            jwt.verify(
              userToken,
              getBrandSpecificSecret('customerAccount', brand)
            )
          } catch (error) {
            return log.error(`Invalid token: ${err}`)
          }

          // 4xx range
          if (statusCode[0] === '4') {
            log.error(
              `Error validating user data. The user with email: ${email} is ${response.data.code}`
            )
            sendOrderFailed(
              {
                failureReason: 'InvalidUserError',
                orderTotal,
                performanceId: performance,
                sessionId: boltSessionId,
                deviceId: boltDeviceId,
                accountId: cookies['bolt-account'],
              },
              log
            )
            throw new AvAtgError('Invalid user', response.status)
          }
        })

      if (customerDataResponse) {
        const { headers } = customerDataResponse

        if (headers['set-cookie']) {
          reply.header('set-cookie', headers['set-cookie'])
        }

        const { avId, benefits } = jwt.verify(
          headers['atg-user-token'],
          getBrandSpecificSecret('customerAccount', brand)
        )
        avIdStr = avId
        const membershipBenefitIds = new Set()

        if (benefits?.length > 0) {
          benefits.forEach((benefitObj) =>
            membershipBenefitIds.add(benefitObj.id)
          )

          benefitData = await benefitConfigService.getBenefitConfigs(
            benefits,
            benefitConfigService.fetchBenefitInfo
          )
        }

        hasTransactionFeeOverride =
          benefitData?.benefitConfigs?.some(
            (amenity) => amenity.benefitAmenities.transactionFeeOverride === 0
          ) &&
          datapumpPerformanceData.benefitsAvailable.access &&
          datapumpPerformanceData.benefitsAvailable.membership

        const appliedBenefitIds = Object.keys(seats).reduce((acc, seatId) => {
          const benefitId = seats[seatId].benefitId || null
          if (benefitId) {
            acc.push(benefitId)
          }

          return acc
        }, [])

        for (const id of appliedBenefitIds) {
          if (!membershipBenefitIds.has(id)) {
            log.error(
              `Membership benefits mismatch. Benefit with id: ${id} which is applied is not part of the user's membership benefits.`
            )
            sendOrderFailed(
              {
                failureReason: 'MembershipBenefitsMismatchError',
                orderTotal,
                performanceId: performance,
                sessionId: boltSessionId,
                deviceId: boltDeviceId,
                accountId: cookies['bolt-account'],
              },
              log
            )
            throw new AvAtgError('Invalid membership benefits', 400)
          }
        }

        log.info('Customer-data has been successfully checked')
      } else {
        log.warn(
          `Customer-data couldn't be checked, proceed with checkout without benefits`
        )

        const { avId } = jwt.decode(`${userData}.${userSignature}`)
        avIdStr = avId
      }
      response = await avClient.addCustomerIdToOrder(
        apiRequest,
        orderId,
        avIdStr
      )
      serviceChargeDetails = response.serviceChargeDetails
      availablePaymentMethods = response.availablePaymentMethods
    } else {
      const responseInsert = await avClient.v2InsertCustomer(
        apiRequest,
        body,
        true
      )
      customerNumber = responseInsert.customerNumber
      const response = await avClient.addCustomerNumberToOrder(
        apiRequest,
        orderId,
        customerNumber
      )
      // Assign the newly created customerId to the avIdStr usually used for the loggedIn customerId
      avIdStr = responseInsert.customerId
      const userSession = {
        ids: [{ id: responseInsert.customerId, source: SOURCE_ID }],
      }

      await sessionService.setOrderInfoToSession(
        boltSessionId,
        userSession,
        email,
        fname,
        brand
      )

      serviceChargeDetails = response.serviceChargeDetails
      availablePaymentMethods = response.availablePaymentMethods
    }

    if (ticketProtection) {
      await avClient.addDynamicTicketProtection(
        apiRequest,
        orderId,
        seats,
        generalAdmissions,
        ticketProtectionFee,
        bestAvailable
      )
    }

    if (additionalItems.length > 0) {
      await avClient.addAdditionalItemsToOrder(
        apiRequest,
        orderId,
        additionalItems
      )
    }

    if (hasTransactionFeeOverride) {
      const transactionFeeServiceCharge =
        getTransactionFeeServiceCharge(serviceChargeDetails)

      const serviceChargeId =
        transactionFeeServiceCharge?.service_charge_id?.standard

      if (serviceChargeId) {
        await avClient.removeServiceCharge(apiRequest, orderId, serviceChargeId)

        try {
          if (!customerNumber) {
            customerNumber = await v2GetCustomerNumber(apiRequest, avIdStr)
          }
        } catch (err) {
          log.warn(err)
        }

        log.warn(
          {
            orderId,
            serviceChargeId,
            benefit: benefitData?.benefitConfigs?.find(
              (amenity) => amenity.benefitAmenities.transactionFeeOverride === 0
            ),
            performance,
            ...(seats && { seats }),
            ...(additionalItems && { upsells: additionalItems }),
            ticketProtection,
            ...(customerNumber && { customerNumber }),
            orderTotal,
          },
          'Transaction fee removed from basket in av'
        )
      }
    }

    const isBestAvailableUpliftedPerformance = bestAvailable?.find((item) =>
      Boolean(item.holdOverrideId)
    )

    if (isBestAvailableUpliftedPerformance) {
      try {
        log.info(
          `[Uplift Payment] Attempting to add uplift charges to order for best available`
        )

        await upliftUtils.addBestAvailableUpliftedChargeToOrder({
          apiRequest,
          orderId,
          bestAvailable,
          performance,
          serviceChargeDetails,
          calculationId: holdCalculationId,
        })

        log.info(
          `[Uplift Payment] Uplift charges added to order for best available`
        )
      } catch (error) {
        // If we try and continue gracefully, we run the risk of wrongly charging the user
        throw new AvAtgError(
          `[Uplift payment] Error adding charges to order for best available. ${error}`,
          400
        )
      }
    }

    const hasUpliftedGenAdmissions = generalAdmissions?.find((item) =>
      Boolean(item.holdOverrideId)
    )
    const hasUpliftedSeats = hasPerformanceSeatsBeenUplifted(seats)

    if (hasUpliftedSeats || hasUpliftedGenAdmissions) {
      try {
        log.info(`[Uplift Payment] Attempting to add uplift charges to order`)

        await upliftUtils.addSeatUpliftChargesToOrder({
          apiRequest,
          orderId,
          seats,
          performance,
          generalAdmissions,
          serviceChargeDetails,
          calculationId: holdCalculationId,
        })

        log.info(`[Uplift Payment] Uplift charges added to order`)
      } catch (error) {
        // If we try and continue gracefully, we run the risk of wrongly charging the user
        throw new AvAtgError(
          `[Uplift payment] Error adding charges to order ${error}`,
          400
        )
      }
    }

    const profilingId = await getProfilingIdFromToken(headers['authorization'])
    const userPaymentMethods = paymentMethod || paymentMethods

    const sanitiziedReqDetails = {
      userDetails: {
        orderId,
        performance,
        seats,
        generalAdmissions,
        ticketProtectionFee,
        bestAvailable,
      },
      delivery: deliveryMethod,
      callbackURL,
      orderTotal,
      ticketProtection,
      answers,
      optOut,
      additionalItems,
      referrerSource,
      singleUsePromoCode,
      showConfig,
    }
    payments = await avClient.addPaymentsToOrder(
      apiRequest,
      orderId,
      callbackURL,
      cc,
      orderTotal,
      log,
      ticketProtection,
      userPaymentMethods,
      additionalItems,
      availablePaymentMethods,
      sanitiziedReqDetails,
      profilingId
    )

    // This call can fail and throw AvPaymentTokenRequiredError
    // if authentication is required e.g on paypal or 3D Secure.
    // It will then call the payment-callback endpoint
    // so any changes to code beneath here will probably need to be mirrored in payment-callback.js

    const {
      orderNumber,
      deliveryMethodDetails,
      admissions,
      performanceDetails,
      payments: orderPayments,
      tickets,
      priceTypes,
      miscItemDetails,
      orderItems,
    } = await insertOrderWithDeferredDeliveryRetry(
      apiRequest,
      orderId,
      deliveryMethod,
      deliveryType,
      answers,
      referrerSource,
      singleUsePromoCode,
      showConfig,
      optOut,
      ip
    )

    log.info(
      {
        orderTotal,
        orderId,
        orderNumber,
        singleUsePromoCode,
        paymentTypes: payments.map((payment) => payment.type),
        performanceId: performance,
      },
      'Payment completed'
    )

    const basket = await sessionService.getEntireBasket(boltSessionId, brand)

    sendOrderSucceeded({
      orderTotal,
      orderNumber,
      performanceId: performance,
      sessionId: boltSessionId,
      deviceId: boltDeviceId,
      accountId: avIdStr,
      basket,
      orderId,
      creationDate: new Date().toISOString(),
      payments,
      cc,
      userPaymentMethods,
      availablePaymentMethods,
      deliveryId: deliveryMethod,
      deliveryMethodDetails,
      admissions,
      performanceVenueType: datapumpPerformanceData.venueType,
      performanceDetails,
      orderPayments,
      benefitConfigs: benefitData.benefitConfigs,
      tickets,
      priceTypes,
      miscItemDetails,
      serviceChargeDetails,
      orderItems,
    }).catch((err) => log.error(err))

    const setCookie = await sessionService.setOrderAsBooked(
      boltSessionId,
      userData && userSignature ? undefined : orderId,
      brand
    )

    if (setCookie) {
      reply.header('set-cookie', setCookie)
    }

    // Burn single use code for payment completed
    if (singleUsePromoCode) {
      await axios
        .get(`${SINGLE_USE_SERVICE_ENDPOINT}burn/${singleUsePromoCode}`, {
          headers: generateTraceContextHeaders(),
        })
        .catch(() => {
          log.error(
            `${singleUsePromoCode} failed on burn request for order: ${orderId}`
          )
          throw new AvAtgError('Invalid singleUsePromoCode', 400)
        })
      log.info(
        `${singleUsePromoCode} has been successfully burned for order: ${orderId}`
      )
    }

    closeSession().catch(() => {})
    return { orderNumber }
  } catch (err) {
    if (err instanceof AvPaymentTokenRequiredError) {
      const authPayment = payments.find(
        (payment) => payment.type === 'creditCard' || payment.type === 'paypal'
      )

      const { pa_request_URL, pa_request_information } =
        await avClient.fetchOrderPaymentPaRequestData(
          orderId,
          authPayment.paymentId,
          apiRequest
        )

      const response = await processPaymentRedirectData(
        pa_request_URL,
        pa_request_information,
        orderId,
        closeSession
      )

      return response
    } else if (err instanceof AvPaymentAmountError) {
      await closeSession().catch(() => {})
      sendOrderFailed(
        {
          failureReason: 'AvPaymentAmountError',
          orderTotal,
          performanceId: performance,
          sessionId: boltSessionId,
          deviceId: boltDeviceId,
          accountId: cookies['bolt-account'],
        },
        log
      )
      throw new AvPaymentAmountError({
        grandTotal: err.expectedAmount,
        orderTotal: err.submittedAmount,
        ticketProtection: err.ticketProtection,
        additionalItems: err.additionalItems,
        performance,
        seats,
        sanitiziedReqDetails: err.sanitiziedReqDetails || {},
      })
    } else {
      if (
        err instanceof jwt.JsonWebTokenError ||
        err instanceof AvBadSessionError
      ) {
        log.error(err)
        sendOrderFailed(
          {
            failureReason: 'AvBadSessionError',
            orderTotal,
            performanceId: performance,
            sessionId: boltSessionId,
            deviceId: boltDeviceId,
            accountId: cookies['bolt-account'],
          },
          log
        )
      } else if (err instanceof AvExceptionError) {
        try {
          if (!customerNumber) {
            customerNumber = await v2GetCustomerNumber(apiRequest, avIdStr)
          }
        } catch (err) {
          log.warn(err)
        }
        if (
          typeof err.message === 'string' &&
          err.message.includes('is not in list for Selected Delivery Method')
        ) {
          const avNoDeliveryMethodError = {
            name: 'AvNoDeliveryMethodError',
            status: 422,
            message: `No available delivery methods`,
            deliveryMethod,
            performanceId: performance,
            ...(seats && { seats }),
            ...(additionalItems && { upsells: additionalItems }),
            ticketProtection,
            ...(customerNumber && { customerNumber }),
            orderTotal,
          }
          log.error(avNoDeliveryMethodError)
          sendOrderFailed(
            {
              failureReason: 'AvNoDeliveryMethodError',
              orderTotal,
              performanceId: performance,
              sessionId: boltSessionId,
              deviceId: boltDeviceId,
              accountId: cookies['bolt-account'],
            },
            log
          )
        } else if (
          typeof err.message === 'string' &&
          err.message.includes('is not in list for Payment Method')
        ) {
          const avPaymentAvailableMethodsError = {
            name: 'AvPaymentAvailableMethodsError',
            statusCode: 500,
            message: `No available payment methods`,
            performanceId: performance,
            ...(seats && { seats }),
            ...(additionalItems && { upsells: additionalItems }),
            ticketProtection,
            ...(customerNumber && { customerNumber }),
            orderTotal,
          }
          sendOrderFailed(
            {
              failureReason: 'AvPaymentAvailableMethodsError',
              orderTotal,
              performanceId: performance,
              sessionId: boltSessionId,
              deviceId: boltDeviceId,
              accountId: cookies['bolt-account'],
            },
            log
          )
          log.error(avPaymentAvailableMethodsError)
        } else {
          sendOrderFailed(
            {
              failureReason: err.name,
              orderTotal,
              performanceId: performance,
              sessionId: boltSessionId,
              deviceId: boltDeviceId,
              accountId: cookies['bolt-account'],
            },
            log
          )
          log.error(err)
        }
      }
      /*
       * err instanceof AvPaymentInvalidCardError: Logging out for now, but
       * when retry payment is implemented, * we shouldn't logout on this error
       */

      /* err instanceof AvValidationError: may happen when capping check
       * is failed, we can not let the user continue or retry.
       * So logging out.
       */

      // Catch the closeSession error and throw the original error
      await closeSession().catch(() => {})
      throw err
    }
  }
}

module.exports = paymentHandler
