'use strict'

const sinon = require('sinon')
const axios = require('axios')

const mockInsertOrderWithDeferredDeliveryRetry = jest.fn().mockResolvedValue({
  orderNumber: 'order-number',
  admissions: {},
  deliveryMethodDetails: {},
})
const mockGetProfilingIdFromToken = jest.fn().mockResolvedValue('profilingId')

jest.mock('../utils', () => ({
  ...jest.requireActual('../utils'),
  insertOrderWithDeferredDeliveryRetry:
    mockInsertOrderWithDeferredDeliveryRetry,
  getProfilingIdFromToken: mockGetProfilingIdFromToken,
}))

const {
  AvError,
  AvPaymentTokenRequiredError,
} = require('../../../lib/av-client/exceptions')
const sessionService = require('../../../lib/session-service')
const mockedAvAddCustomerToOrderResponse = require('../../../lib/test/fixtures/avAddCustomerResponse.json')
const {
  parseAvAvailablePaymentMethods,
} = require('../../../lib/av-client/v2-av-client')
const { TICKET_PROTECTION_AMOUNT } = require('../../../config')

const handler = require('./payment')

const logStub = {
  info: () => {},
  error: () => {},
  warn: () => {},
}

const createUserStub = () => ({
  session: 'session-id',
  cookie: 'cooookie',
  orderId: 'order-id',
})

const paymentDataStub = [
  {
    paymentId: 'payment-Id',
    type: 'paypal',
  },
]
const pa_request_URL = 'pa_request_URL'
const pa_request_information = '00002p100002v100002p200002v2'

const availablePaymentMethods = parseAvAvailablePaymentMethods(
  mockedAvAddCustomerToOrderResponse
)

const createAvClientStub = () => {
  const closeSessionStub = sinon.stub().resolves()
  const apiRequestStub = sinon.stub()

  return {
    v2InsertCustomer: sinon.stub().resolves({
      customerNumber: 'customer-number',
      customerId: 'customer-id',
    }),
    addCustomerNumberToOrder: sinon
      .stub()
      .resolves({ availablePaymentMethods }),
    addCustomerIdToOrder: sinon.stub().resolves({ availablePaymentMethods }),
    addAdditionalItemsToOrder: sinon.stub().resolves(),
    addDynamicTicketProtection: sinon.stub().resolves(),
    addPaymentsToOrder: sinon.stub().resolves(paymentDataStub),
    insertOrder: sinon.stub().resolves({
      orderNumber: 'order-number',
      admissions: {},
      deliveryMethodDetails: {},
    }),
    fetchOrderPaymentPaRequestData: sinon.stub().resolves({
      pa_request_URL,
      pa_request_information,
    }),
    restoreSession: sinon.stub().returns({
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    }),
    refs: {
      closeSession: closeSessionStub,
      apiRequest: apiRequestStub,
    },
  }
}
const createBodyStub = () => ({
  cc: 'cc-obj',
  delivery: 'post',
  deliveryType: 'eTicket',
  summary: 'summary-obj',
  additionalItems: 'additionalItems-array',
  callbackURL: 'http://www.example.com/callback',
  orderTotal: '100.30',
  answers: 'answers-obj',
  referrerSource: 'referrer-source-parameter',
  singleUsePromoCode: 'single-use-code-parameter',
  showConfig: 'show-config-parameter',
  optOut: false,
})

const sandbox = sinon.createSandbox()

sandbox
  .stub(process.env, 'SINGLE_USE_SERVICE_ENDPOINT')
  .value('SINGLE_USE_SERVICE_ENDPOINT')

sandbox.stub(sessionService, 'setOrderAsBooked').resolves()

const axiosStub = {
  get: sandbox.stub(axios, 'get'),
}

const supc = {
  single_use_code: 'single-use-code-parameter',
  promo_code: '1234',
}

axiosStub.get.resolves(supc)

const dbClientStub = {
  fetchPerformanceById: sinon.stub().resolves({
    venue_slug: 'venue-slug',
  }),
}

describe('payment handler', () => {
  it('payment handler processes the order for payment via creditCard with paymentMethods object - not signed in user', async () => {
    const avStub = createAvClientStub()
    const userStub = createUserStub()
    const bodyStub = createBodyStub()
    const paymentMethodsStub = [
      {
        paymentType: 'creditCard',
        cc: {
          cvv: '100',
          expires: '1020',
          name: 'test test',
          number: '****************',
          type: 'visa',
          amount: 78.65,
        },
      },
    ]

    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    bodyStub.paymentMethods = paymentMethodsStub
    bodyStub.ticketProtection = true

    bodyStub.email = '<EMAIL>' // adding email makes it create a new user.

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'order-number',
    })

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [],
        ticketProtectionFee: 2.75,
        bestAvailable: [],
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via creditCard with paymentMethods object - signed in user', async () => {
    const avStub = createAvClientStub()
    const userStub = createUserStub()
    const bodyStub = createBodyStub()
    const paymentMethodsStub = [
      {
        paymentType: 'creditCard',
        cc: {
          cvv: '100',
          expires: '1020',
          name: 'test test',
          number: '****************',
          type: 'visa',
          amount: 78.65,
        },
      },
    ]

    userStub.seats = {
      'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
        'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
    }
    userStub.generalAdmissions = []
    userStub.bestAvailable = []
    userStub.ticketProtectionFee = TICKET_PROTECTION_AMOUNT

    bodyStub.paymentMethods = paymentMethodsStub
    bodyStub.ticketProtection = true

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'order-number',
    })

    expect(
      avStub.addDynamicTicketProtection.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        userStub.seats,
        userStub.generalAdmissions,
        userStub.ticketProtectionFee,
        userStub.bestAvailable
      )
    ).toBeTruthy()

    expect(
      avStub.addAdditionalItemsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'additionalItems-array'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: {
          'DE6836D9-FF72-4509-8E4A-1976EB56B3C2':
            'B0B0B05E-3D1F-4E65-B8E1-83B5AEE3E74D',
        },
        generalAdmissions: [],
        ticketProtectionFee: 2.75,
        bestAvailable: [],
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: true,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        true,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for payment via paypal with paymentMethods object', async () => {
    const avStub = createAvClientStub()
    const userStub = createUserStub()
    const bodyStub = createBodyStub()

    const insertOrderError = new AvPaymentTokenRequiredError({
      message: '3DS Required',
    })
    avStub.insertOrder = sinon.stub().rejects(insertOrderError)

    bodyStub.paymentMethods = [
      {
        paymentType: 'paypal',
        paypal: {
          amount: 78.65,
        },
      },
    ]

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      insertOrderError
    )

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      redirectTo: pa_request_URL,
      orderId: userStub.orderId,
      paymentRequestParams: { p1: 'v1', p2: 'v2' },
    })

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        undefined,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(
      avStub.fetchOrderPaymentPaRequestData.calledWithExactly(
        userStub.orderId,
        paymentDataStub[0].paymentId,
        avStub.refs.apiRequest
      )
    ).toBeTruthy()

    expect(avStub.refs.closeSession.notCalled).toBeTruthy()
  })

  it('payment handler returns bad gateway when order insertion throws', async () => {
    const avStub = createAvClientStub()
    avStub.insertOrder = sinon
      .stub()
      .rejects(new AvError('something bad happend'))

    const userStub = createUserStub()
    const bodyStub = createBodyStub()

    bodyStub.paymentMethods = [
      {
        paymentType: 'paypal',
        paypal: {
          amount: 78.65,
        },
      },
    ]

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }
    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(new Error())

    const promise = handler(requestStub)

    await expect(promise).rejects.toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        undefined,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    /*
     * expect(replyStub.badGateway.calledWithExactly()).toBeTruthy()
     * the HTTP status will depend on AV's exception number and will be 400
     * by default (bad request).
     * Most likely will be a  402 (payment required).
     */
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order with multiple payment methods - signed in user', async () => {
    const avStub = createAvClientStub()
    const userStub = createUserStub()
    const bodyStub = createBodyStub()
    bodyStub.paymentMethods = [
      {
        paymentType: 'creditCard',
        cc: {
          cvv: '100',
          expires: '1020',
          name: 'test test',
          number: '****************',
          type: 'visa',
          amount: 78.65,
        },
      },
      {
        paymentType: 'giftCertificate',
        giftCertificate: {
          voucherNumber: '1234',
          voucherRedemption: '5678',
          amount: 25,
        },
      },
    ]

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      orderNumber: 'order-number',
    })

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        undefined,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        'profilingId'
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(avStub.fetchOrderPaymentPaRequestData.notCalled).toBeTruthy()
    expect(avStub.refs.closeSession.calledOnce).toBeTruthy()
  })

  it('payment handler processes the order for non existing profilingId', async () => {
    const avStub = createAvClientStub()
    const userStub = createUserStub()
    const bodyStub = createBodyStub()

    const insertOrderError = new AvPaymentTokenRequiredError({
      message: '3DS Required',
    })
    avStub.insertOrder = sinon.stub().rejects(insertOrderError)
    mockGetProfilingIdFromToken.mockReturnValueOnce(null)

    bodyStub.paymentMethods = [
      {
        paymentType: 'paypal',
        paypal: {
          amount: 78.65,
        },
      },
    ]

    const requestStub = {
      avClient: avStub,
      dbClient: dbClientStub,
      user: userStub,
      body: bodyStub,
      log: logStub,
      cookies: {},
      headers: {
        'cf-connecting-ip': '127.0.0.1',
      },
    }

    mockInsertOrderWithDeferredDeliveryRetry.mockRejectedValueOnce(
      insertOrderError
    )

    const promise = handler(requestStub)

    await expect(promise).resolves.toStrictEqual({
      redirectTo: pa_request_URL,
      orderId: userStub.orderId,
      paymentRequestParams: { p1: 'v1', p2: 'v2' },
    })

    expect(
      avStub.v2InsertCustomer.calledWithExactly(
        avStub.refs.apiRequest,
        bodyStub,
        true
      )
    ).toBeTruthy()

    expect(
      avStub.addCustomerNumberToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'customer-number'
      )
    ).toBeTruthy()

    const tempSanitizedReqDetailsStub = {
      userDetails: {
        orderId: 'order-id',
        performance: undefined,
        seats: undefined,
        generalAdmissions: undefined,
        ticketProtectionFee: undefined,
        bestAvailable: undefined,
      },
      delivery: 'post',
      callbackURL: 'http://www.example.com/callback',
      orderTotal: '100.30',
      ticketProtection: undefined,
      answers: 'answers-obj',
      optOut: false,
      additionalItems: 'additionalItems-array',
      referrerSource: 'referrer-source-parameter',
      singleUsePromoCode: 'single-use-code-parameter',
      showConfig: 'show-config-parameter',
    }

    expect(
      avStub.addPaymentsToOrder.calledWithExactly(
        avStub.refs.apiRequest,
        'order-id',
        'http://www.example.com/callback',
        'cc-obj',
        '100.30',
        logStub,
        undefined,
        bodyStub.paymentMethods,
        'additionalItems-array',
        availablePaymentMethods,
        tempSanitizedReqDetailsStub,
        null
      )
    ).toBeTruthy()

    expect(mockInsertOrderWithDeferredDeliveryRetry).toHaveBeenCalledWith(
      avStub.refs.apiRequest,
      'order-id',
      'post',
      'eTicket',
      'answers-obj',
      bodyStub.referrerSource,
      bodyStub.singleUsePromoCode,
      bodyStub.showConfig,
      bodyStub.optOut,
      '127.0.0.1'
    )

    expect(
      avStub.fetchOrderPaymentPaRequestData.calledWithExactly(
        userStub.orderId,
        paymentDataStub[0].paymentId,
        avStub.refs.apiRequest
      )
    ).toBeTruthy()

    expect(avStub.refs.closeSession.notCalled).toBeTruthy()
  })
})
