const { purchaseHandler } = require('./handlers/purchase')
const { purchaseCallbackHandler } = require('./handlers/purchase-callback')

const baseSchema = {
  description: 'Purchase ATG membership',
  tags: ['purchase flow', 'av integration'],
}

module.exports = async function (fastify) {
  fastify.post(
    '/v2/membership/purchase',
    {
      schema: baseSchema,
      preHandler: [fastify.optionalToken],
    },
    purchaseHandler
  )

  fastify.post(
    '/v2/membership/purchase-callback',
    {
      schema: baseSchema,
      preHandler: [fastify.optionalToken],
    },
    purchaseCallbackHandler
  )
}
