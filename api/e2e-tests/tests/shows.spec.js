const { test, expect } = require('@playwright/test')
const Ajv = require('ajv')

const { showCard } = require('../../lib/schemas/index')
const { production } = require('../../lib/schemas')

const ajv = new Ajv()

test('GET all available shows, then get details of a specific show ', async ({
  request,
}) => {
  //Get all shows
  const response = await request.get('/shows')
  const body = await response.json()

  expect(response.status()).toBe(200)
  expect(body).not.toBeNull()

  const firstShow = body[0]

  expect(firstShow.title).toBeDefined()

  const showTitleSlug = firstShow.titleSlug
  const showVenueSlug = firstShow.venueSlug

  const valid = ajv.validate({ type: 'array', items: showCard }, body)

  expect
    .soft(valid, `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`)
    .toBe(true)

  //Get a specific show
  const specificShowResponse = await request.get(
    `/shows/${showTitleSlug}/${showVenueSlug}`
  )
  const specificShowBody = await specificShowResponse.json()
  const isSpecificShowResponseValid = ajv.validate(production, specificShowBody)

  expect
    .soft(
      isSpecificShowResponseValid,
      `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`
    )
    .toBe(true)
  expect(specificShowResponse.status()).toBe(200)
  expect(specificShowBody.titleSlug).toBe(String(showTitleSlug))
  expect(specificShowBody.venueSlug).toBe(String(showVenueSlug))
})
