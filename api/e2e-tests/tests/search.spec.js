const { test, expect } = require('@playwright/test')
const Ajv = require('ajv')

const { venueCard, showCard, seriesSet } = require('../../lib/schemas')

const ajv = new Ajv()
let firstShowTitle

test.beforeAll('Get a show title', async ({ request }) => {
  const showsResponse = await request.get('/shows')
  const showsData = await showsResponse.json()

  expect(showsResponse.status()).toBe(200)
  expect(showsData).not.toBeNull()

  firstShowTitle = showsData[0].title

  expect(firstShowTitle).toBeDefined()
})

test.describe('Tests for the Search endpoint', () => {
  test('Search for a specific show and verify response', async ({
    request,
  }) => {
    const searchResponse = await request.fetch('/search', {
      method: 'get',
      params: { q: firstShowTitle },
    })
    const searchBody = await searchResponse.json()

    expect(searchResponse.status()).toBe(200)
    expect(searchBody.shows[0].title).toBe(String(firstShowTitle))

    const searchSchema = {
      type: 'object',
      properties: {
        venueCard,
        showCard,
        seriesSet,
      },
    }
    const isSearchResponseValid = ajv.validate(searchSchema, searchBody)

    expect
      .soft(
        isSearchResponseValid,
        `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`
      )
      .toBe(true)
  })

  // Skipping due to this bug - https://ambassadors.atlassian.net/browse/QA-73
  test('GET /search/suggest with query', async ({ request }) => {
    const trimmedTitle = firstShowTitle.substring(0, 5).trim()

    const response = await request.fetch('/search/suggest', {
      method: 'get',
      params: { q: trimmedTitle },
    })
    const responseBody = await response.json()

    expect(response.status()).toBe(200)
    expect(responseBody[0]).toBe(trimmedTitle)
    expect(responseBody[1]).toContain(firstShowTitle)
  })
})
