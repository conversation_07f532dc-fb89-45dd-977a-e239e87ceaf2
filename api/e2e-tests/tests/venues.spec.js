const { test, expect } = require('@playwright/test')
const Ajv = require('ajv')

const { venueCard } = require('../../lib/schemas/index')

const ajv = new Ajv()

test('GET venues', async ({ request }) => {
  const response = await request.get('/venues')

  expect(response.status()).toBe(200)

  const responseBody = await response.json()

  expect(responseBody).not.toBeNull()

  const firstVenue = responseBody[0]

  expect(firstVenue.title).toBeDefined()
  expect(typeof firstVenue.title).toBe('string')

  const isVenueSchemaValid = ajv.validate(
    { type: 'array', items: venueCard },
    responseBody
  )

  expect
    .soft(
      isVenueSchemaValid,
      `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`
    )
    .toBe(true)
})
