const { test, expect } = require('@playwright/test')
const Ajv = require('ajv')

const { home } = require('../../lib/schemas/index')

const ajv = new Ajv()

test('GET home', async ({ request }) => {
  const response = await request.get('/home')
  const body = await response.json()

  expect(response.status()).toBe(200)
  expect(body).not.toBeNull()

  const valid = ajv.validate(home, body)

  expect
    .soft(valid, `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`)
    .toBe(true)
})
