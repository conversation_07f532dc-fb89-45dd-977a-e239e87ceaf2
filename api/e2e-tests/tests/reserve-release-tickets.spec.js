const { test, expect } = require('@playwright/test')

const cdsGraphqlQuery = require('../payloads/calendar-date-service')
const { fetchPerformances } = require('../helpers/calendar-date-service')
const { getAvailableSeat } = require('../helpers/admissions')
const { loadAndReplacePayload } = require('../helpers/utils')
const { makePostRequest } = require('../helpers/requests-methods')

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

test.describe.configure({ mode: 'serial' })

test.describe('Tests for reservedSeatsV3/release-seats', () => {
  let performanceId
  let token
  let payloadReplacements
  const headers = { 'content-type': 'application/json' }
  const reserveSeatsUrl = `/reserveseatsv3`
  const releaseSeatsUrl = `/release-seats`

  test.beforeAll(
    'Get a performance and first available seat details',
    async ({ request }) => {
      performanceId = await fetchPerformances(request, cdsGraphqlQuery)

      expect(performanceId).toBeTruthy()

      const seatInfo = await getAvailableSeat(request, performanceId)

      expect(seatInfo).toBeTruthy()

      payloadReplacements = {
        performanceId,
        seatId: seatInfo.availableSeatId,
        priceTypeId: seatInfo.defaultTicketForZone,
      }

      test.info().annotations.push({
        type: 'Performance and Seat Details',
        description: `Performance ID: ${performanceId}, Seat Info: ${JSON.stringify(
          seatInfo
        )}`,
      })
    }
  )

  test('Reserve an available seat and try to reserve an already reserved seat', async ({
    request,
  }) => {
    const headers = { 'content-type': 'application/json' }
    const requestPayload = loadAndReplacePayload(
      'e2e-tests/payloads/reservedSeatsV3.json',
      payloadReplacements
    )

    let response = await makePostRequest(
      request,
      reserveSeatsUrl,
      headers,
      requestPayload
    )
    let responseBody = await response.json()

    expect(response.status()).toBe(200)
    expect(responseBody).toHaveProperty('token')
    expect(responseBody.token).toBeTruthy()

    token = responseBody.token

    await delay(2000)

    // Try to reserve an already reserved seat
    response = await makePostRequest(
      request,
      reserveSeatsUrl,
      headers,
      requestPayload
    )
    responseBody = await response.json()

    expect(response.status()).toBe(410)
    expect(responseBody.message).toBe('These seats are no longer available.')
  })

  test('Release a reserved seat and reserve a released seat', async ({
    request,
  }) => {
    const releaseHeader = { authorization: `Bearer ${token}` }

    // Release a reserved seat
    let response = await makePostRequest(
      request,
      releaseSeatsUrl,
      releaseHeader
    )
    let responseBody = await response.json()

    expect(response.status()).toBe(200)
    expect(responseBody.msg).toBe('success')

    await delay(2000)

    // Reserve a released seat
    const requestPayload = loadAndReplacePayload(
      'e2e-tests/payloads/reservedSeatsV3.json',
      payloadReplacements
    )
    response = await makePostRequest(
      request,
      reserveSeatsUrl,
      headers,
      requestPayload
    )

    responseBody = await response.json()

    expect(response.status()).toBe(200)
    expect(responseBody).toHaveProperty('token')
    expect(responseBody.token).toBeTruthy()

    token = responseBody.token
  })

  test.afterAll('Release the last reserved seat', async ({ request }) => {
    const releaseHeaders = {
      accept: '*/*',
      authorization: `Bearer ${token}`,
    }
    const response = await makePostRequest(
      request,
      releaseSeatsUrl,
      releaseHeaders
    )
    const responseBody = await response.json()

    expect(response.status()).toBe(200)
    expect(responseBody.msg).toBe('success')
  })
})
