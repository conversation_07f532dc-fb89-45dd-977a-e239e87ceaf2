import { test as setup } from '@playwright/test'

import { voucherGenerator } from '../helpers/voucher-generator'

setup('Voucher setup', async ({ request }) => {
  setup.setTimeout(60000)
  console.log('Generating a new voucher...')
  try {
    const voucherData = await voucherGenerator(request)
    if (voucherData) {
      console.log(voucherData)
      process.env.GIFT_CERTIFICATE_NUMBER = voucherData.giftCertificateNumber
      process.env.GIFT_CERTIFICATE_REDEMPTION_NUMBER =
        voucherData.giftCertificateRedemptionNumber
      console.log('Voucher generated successfully')
    } else {
      console.log('Backup voucher will be used for test.')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error(error.message)
    }
  }
})
