const { test, expect } = require('@playwright/test')
const Ajv = require('ajv')

const { boltRootData } = require('../../lib/schemas/index')

const ajv = new Ajv()

test('GET /cms/root', async ({ request }) => {
  const response = await request.get('/cms/root')
  const responseBody = await response.json()

  expect(response.status()).toBe(200)
  expect(responseBody).not.toBeNull()

  const isCmsRootResponseValid = ajv.validate(boltRootData, responseBody)

  expect
    .soft(
      isCmsRootResponseValid,
      `AJV Schema Validation Errors: ${JSON.stringify(ajv.errors)}`
    )
    .toBe(true)
})
