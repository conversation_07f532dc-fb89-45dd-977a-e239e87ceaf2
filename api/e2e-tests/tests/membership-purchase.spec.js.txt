import { test, expect } from '@playwright/test'

const { login } = require('../helpers/auth-service-login')
const { getSessionId } = require('../helpers/session-service')
const {
  updateCustomerBenefits,
  fetchCustomerData,
} = require('../helpers/avCancelOrder')
const { purchaseMembership } = require('../helpers/makePayment')

const paymentTypes = [
  'directDebit',
  'directDebit+TP',
  'creditCard',
  'creditCard+TP',
]

paymentTypes.forEach((paymentTypes) => {
  test.describe.parallel('Membership purchase', () => {
    let sessionId
    let loginResponseData
    let benefitsIds

    test.beforeEach(async ({ request }) => {
      sessionId = await getSessionId(request)
      loginResponseData = await login(
        request,
        process.env.BACKEND_TEST_USER,
        sessionId
      )
    })

    test(`Membership purchase using ${paymentTypes}`, async ({ request }) => {
      const response = await purchaseMembership(
        request,
        sessionId,
        loginResponseData.userData,
        loginResponseData.userSig,
        paymentTypes
      )

      expect(response.ok()).toBeTruthy()
      expect(await response.json()).toHaveProperty('membershipNumber')
    })

    test.afterEach(async ({ request }) => {
      try {
        const customerData = await fetchCustomerData(request)
        const benefits = customerData?.data?.Benefits || {}
        benefitsIds = Object.keys(benefits).filter((key) => key !== 'state')

        await updateCustomerBenefits(request, benefitsIds)
      } catch (error) {
        console.error('Error in afterEach hook:', error)
      }
    })
  })
})
