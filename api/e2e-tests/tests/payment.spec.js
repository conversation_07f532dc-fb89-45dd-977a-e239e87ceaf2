const { test, expect } = require('@playwright/test')

const cdsGraphqlQuery = require('../payloads/calendar-date-service')
const { fetchPerformances } = require('../helpers/calendar-date-service')
const { getCheapestAvailableSeat } = require('../helpers/admissions')
const { loadAndReplacePayload } = require('../helpers/utils')
const { makePostRequest } = require('../helpers/requests-methods')
const { performPayment } = require('../helpers/makePayment')
const { avOrderCancellation } = require('../helpers/avCancelOrder')

test.describe.parallel('Payments', () => {
  let performanceId
  let token
  let totalValue
  let payloadReplacements
  let paymentResponse
  let responseBody
  let orderNumber
  const headers = { 'content-type': 'application/json' }
  const reserveSeatsUrl = `/reserveseatsv3`
  const paymentUrl = `/payment`

  test.beforeEach(
    'Retrieve performance details and find the cheapest available seat for payment',
    async ({ request }) => {
      performanceId = await fetchPerformances(request, cdsGraphqlQuery)

      expect(performanceId).toBeTruthy()

      const seatInfo = await getCheapestAvailableSeat(request, performanceId)

      expect(seatInfo).toBeTruthy()

      payloadReplacements = {
        performanceId,
        seatId: seatInfo.availableSeatId,
        priceTypeId: seatInfo.defaultTicketForZone,
      }
      const requestPayload = loadAndReplacePayload(
        'e2e-tests/payloads/reservedSeatsV3.json',
        payloadReplacements
      )

      const response = await makePostRequest(
        request,
        reserveSeatsUrl,
        headers,
        requestPayload
      )
      responseBody = await response.json()

      expect(response.status()).toBe(200)
      expect(responseBody).toHaveProperty('token')
      expect(responseBody.token).toBeTruthy()

      token = responseBody.token
      totalValue = responseBody.priceChange.seats[0].total

      test.info().annotations.push({
        type: 'Performance and Seat Details',
        description: `Performance ID: ${performanceId}, Seat Info: ${JSON.stringify(
          seatInfo
        )}, Total Amount: ${totalValue})}`,
      })
    }
  )

  const paymentOptions = ['creditCard', 'voucher', 'voucherAndCreditCard']
  for (const paymentOption of paymentOptions) {
    test(`Payment using ${paymentOption}`, async ({ request }) => {
      paymentResponse = await performPayment(
        request,
        token,
        paymentUrl,
        paymentOption,
        totalValue
      )
      responseBody = await paymentResponse.json()

      expect(paymentResponse.status()).toBe(200)
      expect(responseBody).toHaveProperty('orderNumber')
      expect(responseBody.orderNumber).toBeTruthy()

      orderNumber = await responseBody.orderNumber

      test.info().annotations.push({
        type: 'Order Details',
        description: `Order ID: ${JSON.stringify(
          responseBody
        )}, Total Amount: ${totalValue})}`,
      })
    })
  }

  test.afterEach('Cancel Order - Clean up', async ({ request }) => {
    const cancelledOrderDetails = await avOrderCancellation(
      request,
      orderNumber
    )

    test.info().annotations.push({
      type: 'Cancellation Details',
      description: `AV Cancellation Details: ${JSON.stringify(
        cancelledOrderDetails
      )}`,
    })
  })
})
