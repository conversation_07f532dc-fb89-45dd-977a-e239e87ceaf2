const { test, expect } = require('@playwright/test')

const {
  reserveCheapestSeat,
  releaseReservedSeat,
} = require('../helpers/reserve-release-seat')

const voucherNumber = process.env.GIFT_CERTIFICATE_NUMBER || '1106347'
const voucherRedemptionNumber =
  process.env.GIFT_CERTIFICATE_REDEMPTION_NUMBER || 'I036Z3LZW1V2'

let token
async function validateVoucher(request, voucherNumber, redemptionCode, token) {
  const voucherResponse = await request.get(
    `/voucher/validate/${voucherNumber}/${redemptionCode}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  )

  const voucherResponseBody = await voucherResponse.json()

  return { response: voucherResponse, body: voucherResponseBody }
}

test.describe.parallel('voucher validate', () => {
  test.beforeAll(
    'Retrieve performance details, find the cheapest available seat and reserve seat',
    async ({ request }) => {
      const reservationData = await reserveCheapestSeat(request)
      token = reservationData.token
    }
  )

  test('Validate valid voucher with invalid bearer token', async ({
    request,
  }) => {
    const { response, body } = await validateVoucher(
      request,
      voucherNumber,
      voucherNumber,
      'invalid'
    )

    expect(response.status()).toBe(401)
    expect(body.message).toBeTruthy()
    expect(body.message).toContain('Authorization token is invalid')
  })

  test('Validate valid voucher - Happy path', async ({ request }) => {
    const { response, body } = await validateVoucher(
      request,
      voucherNumber,
      voucherRedemptionNumber,
      token
    )

    expect(response.status()).toBe(200)
    expect(body.balance).toBeTruthy()
    expect(body.expiry).toBeTruthy()
    expect(typeof body.balance).toBe('number')
    expect(typeof body.expiry).toBe('string')
  })

  test('Validate invalid voucher number', async ({ request }) => {
    const { response, body } = await validateVoucher(
      request,
      '00000',
      voucherRedemptionNumber,
      token
    )

    expect(response.status()).toBe(422)
    expect(body.message).toBeTruthy()
    expect(body.message).toBe(
      'The voucher number / redemption code combination is invalid'
    )
  })

  test('Validate invalid redemption code', async ({ request }) => {
    const { response, body } = await validateVoucher(
      request,
      voucherNumber,
      '000000',
      token
    )

    expect(response.status()).toBe(422)
    expect(body.message).toBeTruthy()
    expect(body.message).toBe(
      'The voucher number / redemption code combination is invalid'
    )
  })

  test('Validate invalid voucher number and redemption code', async ({
    request,
  }) => {
    const { response, body } = await validateVoucher(
      request,
      '000000',
      '000000',
      token
    )

    expect(response.status()).toBe(422)
    expect(body.message).toBeTruthy()
    expect(body.message).toBe(
      'The voucher number / redemption code combination is invalid'
    )
  })

  test.afterAll('Release reserved seat', async ({ request }) => {
    await releaseReservedSeat(request, token)
  })
})
