const getSessionId = async function createSession(request) {
  const response = await request.post(
    'https://session-service-stage.atgtickets.com/graphql',
    {
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        query: 'mutation { createSession }',
      },
    }
  )

  if (!response.ok) {
    throw new Error(
      `Failed to create session: ${response.status} ${response.statusText}`
    )
  }

  const setCookieHeader = response
    .headersArray()
    .find((header) => header.name.toLowerCase() === 'set-cookie')

  if (!setCookieHeader) {
    throw new Error('Set-Cookie header is missing in the response')
  }

  const sessionId = setCookieHeader.value.match(/bolt-session=([^;]+)/)?.[1]

  if (!sessionId) {
    throw new Error('Bolt session ID not found in the Set-Cookie header')
  }

  return sessionId
}
module.exports = { getSessionId }
