import { avAuthenticate } from './utils'

const apiKey = process.env.STAGING_AV_UK_API_KEY
const baseUrl = 'https://api.staging.secure.atgtickets.com/app/WebAPI/v2/'
const authUrl = `${baseUrl}session/authenticateUser?api_key=${apiKey}`
const orderUrlWithApiKey = `${baseUrl}order?api_key=${apiKey}`

export async function manageGiftCertificates(request) {
  const orderResponse = await request.post(orderUrlWithApiKey, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'manageGiftCertificates',
          params: {
            'addStoredValueID': 'CB458660-7463-4838-A79D-E25622EF86EC',
            'action': 'Purchase',
            'GiftCertificate::amount': '1500.00',
            'GiftCertificate::recipient': 'Tester Tester',
            'GiftCertificate::message': 'Testing Testing',
          },
        },
      ],
      objectName: 'myOrderRef',
      get: ['Order::grand_total'],
    },
  })

  if (!orderResponse.ok()) {
    throw new Error(
      `Could not manage gift certificates\nError text: ${JSON.stringify(
        await orderResponse.json()
      )} ${orderResponse.statusText()}`
    )
  }
}

async function addCustomerToOrder(request) {
  const addCustomerToOrderResponse = await request.post(orderUrlWithApiKey, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'addCustomer',
          params: {
            Customer: {
              customer_id: 'E8D2B013-987A-4316-9A0E-BB7BADF02E49',
            },
          },
        },
      ],
      objectName: 'myOrderRef',
      get: ['AvailablePaymentMethods'],
    },
  })

  if (!addCustomerToOrderResponse.ok()) {
    throw new Error(
      `Could not add customer to order\nError text: ${JSON.stringify(
        await addCustomerToOrderResponse.json()
      )} ${addCustomerToOrderResponse.statusText()}`
    )
  }
}

async function addPayment(request) {
  const addPaymentResponse = await request.post(orderUrlWithApiKey, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'addPayment',
          params: {
            'paymentType': '0',
            'paymentMethodId': 'E0384EDB-72C7-412E-B32D-338B88D996FF',
            'Payment::account_number': '****************',
            'Payment::expiration_date': '1230',
            'Payment::cvv_code': '321',
            'Payment::cardholder_name': 'Testing Testing',
            'Payment::pa_response_URL': `https://stage.${process.env.domain}/checkout/verify-receiver?bolt=1`,
            'Payment::swipe_indicator': 'Internet',
            'Payment::transaction_amount': '1500',
          },
        },
      ],
      objectName: 'myOrderRef',
      get: ['Order::lastAddedPayments', 'Payments'],
    },
  })
  if (!addPaymentResponse.ok()) {
    throw new Error(
      `Could not add payment\nError text: ${JSON.stringify(
        await addPaymentResponse.json()
      )} ${addPaymentResponse.statusText()}`
    )
  }
}

async function insertOrder(request) {
  const orderResponse = await request.post(orderUrlWithApiKey, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'insert',
          params: {
            notification: 'correspondence',
          },
        },
      ],
      objectName: 'myOrderRef',
      set: {
        'Order::deliverymethod_id': 'C30998BC-F652-44F1-B695-D4CEFA417CE8',
        'Marketing::data4': 'Bolt',
        'Marketing::data10': '0',
        'Marketing::data17': 'atg',
      },
      get: ['Order::order_number', 'GiftCertificates'],
    },
  })

  if (!orderResponse.ok()) {
    throw new Error(
      `Could not insert order\nError text: ${JSON.stringify(
        await orderResponse.json()
      )} ${orderResponse.statusText()}`
    )
  }

  const responseData = await orderResponse.json()
  const giftCertificates = responseData.data.GiftCertificates

  if (!giftCertificates || typeof giftCertificates !== 'object') {
    console.error('GiftCertificates not found or invalid')
    return null
  }

  for (const certificateId in giftCertificates) {
    if (Object.prototype.hasOwnProperty.call(giftCertificates, certificateId)) {
      const certificateData = giftCertificates[certificateId]

      if (certificateData && typeof certificateData === 'object') {
        const giftCertificateNumber =
          certificateData.gift_certificate_number?.standard
        const giftCertificateRedemptionNumber =
          certificateData.gift_certificate_redemption_number?.standard

        if (giftCertificateNumber && giftCertificateRedemptionNumber) {
          return {
            giftCertificateNumber,
            giftCertificateRedemptionNumber,
          }
        }
      }
    }
  }
  return null
}

export async function voucherGenerator(request) {
  try {
    await avAuthenticate(request, authUrl)
    await manageGiftCertificates(request)
    await addCustomerToOrder(request)
    await addPayment(request)
    return await insertOrder(request)
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error during voucher generation:', error.message)
    } else {
      console.error('Unknown error during voucher generation:', error)
    }
    return null
  }
}
