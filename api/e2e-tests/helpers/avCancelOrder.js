const apiKey = process.env.AV_APIKEY_STAGING
const baseUrl = `https://${process.env.AV_DOMAIN_STAGING}/app/WebAPI/v2`
const authUrl = `${baseUrl}/session/authenticateUser?api_key=${apiKey}`
const orderUrl = `${baseUrl}/order?api_key=${apiKey}`
const logoutUrl = `${baseUrl}/session/logout?api_key=${apiKey}`
const customerUrl = `${baseUrl}/customer?api_key=${apiKey}`

export async function avAuthenticate(request) {
  const authResponse = await request.post(authUrl, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    form: {
      userid: process.env.SECRET_AV_USERNAME,
      password: process.env.SECRET_AV_PASSWORD,
    },
  })
  if (!authResponse.ok()) {
    throw new Error(
      `Could not authenticate with AV\n Error text: ${authResponse.status()} ${authResponse.statusText()}`
    )
  }
}

async function avLoadOrder(request, orderNumber) {
  const loadOrderResponse = await request.post(orderUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'load',
          params: {
            Order: {
              order_number: orderNumber,
            },
          },
        },
      ],
      objectName: 'myOrderRef',
      get: ['Order::grand_total', 'Payments'],
    },
  })
  if (!loadOrderResponse.ok()) {
    throw new Error(
      `Could not load order ${orderNumber} from AV\n Error text: ${loadOrderResponse.status()} ${loadOrderResponse.statusText()}`
    )
  }

  const loadOrderJson = await loadOrderResponse.json()

  if (!loadOrderJson.data['Order::grand_total']) {
    throw new Error(
      `Order total not present in ${JSON.stringify(loadOrderJson)}`
    )
  }
  return loadOrderJson.data['Order::grand_total']
}

async function avCancelOrder(request) {
  const cancelOrderResponse = await request.post(orderUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'cancelOrderItems',
        },
      ],
      objectName: 'myOrderRef',
    },
  })

  if (!cancelOrderResponse.ok()) {
    throw new Error(
      `Could not cancel order with AV\n Error text: ${cancelOrderResponse.status()} ${cancelOrderResponse.statusText()}`
    )
  }
}

async function updateOrderCancellation(request) {
  const updateOrderResponse = await request.post(orderUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'update',
          acceptWarnings: [5282],
        },
      ],
      objectName: 'myOrderRef',
      set: {
        'Order::cancel_reason': 'Test transaction',
      },
    },
  })

  if (!updateOrderResponse.ok()) {
    throw new Error(
      `Could not update order with AV\n Error text: ${updateOrderResponse.status()} ${updateOrderResponse.statusText()}`
    )
  }

  const responseBody = await updateOrderResponse.json()

  if (!responseBody.return[0].message.includes('Order updated')) {
    throw new Error(`Response body was ${JSON.stringify(responseBody)}`)
  }
}

async function logout(request) {
  const logoutResponse = await request.post(logoutUrl)
  if (!logoutResponse.ok()) {
    throw new Error(
      `Could not logout from AV\n Error text: ${logoutResponse.status()} ${logoutResponse.statusText()}`
    )
  }
}

export async function avOrderCancellation(request, orderNumber) {
  const maxRetries = 5
  let attempt = 0

  while (attempt < maxRetries) {
    try {
      await avAuthenticate(request)
      await avLoadOrder(request, orderNumber)
      await avCancelOrder(request)
      await updateOrderCancellation(request)
      return await avLoadOrder(request, orderNumber)
    } catch (error) {
      console.error(
        `Attempt ${attempt + 1} failed: ${
          error instanceof Error ? error.message : error
        }`
      )

      try {
        await logout(request)
      } catch (logoutError) {
        console.error(
          `Logout failed: ${
            logoutError instanceof Error ? logoutError.message : logoutError
          }`
        )
      }

      if (attempt >= maxRetries - 1) {
        console.error(`Failed to cancel order after ${maxRetries} attempts`)
        return null
      }
    } finally {
      attempt++
    }
  }
  return null
}

export async function updateCustomerBenefits(request, benefits) {
  const benefitsObject = benefits.reduce((acc, uuid) => {
    acc[uuid] = null
    return acc
  }, {})

  const response = await request.post(customerUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      set: {
        Benefits: benefitsObject,
      },
      actions: [
        {
          method: 'update',
        },
      ],
      objectName: 'customer',
      get: ['Customer', 'Benefits'],
    },
  })
  return response
}

export async function fetchCustomerData(request) {
  await avAuthenticate(request)
  const response = await request.post(customerUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      actions: [
        {
          method: 'load',
          params: {
            'Customer::customer_id': 'DCEDD78D-D966-4B96-8291-5C104CB8CAD0',
          },
        },
      ],
      get: ['Benefits'],
      objectName: 'customer',
    },
  })
  return await response.json()
}
