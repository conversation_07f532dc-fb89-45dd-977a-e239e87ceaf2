const { expect } = require('@playwright/test')

const fetchPerformances = async (request, graphqlQuery) => {
  const response = await request.post(
    // 'https://calendar-dates-service-stage.atgtickets.com/graphql',
    'https://calendar-service.core.platform.stage-atgtickets.com/',
    {
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        query: graphqlQuery,
      },
    }
  )

  const responseData = await response.json()
  expect(response.status()).toBe(200)
  //
  // expect(
  //   responseData && responseData.data.fetchNextPerformances.performances
  // ).toBeTruthy()
  //
  // const performances = responseData.data.fetchNextPerformances.performances
  // const today = new Date().toISOString()
  // const filteredPerformances = performances.filter(
  //   (performance) => new Date(performance.dateTime) >= new Date(today)
  // )
  //
  // const randomIndex = Math.floor(Math.random() * filteredPerformances.length)
  // const randomPerformance = filteredPerformances[randomIndex]
  //
  // return randomPerformance.performanceId

  expect(
    responseData && responseData.data.getShow.show.performances
  ).toBeTruthy()

  const performances = responseData.data.getShow.show.performances
  const today = new Date().toISOString()
  const filteredPerformances = performances.filter(
    (performance) =>
      new Date(performance.dates.performanceDate) >= new Date(today)
  )

  if (filteredPerformances.length === 0) {
    throw new Error('No upcoming performances found.')
  }

  const randomIndex = Math.floor(Math.random() * filteredPerformances.length)
  const randomPerformance = filteredPerformances[randomIndex]

  return randomPerformance.id
}

module.exports = { fetchPerformances }
