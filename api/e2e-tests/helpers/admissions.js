const { expect } = require('@playwright/test')

const getAvailableSeat = async (request, performanceId) => {
  const response = await request.get(
    `https://boltapi.stage-atgtickets.com/admissions/wicked/apollo-victoria-theatre/${performanceId}`,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )

  const responseData = await response.json()
  expect(await response.status()).toBe(200)
  expect(responseData.seats && responseData.zones).toBeTruthy()

  const availableSeat = responseData.seats.find(
    (seat) => seat.available === true
  )
  expect(availableSeat).toBeTruthy()

  const zone = responseData.zones[availableSeat.zone]
  expect(zone).toBeTruthy()

  const defaultTicketForZone = zone.defaultTicket
  expect(defaultTicketForZone).toBeTruthy()

  return {
    availableSeatId: availableSeat.id,
    defaultTicketForZone,
  }
}

const getCheapestAvailableSeat = async (request, performanceId) => {
  try {
    const response = await request.get(
      `https://boltapi.stage-atgtickets.com/admissions/wicked/apollo-victoria-theatre/${performanceId}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    const responseData = await response.json()
    expect(await response.status()).toBe(200)
    expect(responseData.seats && responseData.zones).toBeTruthy()

    let minTotalAmount = Number.MAX_SAFE_INTEGER
    let minTotalAmountZoneId = null
    let availableSeatId = null
    let defaultTicketForZone = null

    const zones = responseData.zones

    for (const zoneId in zones) {
      const zone = zones[zoneId]
      const totalAmount = parseFloat(zone.tickets[zone.defaultTicket].total)

      if (totalAmount < minTotalAmount) {
        minTotalAmount = totalAmount
        minTotalAmountZoneId = zone.id

        const correspondingSeats = responseData.seats.filter(
          (seat) => seat.zone === minTotalAmountZoneId
        )
        const availableSeat = correspondingSeats.find(
          (seat) => seat.available === true
        )

        if (availableSeat) {
          availableSeatId = availableSeat.id
          defaultTicketForZone = zone.defaultTicket
        }
      }
    }

    if (availableSeatId !== null) {
      if (defaultTicketForZone !== null) {
        return { availableSeatId, defaultTicketForZone }
      } else {
        throw new Error('availableSeatId or defaultTicketId is null.')
      }
    } else {
      throw new Error('No available seats found.')
    }
  } catch (error) {
    throw new Error(`Error fetching data: ${error.message}`)
  }
}

module.exports = { getAvailableSeat, getCheapestAvailableSeat }
