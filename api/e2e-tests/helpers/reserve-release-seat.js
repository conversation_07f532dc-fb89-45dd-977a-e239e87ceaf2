const { test, expect } = require('@playwright/test')

const cdsGraphqlQuery = require('../payloads/calendar-date-service')
const { fetchPerformances } = require('../helpers/calendar-date-service')
const { getCheapestAvailableSeat } = require('../helpers/admissions')
const { loadAndReplacePayload } = require('../helpers/utils')
const { makePostRequest } = require('../helpers/requests-methods')

const headers = { 'content-type': 'application/json' }
const baseUrl = 'https://boltapi.stage-atgtickets.com'
const reserveSeatsUrl = `${baseUrl}/reserveseatsv3`
const releaseSeatsUrl = `${baseUrl}/release-seats`

export async function reserveCheapestSeat(request) {
  const performanceId = await fetchPerformances(request, cdsGraphqlQuery)
  expect(performanceId).toBeTruthy()

  const seatInfo = await getCheapestAvailableSeat(request, performanceId)
  expect(seatInfo).toBeTruthy()

  const payloadReplacements = {
    performanceId,
    seatId: seatInfo.availableSeatId,
    priceTypeId: seatInfo.defaultTicketForZone,
  }

  const requestPayload = loadAndReplacePayload(
    'e2e-tests/payloads/reservedSeatsV3.json',
    payloadReplacements
  )

  const response = await makePostRequest(
    request,
    reserveSeatsUrl,
    headers,
    requestPayload
  )
  const responseBody = await response.json()

  if (response.status() !== 200) {
    console.error(`Seat reservation failed: ${responseBody}`)
  }

  expect(response.status()).toBe(200)
  expect(responseBody).toHaveProperty('token')
  expect(responseBody.token).toBeTruthy()

  const token = responseBody.token
  const totalValue = responseBody.priceChange.seats[0].total

  test.info().annotations.push({
    type: 'Performance and Seat Details',
    description: `Performance ID: ${performanceId}, Seat Info: ${JSON.stringify(
      seatInfo
    )}, Total Amount: ${totalValue}`,
  })

  return {
    token,
    totalValue,
  }
}

export async function releaseReservedSeat(request, token) {
  const releaseHeaders = {
    accept: '*/*',
    authorization: `Bearer ${token}`,
  }

  const response = await makePostRequest(
    request,
    releaseSeatsUrl,
    releaseHeaders
  )

  const responseBody = await response.json()

  if (response.status() !== 200) {
    console.error(
      'Failed to release reserved seat. Status code:',
      response.status()
    )
    console.error('Response body:', responseBody)
  }
}
