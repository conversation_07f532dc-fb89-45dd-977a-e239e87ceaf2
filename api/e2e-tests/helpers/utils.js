const fs = require('fs')

const loadAndReplacePayload = (filePath, replacements) => {
  try {
    const jsonData = fs.readFileSync(filePath, 'utf8')
    let payload = JSON.parse(jsonData)

    for (const placeholder in replacements) {
      if (Object.prototype.hasOwnProperty.call(replacements, placeholder)) {
        payload = JSON.parse(
          JSON.stringify(payload).replace(
            new RegExp(`{{${placeholder}}}`, 'g'),
            replacements[placeholder]
          )
        )
      }
    }
    return payload
  } catch (error) {
    throw new error('Error loading or updating payload:', error)
  }
}

export async function avAuthenticate(request, authUrl) {
  const authResponse = await request.post(authUrl, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    form: {
      userid: process.env.SECRET_AV_USERNAME || '',
      password: process.env.SECRET_AV_PASSWORD || '',
    },
  })
  if (!authResponse.ok()) {
    throw new Error(
      `Could not authenticate with AV\n Error text: ${authResponse.status()} ${authResponse.statusText()}`
    )
  }
}

module.exports = { loadAndReplacePayload, avAuthenticate }
