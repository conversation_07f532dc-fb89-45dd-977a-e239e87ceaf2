const fs = require('fs')

const { getTokenEx } = require('../helpers/tokenEx')

const { makePostRequest } = require('./requests-methods')

let creditCardAmount
let voucherAmount

const voucherNumber = process.env.GIFT_CERTIFICATE_NUMBER || '1106347'
const voucherRedemption =
  process.env.GIFT_CERTIFICATE_REDEMPTION_NUMBER || 'I036Z3LZW1V2'

async function performPayment(
  request,
  token,
  paymentUrl,
  paymentType,
  totalValue
) {
  const tokenEx = await getTokenEx(request)
  const authorizationHeader = { authorization: `Bearer ${token}` }
  let jsonData
  try {
    const data = fs.readFileSync('e2e-tests/payloads/payment.json', 'utf8')
    jsonData = JSON.parse(data)
  } catch (err) {
    throw new Error(`Error reading JSON file:${err}`)
  }

  jsonData.orderTotal = parseFloat(totalValue)

  switch (paymentType) {
    case 'creditCard':
      jsonData.paymentMethods = [
        {
          paymentType: 'creditCard',
          txToken: true,
          cc: {
            cvv: '',
            expires: '1028',
            name: 'Test Test',
            number: `{{{${tokenEx}}}}`,
            type: 'mastercard',
            amount: parseFloat(totalValue),
          },
        },
      ]
      break
    case 'voucher':
      jsonData.paymentMethods = [
        {
          paymentType: 'giftCertificate',
          giftCertificate: {
            voucherNumber,
            voucherRedemption,
            amount: parseFloat(totalValue),
          },
        },
      ]
      break

    case 'voucherAndCreditCard':
      creditCardAmount = totalValue > 10 ? totalValue - 10 : totalValue - 1
      voucherAmount = totalValue > 10 ? 10 : 1

      jsonData.paymentMethods = [
        {
          paymentType: 'giftCertificate',
          giftCertificate: {
            voucherNumber,
            voucherRedemption,
            amount: voucherAmount,
          },
        },
        {
          paymentType: 'creditCard',
          txToken: true,
          cc: {
            cvv: '',
            expires: '1028',
            name: 'Test Test',
            number: `{{{${tokenEx}}}}`,
            type: 'mastercard',
            amount: creditCardAmount,
          },
        },
      ]
      break
    default:
      throw new Error(`Invalid payment type: ${paymentType}`)
  }
  return await makePostRequest(
    request,
    paymentUrl,
    authorizationHeader,
    jsonData
  )
}

function calculateCollectionDate(purchaseDate) {
  const date = new Date(purchaseDate)
  date.setDate(date.getDate() + 14)

  const fifteenthOfMonth = new Date(date.getFullYear(), date.getMonth(), 15)

  if (date < fifteenthOfMonth) {
    return fifteenthOfMonth.toISOString().split('T')[0]
  } else {
    const nextMonthFirst = new Date(date.getFullYear(), date.getMonth() + 1, 1)
    return nextMonthFirst.toISOString().split('T')[0]
  }
}

function generateBacsReference() {
  const now = new Date()
  return `ATG${now.toISOString().slice(5, 7)}${now
    .toISOString()
    .slice(8, 10)}${now.toTimeString().slice(0, 8).replace(/:/g, '')}`
}

async function purchaseMembership(
  request,
  sessionId,
  userData,
  userSig,
  paymentType
) {
  const requestBody = {
    delivery: 'DEC3A1B2-0796-4B1A-B33B-D8A896DDE44B',
    membership: {
      itemId:
        paymentType === 'creditCard'
          ? '6DF525C4-B17C-4C30-9E0A-A243705186BA'
          : paymentType === 'directDebit+TP'
            ? 'A571968E-0B1A-44A8-AEE2-4D0AA12AA028'
            : paymentType === 'creditCard+TP'
              ? '667B1EFD-EB86-42BB-849A-16E0E88CDF28'
              : '2F6428F4-D7C8-4DFB-9FF1-E429D8EB572B',
      quantity: '1',
    },
    paymentMethods: [],
    preferredVenue: 'West End',
    callbackUrl:
      'https://stage.atgtickets.com/membership/purchase/dd/atgplus/verify-receiver',
  }

  if (paymentType === 'creditCard' || paymentType === 'creditCard+TP') {
    const tokenEx = await getTokenEx(request)
    requestBody.paymentMethods.push({
      paymentType: 'creditCard',
      txToken: true,
      cc: {
        expires: '0726',
        name: 'test tests',
        number: `{{{${tokenEx}}}}`,
        type: 'mastercard',
        amount: paymentType === 'creditCard+TP' ? 85 : 60,
      },
    })
  } else {
    const purchaseDate = new Date()
    const collectionDate = calculateCollectionDate(purchaseDate)
    requestBody.paymentMethods.push({
      paymentType: 'directDebit',
      directDebit: {
        accountNumber: '********',
        sortCode: '606004',
        accountName: 'ATG+ User',
        bacsReference: generateBacsReference(),
        amount: paymentType === 'directDebit+TP' ? 75 : 50,
        //todo: add collection - validate collection date when this is moved to a separate service out of bolt api
        collectionDate,
      },
    })
  }

  const response = await request.post(
    'https://boltapi-stage.atgtickets.com/v2/membership/purchase',
    {
      headers: {
        'accept': '*/*',
        'bolt-brand': 'atgtickets.com',
        'content-type': 'application/json',
        'cookie': `bolt-session=${sessionId}; user-data=${userData}; user-sig=${userSig}`,
      },
      data: requestBody,
    }
  )

  return response
}

module.exports = { performPayment, purchaseMembership }
