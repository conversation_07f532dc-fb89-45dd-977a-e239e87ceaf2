import { expect } from '@playwright/test'

require('dotenv').config()

const getTokenEx = async (request) => {
  const response = await request.post(
    'https://test-api.tokenex.com/v2/Pci/Tokenize',
    {
      headers: {
        'tx-token-scheme': 'PCI',
        'tx-tokenex-id': process.env.TOKENEX_ID,
        'tx-apikey': process.env.TOKENEX_APIKEY,
        'Content-Type': 'application/json',
      },
      data: {
        data: '5200000000003001',
        cvv: '490',
      },
    }
  )

  expect(response.status()).toBe(200)
  const responseData = await response.json()
  expect(responseData.token).toBeTruthy()
  return responseData.token
}

module.exports = { getTokenEx }
