import { expect } from '@playwright/test'

const login = async function authServiceLogin(request, email, sessionId) {
  const response = await request.post(
    `https://auth-service-stage.atgtickets.com/login`,
    {
      headers: {
        'accept': '*/*',
        'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'origin': 'stage.atgtickets.com',
        'cookie': `bolt-session=${sessionId}`,
      },
      form: {
        email,
        password: process.env.TEST_PASSWORD,
        rememberMe: 'false',
      },
    }
  )

  expect(response.ok()).toBeTruthy()
  const responseBody = await response.json()
  expect(responseBody).toEqual({
    code: 'SUCCESS',
  })

  const setCookieHeader = response.headers()['set-cookie']
  if (!setCookieHeader) {
    throw new Error('No set-cookie header found in the response')
  }

  const cookies = setCookieHeader.split(';').map((cookie) => cookie.trim())

  const userDataCookie = cookies.find((cookie) =>
    cookie.startsWith('user-data=')
  )
  if (!userDataCookie) {
    throw new Error('user-data cookie not found')
  }
  const userData = userDataCookie.split('=')[1]

  const userSigCookie = cookies.find((cookie) => cookie.startsWith('user-sig='))
  if (!userSigCookie) {
    throw new Error('user-sig cookie not found')
  }
  const userSig = userSigCookie.split('=')[1]

  return { userData, userSig }
}

module.exports = { login }
